import { NextResponse } from 'next/server';
import { generateEnhancedAIResponse } from '@/lib/ai-service';

// Enhanced subtask generation API endpoint
export async function POST(request: Request) {
  try {
    const data = await request.json();
    const { task, additionalInfo, projectContext } = data;
    
    if (!task) {
      return NextResponse.json(
        { error: 'Task is required' },
        { status: 400 }
      );
    }
    
    // Create context for AI analysis
    const context = {
      currentTask: task,
      tasks: projectContext?.tasks || [],
      additionalInfo: additionalInfo || ''
    };
    
    // Generate AI response for subtask creation
    const aiResponse = generateEnhancedAIResponse('разбить на подзадачи', context);
    
    // Extract subtasks from the AI response if available
    const subtasks = aiResponse.suggestions || generateDefaultSubtasks(task);
    
    return NextResponse.json({
      success: true,
      subtasks: subtasks,
      analysis: aiResponse.content,
      metadata: aiResponse.metadata
    });
    
  } catch (error) {
    console.error('Error generating subtasks:', error);
    return NextResponse.json(
      { error: 'Failed to generate subtasks' },
      { status: 500 }
    );
  }
}

// Fallback subtask generation
function generateDefaultSubtasks(task: any) {
  const baseSubtasks = [
    {
      id: `subtask-${Date.now()}-1`,
      title: 'Планирование и анализ требований',
      description: 'Детальный анализ требований и создание плана выполнения',
      completed: false,
      estimatedHours: 2,
      priority: 'high',
      dueDate: calculateDueDate(task.dueDate, -7)
    },
    {
      id: `subtask-${Date.now()}-2`,
      title: 'Основная реализация',
      description: 'Выполнение основной части работы согласно плану',
      completed: false,
      estimatedHours: 6,
      priority: 'high',
      dueDate: calculateDueDate(task.dueDate, -3)
    },
    {
      id: `subtask-${Date.now()}-3`,
      title: 'Тестирование и проверка качества',
      description: 'Проведение тестирования и контроль качества результатов',
      completed: false,
      estimatedHours: 2,
      priority: 'medium',
      dueDate: calculateDueDate(task.dueDate, -1)
    },
    {
      id: `subtask-${Date.now()}-4`,
      title: 'Финализация и документирование',
      description: 'Завершающие работы и создание документации',
      completed: false,
      estimatedHours: 1,
      priority: 'low',
      dueDate: task.dueDate
    }
  ];
  
  return baseSubtasks;
}

// Helper function to calculate due dates
function calculateDueDate(parentDueDate: string | undefined, daysBefore: number): string | undefined {
  if (!parentDueDate) return undefined;
  
  try {
    const parentDue = new Date(parentDueDate);
    const subtaskDue = new Date(parentDue);
    subtaskDue.setDate(subtaskDue.getDate() + daysBefore);
    return subtaskDue.toISOString().split('T')[0];
  } catch {
    return undefined;
  }
}
