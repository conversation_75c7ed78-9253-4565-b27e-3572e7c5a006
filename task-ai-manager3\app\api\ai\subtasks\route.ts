import { NextResponse } from 'next/server';
import { streamText } from 'ai';
import { getAIConfig, createOpenAIClientSync } from '@/lib/ai-config';

// Enhanced subtask generation API endpoint with real AI integration
export async function POST(request: Request) {
  try {
    const data = await request.json();
    const { task, additionalInfo, projectContext } = data;

    if (!task) {
      return NextResponse.json(
        { error: 'Task is required' },
        { status: 400 }
      );
    }

    // Generate AI-powered subtasks
    const result = await generateAISubtasks(task, additionalInfo, projectContext);

    return NextResponse.json({
      success: true,
      subtasks: result.subtasks,
      analysis: result.analysis,
      metadata: result.metadata
    });

  } catch (error) {
    console.error('Error generating subtasks:', error);
    return NextResponse.json(
      { error: 'Failed to generate subtasks' },
      { status: 500 }
    );
  }
}

// Generate AI-powered subtasks
async function generateAISubtasks(task: any, additionalInfo: string, projectContext: any) {
  try {
    const config = await getAIConfig();

    // Check if AI is enabled
    if (!config.enabled) {
      return generateFallbackSubtasks(task, additionalInfo);
    }

    // Detect language
    const isRussian = /[а-яА-Я]/.test(task.title + ' ' + (task.description || '') + ' ' + additionalInfo);

    // Create specialized system prompt for subtask generation
    const systemPrompt = getSubtaskGenerationPrompt(isRussian);

    // Create OpenAI client
    const openai = createOpenAIClientSync();

    // Prepare the prompt
    const userPrompt = createSubtaskPrompt(task, additionalInfo, projectContext, isRussian);

    // Generate AI response
    const result = await streamText({
      model: openai,
      system: systemPrompt,
      messages: [
        {
          role: 'user',
          content: userPrompt
        }
      ],
      temperature: config.temperature,
      maxTokens: config.maxTokens,
    });

    // Convert stream to text
    let fullResponse = '';
    for await (const chunk of result.textStream) {
      fullResponse += chunk;
    }

    // Parse the AI response to extract subtasks
    const parsed = parseAISubtaskResponse(fullResponse, isRussian);

    return {
      subtasks: parsed.subtasks,
      analysis: parsed.analysis,
      metadata: {
        taskId: task.id,
        generatedAt: new Date().toISOString(),
        aiModel: config.model,
        language: isRussian ? 'ru' : 'en'
      }
    };

  } catch (error) {
    console.error('Error in AI subtask generation:', error);
    return generateFallbackSubtasks(task, additionalInfo);
  }
}

// Get specialized system prompt for subtask generation
function getSubtaskGenerationPrompt(isRussian: boolean): string {
  return isRussian
    ? `Ты - экспертный ИИ-ассистент по декомпозиции задач. Твоя задача - разбивать сложные задачи на логичные, выполнимые подзадачи.

🎯 **Принципы декомпозиции**:
- Каждая подзадача должна быть конкретной и измеримой
- Подзадачи должны быть логически связаны и последовательны
- Учитывай зависимости между подзадачами
- Предлагай реалистичные временные рамки

📋 **Формат ответа**:
1. Краткий анализ задачи (2-3 предложения)
2. Список подзадач в формате:
   - **Название подзадачи**: Описание (Приоритет: Высокий/Средний/Низкий)

Отвечай структурированно и профессионально.`
    : `You are an expert AI assistant specializing in task decomposition. Your job is to break down complex tasks into logical, actionable subtasks.

🎯 **Decomposition Principles**:
- Each subtask should be specific and measurable
- Subtasks should be logically connected and sequential
- Consider dependencies between subtasks
- Suggest realistic timeframes

📋 **Response Format**:
1. Brief task analysis (2-3 sentences)
2. List of subtasks in format:
   - **Subtask Name**: Description (Priority: High/Medium/Low)

Respond in a structured and professional manner.`;
}

// Create subtask generation prompt
function createSubtaskPrompt(task: any, additionalInfo: string, projectContext: any, isRussian: boolean): string {
  const lang = isRussian ? 'ru' : 'en';

  let prompt = '';

  if (lang === 'ru') {
    prompt = `Разбей следующую задачу на подзадачи:

📋 **Основная задача**: ${task.title || task.name}
📝 **Описание**: ${task.description || 'Описание не предоставлено'}`;

    if (additionalInfo) {
      prompt += `\n💡 **Дополнительная информация**: ${additionalInfo}`;
    }

    if (projectContext?.tasks?.length > 0) {
      prompt += `\n\n🔗 **Контекст проекта** (связанные задачи):`;
      projectContext.tasks.slice(0, 3).forEach((t: any, i: number) => {
        prompt += `\n${i + 1}. ${t.title || t.name}`;
      });
    }

    prompt += `\n\nПожалуйста, проанализируй задачу и предложи 3-7 логичных подзадач с описаниями и приоритетами.`;
  } else {
    prompt = `Break down the following task into subtasks:

📋 **Main Task**: ${task.title || task.name}
📝 **Description**: ${task.description || 'No description provided'}`;

    if (additionalInfo) {
      prompt += `\n💡 **Additional Information**: ${additionalInfo}`;
    }

    if (projectContext?.tasks?.length > 0) {
      prompt += `\n\n🔗 **Project Context** (related tasks):`;
      projectContext.tasks.slice(0, 3).forEach((t: any, i: number) => {
        prompt += `\n${i + 1}. ${t.title || t.name}`;
      });
    }

    prompt += `\n\nPlease analyze the task and suggest 3-7 logical subtasks with descriptions and priorities.`;
  }

  return prompt;
}

// Parse AI response to extract subtasks
function parseAISubtaskResponse(response: string, isRussian: boolean) {
  const lines = response.split('\n').filter(line => line.trim());
  const subtasks: any[] = [];
  let analysis = '';
  let inSubtaskSection = false;

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Check if we're entering the subtask section
    if (trimmedLine.match(/^\d+\./)) {
      inSubtaskSection = true;
    }

    if (inSubtaskSection && trimmedLine.match(/^[\d-•*]\s*\*?\*?(.+)/)) {
      // Extract subtask information
      const match = trimmedLine.match(/^[\d-•*]\s*\*?\*?(.+?)(?:\s*\(.*?\))?$/);
      if (match) {
        const fullText = match[1].trim();
        const [title, ...descParts] = fullText.split(':');
        const description = descParts.join(':').trim();

        // Extract priority
        let priority = 'Medium';
        const priorityMatch = line.match(/(Высокий|Средний|Низкий|High|Medium|Low)/i);
        if (priorityMatch) {
          const p = priorityMatch[1].toLowerCase();
          if (p.includes('высок') || p === 'high') priority = 'High';
          else if (p.includes('низк') || p === 'low') priority = 'Low';
          else priority = 'Medium';
        }

        subtasks.push({
          id: `subtask-${Date.now()}-${subtasks.length}`,
          title: title.replace(/^\*\*|\*\*$/g, '').trim(),
          description: description || title.replace(/^\*\*|\*\*$/g, '').trim(),
          priority: priority,
          status: 'pending',
          completed: false
        });
      }
    } else if (!inSubtaskSection) {
      // Collect analysis text before subtasks
      analysis += trimmedLine + ' ';
    }
  }

  // If no subtasks were parsed, generate fallback
  if (subtasks.length === 0) {
    return generateFallbackSubtasks({ title: 'Unknown Task' }, '');
  }

  return {
    subtasks,
    analysis: analysis.trim() || (isRussian ? 'Анализ задачи выполнен.' : 'Task analysis completed.')
  };
}

// Fallback subtask generation
function generateFallbackSubtasks(task: any, additionalInfo: string): any {
  const subtasks = generateDefaultSubtasks(task);
  return {
    subtasks,
    analysis: `Fallback analysis for task: ${task.title || task.name}. ${additionalInfo ? 'Additional info: ' + additionalInfo : ''}`,
    metadata: {
      taskId: task.id,
      generatedAt: new Date().toISOString(),
      fallback: true
    }
  };
}

// Default subtask generation
function generateDefaultSubtasks(task: any) {
  const baseSubtasks = [
    {
      id: `subtask-${Date.now()}-1`,
      title: 'Планирование и анализ требований',
      description: 'Детальный анализ требований и создание плана выполнения',
      completed: false,
      estimatedHours: 2,
      priority: 'high',
      dueDate: calculateDueDate(task.dueDate, -7)
    },
    {
      id: `subtask-${Date.now()}-2`,
      title: 'Основная реализация',
      description: 'Выполнение основной части работы согласно плану',
      completed: false,
      estimatedHours: 6,
      priority: 'high',
      dueDate: calculateDueDate(task.dueDate, -3)
    },
    {
      id: `subtask-${Date.now()}-3`,
      title: 'Тестирование и проверка качества',
      description: 'Проведение тестирования и контроль качества результатов',
      completed: false,
      estimatedHours: 2,
      priority: 'medium',
      dueDate: calculateDueDate(task.dueDate, -1)
    },
    {
      id: `subtask-${Date.now()}-4`,
      title: 'Финализация и документирование',
      description: 'Завершающие работы и создание документации',
      completed: false,
      estimatedHours: 1,
      priority: 'low',
      dueDate: task.dueDate
    }
  ];
  
  return baseSubtasks;
}

// Helper function to calculate due dates
function calculateDueDate(parentDueDate: string | undefined, daysBefore: number): string | undefined {
  if (!parentDueDate) return undefined;
  
  try {
    const parentDue = new Date(parentDueDate);
    const subtaskDue = new Date(parentDue);
    subtaskDue.setDate(subtaskDue.getDate() + daysBefore);
    return subtaskDue.toISOString().split('T')[0];
  } catch {
    return undefined;
  }
}
