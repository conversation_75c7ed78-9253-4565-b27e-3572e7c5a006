"use client"

import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Badge } from "@/components/ui/badge"
import { Loader2, RefreshCw, Sparkles, Plus, Check, X } from "lucide-react"
import { useTranslation } from "@/lib/translations"

interface AITaskGeneratorProps {
  projectId: string
  projectName: string
  projectDescription: string
  onAddTasks: (tasks: any[]) => void
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AITaskGeneratorSimple({
  projectId,
  projectName,
  projectDescription,
  onAddTasks,
  open,
  onOpenChange
}: AITaskGeneratorProps) {
  const { t } = useTranslation()
  const { toast } = useToast()

  // Состояния
  const [isLoading, setIsLoading] = useState(false)
  const [additionalInfo, setAdditionalInfo] = useState("")
  const [generatedTasks, setGeneratedTasks] = useState<any[]>([])
  const [selectedTasks, setSelectedTasks] = useState<string[]>([])
  const [regenerateCount, setRegenerateCount] = useState(0)

  // Функция для генерации задач с помощью ИИ
  const generateTasks = async () => {
    setIsLoading(true)
    console.log("Generating tasks for project:", projectName, projectId)

    try {
      // Имитация запроса к API ИИ
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Пример сгенерированных задач (в реальном приложении здесь будет запрос к API ИИ)
      const timestamp = Date.now();
      const aiTasks = [
        {
          id: `ai-task-${timestamp}-1`,
          title: `Анализ требований для ${projectName}`,
          description: "Провести анализ требований заказчика, определить ключевые функциональные и нефункциональные требования.",
          priority: "high",
          estimatedHours: 8,
          tags: ["анализ", "планирование"],
          status: "todo",
          projectId: projectId
        },
        {
          id: `ai-task-${timestamp}-2`,
          title: "Создание прототипа интерфейса",
          description: "Разработать прототип пользовательского интерфейса с основными экранами и элементами взаимодействия.",
          priority: "medium",
          estimatedHours: 16,
          tags: ["дизайн", "UI/UX"],
          status: "todo",
          projectId: projectId
        },
        {
          id: `ai-task-${timestamp}-3`,
          title: "Разработка архитектуры",
          description: "Спроектировать архитектуру приложения, определить компоненты и их взаимодействие.",
          priority: "high",
          estimatedHours: 12,
          tags: ["архитектура", "планирование"],
          status: "todo",
          projectId: projectId
        },
        {
          id: `ai-task-${timestamp}-4`,
          title: "Настройка инфраструктуры разработки",
          description: "Настроить среду разработки, CI/CD, тестовые окружения.",
          priority: "medium",
          estimatedHours: 8,
          tags: ["DevOps", "инфраструктура"],
          status: "todo",
          projectId: projectId
        },
        {
          id: `ai-task-${timestamp}-5`,
          title: "Разработка базы данных",
          description: "Спроектировать схему базы данных, создать миграции, настроить индексы.",
          priority: "high",
          estimatedHours: 10,
          tags: ["база данных", "backend"],
          status: "todo",
          projectId: projectId
        }
      ]

      setGeneratedTasks(aiTasks)
      // Автоматически выбираем все задачи
      setSelectedTasks(aiTasks.map(task => task.id))
    } catch (error) {
      console.error("Error generating tasks:", error)
      toast({
        title: "Ошибка генерации задач",
        description: "Не удалось сгенерировать задачи. Пожалуйста, попробуйте еще раз.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Функция для перегенерации задач
  const regenerateTasks = () => {
    setRegenerateCount(prev => prev + 1)
    generateTasks()
  }

  // Функция для добавления выбранных задач в проект
  const addSelectedTasks = () => {
    try {
      console.log("Adding selected tasks to project:", projectId);
      const tasksToAdd = generatedTasks.filter(task => selectedTasks.includes(task.id))
      console.log("Selected tasks:", tasksToAdd.length);

      if (tasksToAdd.length === 0) {
        toast({
          title: "Выберите задачи",
          description: "Пожалуйста, выберите хотя бы одну задачу для добавления.",
          variant: "default"
        })
        return
      }

      // Задачи уже содержат необходимые поля, но добавим уникальные ID
      const timestamp = Date.now();
      const formattedTasks = tasksToAdd.map((task, index) => ({
        ...task,
        id: `task-${timestamp}-${index}-${Math.random().toString(36).substr(2, 5)}`,
      }))

      console.log("Formatted tasks ready to add:", formattedTasks);

      // Вызываем функцию добавления задач в родительском компоненте
      if (typeof onAddTasks === 'function') {
        onAddTasks(formattedTasks);

        // Отображаем уведомление об успешном добавлении задач
        toast({
          title: "Задачи добавлены",
          description: `${formattedTasks.length} задач успешно добавлено в проект.`,
          variant: "default"
        });

        // Закрываем диалог с небольшой задержкой, чтобы пользователь увидел уведомление
        setTimeout(() => {
          onOpenChange(false);
        }, 500);
      } else {
        // Если функция не передана, показываем ошибку
        console.error("onAddTasks function is not provided");
        toast({
          title: "Ошибка",
          description: "Не удалось добавить задачи в проект.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error adding selected tasks:", error);
      toast({
        title: "Ошибка",
        description: "Произошла ошибка при добавлении задач.",
        variant: "destructive"
      });
    }
  }

  // Обработчик выбора/отмены выбора задачи
  const toggleTaskSelection = (taskId: string) => {
    setSelectedTasks(prev =>
      prev.includes(taskId)
        ? prev.filter(id => id !== taskId)
        : [...prev, taskId]
    )
  }

  // Обработчик выбора всех задач
  const selectAllTasks = () => {
    if (selectedTasks.length === generatedTasks.length) {
      setSelectedTasks([]) // Если все выбраны, снимаем выбор со всех
    } else {
      setSelectedTasks(generatedTasks.map(task => task.id)) // Иначе выбираем все
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Sparkles className="h-5 w-5 mr-2 text-primary" />
            {t('aiGenerateTasks') || 'Генерация задач с помощью ИИ'}
          </DialogTitle>
          <DialogDescription>
            {t('aiGenerateTasksDescription') || 'Используйте ИИ для генерации задач на основе информации о проекте'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Информация о проекте */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">{t('projectInfo') || 'Информация о проекте'}</h3>
            <div className="grid gap-2">
              <div>
                <Label>{t('projectName') || 'Название проекта'}</Label>
                <div className="p-2 border rounded-md bg-muted/50">{projectName}</div>
              </div>
              <div>
                <Label>{t('projectDescription') || 'Описание проекта'}</Label>
                <div className="p-2 border rounded-md bg-muted/50 min-h-[60px]">{projectDescription || t('noDescription') || 'Нет описания'}</div>
              </div>
            </div>
          </div>

          {/* Дополнительная информация */}
          <div className="space-y-2">
            <Label htmlFor="additionalInfo">{t('additionalInfo') || 'Дополнительная информация (необязательно)'}</Label>
            <Textarea
              id="additionalInfo"
              placeholder={t('additionalInfoPlaceholder') || 'Укажите дополнительные требования или пожелания для генерации задач...'}
              value={additionalInfo}
              onChange={(e) => setAdditionalInfo(e.target.value)}
              className="min-h-[100px]"
            />
          </div>

          {/* Кнопка генерации */}
          <div className="flex justify-center">
            <Button
              onClick={generateTasks}
              className="w-full sm:w-auto"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('generating') || 'Генерация...'}
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  {t('generateTasks') || 'Сгенерировать задачи'}
                </>
              )}
            </Button>
          </div>

          {/* Сгенерированные задачи */}
          {generatedTasks.length > 0 && (
            <div className="space-y-4 mt-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">{t('generatedTasks') || 'Сгенерированные задачи'}</h3>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={selectAllTasks}
                  >
                    {selectedTasks.length === generatedTasks.length ? (
                      <>
                        <X className="mr-2 h-4 w-4" />
                        {t('deselectAll') || 'Снять выбор'}
                      </>
                    ) : (
                      <>
                        <Check className="mr-2 h-4 w-4" />
                        {t('selectAll') || 'Выбрать все'}
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={regenerateTasks}
                    disabled={isLoading}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    {t('regenerate') || 'Перегенерировать'}
                  </Button>
                </div>
              </div>

              <div className="grid gap-4">
                {generatedTasks.map((task) => (
                  <Card key={task.id} className={`overflow-hidden transition-all ${selectedTasks.includes(task.id) ? 'border-primary' : ''}`}>
                    <CardHeader className="p-4 pb-2">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-2">
                          <Checkbox
                            checked={selectedTasks.includes(task.id)}
                            onCheckedChange={() => toggleTaskSelection(task.id)}
                            className="mt-1"
                          />
                          <CardTitle className="text-base">{task.title}</CardTitle>
                        </div>
                        <Badge variant={task.priority === 'high' ? 'destructive' : task.priority === 'medium' ? 'default' : 'outline'}>
                          {task.priority === 'high' ? t('highPriority') || 'Высокий' :
                            task.priority === 'medium' ? t('mediumPriority') || 'Средний' :
                              t('lowPriority') || 'Низкий'}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="p-4 pt-2">
                      <p className="text-sm text-muted-foreground mb-2">{task.description}</p>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {task.tags && task.tags.map((tag: string) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      {task.estimatedHours && (
                        <div className="mt-2 text-xs text-muted-foreground">
                          {t('estimatedHours') || 'Оценка времени'}: {task.estimatedHours} {t('hours') || 'часов'}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between sm:justify-end">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            {t('cancel') || 'Отмена'}
          </Button>
          {generatedTasks.length > 0 && (
            <Button
              onClick={addSelectedTasks}
              disabled={selectedTasks.length === 0}
            >
              <Plus className="mr-2 h-4 w-4" />
              {t('addSelectedTasks') || 'Добавить выбранные задачи'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
