# Reminder and Notification System Documentation

## Overview

The reminder and notification system provides comprehensive deadline management and notification delivery for tasks, projects, and calendar events. It supports both email and Telegram notifications with customizable timing and user preferences.

## Features

### 1. Email Reminders
- **Deadline Notifications**: 1 day, 3 days, 1 week before due dates (customizable)
- **Project Milestone Reminders**: Important project deadlines and milestones
- **Calendar Event Reminders**: Meeting and event notifications
- **Overdue Notifications**: Alerts for overdue tasks and projects
- **Customizable Timing**: Users can set their preferred reminder intervals

### 2. Telegram Integration
- **Bot Integration**: Seamless Telegram bot for instant notifications
- **Same Reminder Types**: All email reminder types available via Telegram
- **Chat ID Validation**: Automatic validation of Telegram chat IDs
- **Test Functionality**: Built-in test feature to verify Telegram connectivity

### 3. User Preference Management
- **Granular Controls**: Separate toggles for email and Telegram notifications
- **Timing Customization**: Custom reminder intervals (minutes, hours, days)
- **Notification Types**: Enable/disable specific reminder categories
- **Quiet Hours**: Set times when notifications should be delayed
- **Timezone Support**: Respect user's timezone for accurate timing

### 4. Background Scheduling
- **Automated Processing**: Background job scheduling using intervals
- **Queue Management**: Reliable reminder queue with retry mechanisms
- **Error Handling**: Robust error handling with exponential backoff
- **Rate Limiting**: Built-in rate limiting to prevent spam

## Architecture

### Core Components

#### 1. Enhanced Reminder Service (`lib/enhanced-reminder-service.ts`)
- **Main Service**: Core reminder logic and queue management
- **User Preferences**: Preference storage and retrieval
- **Reminder Creation**: Intelligent reminder generation based on item type
- **Notification History**: Tracking of sent notifications for debugging

#### 2. Reminder Scheduler (`lib/reminder-scheduler.ts`)
- **Background Jobs**: Interval-based processing of reminder queue
- **Cleanup Tasks**: Automatic cleanup of old reminders and history
- **Overdue Checking**: Regular checks for overdue items
- **Sync Operations**: Periodic synchronization of all reminders

#### 3. User Interface Components
- **Notification Preferences** (`components/settings/notification-preferences.tsx`)
- **Notification History** (`components/settings/notification-history.tsx`)
- **User Settings Page** (`components/settings/user-settings-page.tsx`)

#### 4. Integration Hooks (`hooks/use-reminder-integration.ts`)
- **Automatic Scheduling**: Auto-schedule reminders when items are created/updated
- **Preference Sync**: Monitor and respond to preference changes
- **Bulk Operations**: Bulk scheduling for existing items

### Data Structures

#### User Reminder Preferences
```typescript
interface UserReminderPreferences {
  userId: string;
  emailReminders: boolean;
  telegramReminders: boolean;
  emailForReminders: string;
  telegramChatId: string;
  reminderTimes: number[]; // minutes before due date
  timezone: string;
  taskReminders: boolean;
  projectReminders: boolean;
  calendarReminders: boolean;
  overdueReminders: boolean;
  reminderFrequency: 'once' | 'daily' | 'hourly';
  quietHours: {
    enabled: boolean;
    start: string; // "22:00"
    end: string;   // "08:00"
  };
}
```

#### Enhanced Reminder
```typescript
interface EnhancedReminder {
  id: string;
  userId: string;
  itemType: 'event' | 'task' | 'subtask' | 'project';
  itemId: string;
  reminderTime: Date;
  sent: boolean;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  updatedAt: Date;
  sentAt?: Date;
  error?: string;
  reminderType: 'deadline' | 'overdue' | 'milestone' | 'calendar';
  priority: 'low' | 'medium' | 'high';
}
```

## API Endpoints

### GET `/api/reminders`

**Query Parameters:**
- `action`: `status` | `preferences` | `history` | `check-queue` | `check-all` | `manual-check`
- `userId`: Required for `preferences` and `history` actions
- `limit`: Number of history entries to return (default: 50)

**Examples:**
```javascript
// Get scheduler status
GET /api/reminders?action=status

// Get user preferences
GET /api/reminders?action=preferences&userId=123

// Get notification history
GET /api/reminders?action=history&userId=123&limit=25

// Manually process reminder queue
GET /api/reminders?action=check-queue
```

### POST `/api/reminders`

**Actions:**
- `create-reminder`: Create reminders for a specific item
- `save-preferences`: Save user reminder preferences
- `start-scheduler`: Start the reminder scheduler
- `stop-scheduler`: Stop the reminder scheduler
- `calendar-reminders`: Process calendar reminders (legacy)

**Examples:**
```javascript
// Create reminder for a task
POST /api/reminders
{
  "action": "create-reminder",
  "userId": "123",
  "itemType": "task",
  "itemId": "task-456",
  "dueDate": "2025-04-15T10:00:00Z",
  "priority": "high"
}

// Save user preferences
POST /api/reminders
{
  "action": "save-preferences",
  "preferences": {
    "userId": "123",
    "emailReminders": true,
    "telegramReminders": true,
    "emailForReminders": "<EMAIL>",
    "telegramChatId": "123456789",
    "reminderTimes": [1440, 60, 15]
  }
}
```

## Usage Examples

### 1. Setting Up User Preferences

```typescript
import { NotificationPreferences } from '@/components/settings/notification-preferences';

function UserSettings({ user }) {
  return (
    <NotificationPreferences 
      userId={user.id} 
      userEmail={user.email}
    />
  );
}
```

### 2. Integrating with Task Management

```typescript
import { useReminderIntegration } from '@/hooks/use-reminder-integration';

function TaskManager() {
  const { scheduleTaskReminders, handleTaskUpdate } = useReminderIntegration();
  
  const createTask = async (taskData) => {
    const task = await saveTask(taskData);
    
    // Automatically schedule reminders
    if (task.dueDate && task.assignee) {
      await scheduleTaskReminders(task);
    }
    
    return task;
  };
  
  const updateTask = async (oldTask, newTaskData) => {
    const newTask = await saveTask(newTaskData);
    
    // Handle reminder rescheduling
    await handleTaskUpdate(oldTask, newTask);
    
    return newTask;
  };
}
```

### 3. Manual Reminder Operations

```typescript
import { 
  processReminderQueue, 
  createRemindersForItem,
  getUserReminderPreferences 
} from '@/lib/enhanced-reminder-service';

// Process pending reminders
await processReminderQueue();

// Create reminders for a specific item
await createRemindersForItem(
  'user-123',
  'task',
  'task-456',
  new Date('2025-04-15T10:00:00Z'),
  'high'
);

// Get user preferences
const preferences = getUserReminderPreferences('user-123');
```

## Configuration

### Environment Variables

```env
# Email Configuration (if using SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-bot-token
TELEGRAM_BOT_USERNAME=your-bot-username
```

### Default Settings

- **Reminder Times**: 1 day (1440 min), 1 hour (60 min), 15 minutes before due date
- **Max Retries**: 3 attempts for failed notifications
- **Queue Processing**: Every 1 minute
- **Cleanup**: Every 1 hour
- **Overdue Check**: Every 30 minutes
- **Reminder Sync**: Every 6 hours

## Testing

### Browser Console Tests

```javascript
// Load the test suite
// Run in browser console after loading the app

// Test all functionality
testReminderSystem();

// Test specific components
testUserPreferences('user-123');
testReminderCreation('user-123', taskData, projectData);
testAPIEndpoints('user-123');
testSchedulerFunctions();
testNotificationIntegration('user-123');
testReminderPerformance();
```

### API Testing

```bash
# Test reminder status
curl "http://localhost:3000/api/reminders?action=status"

# Test creating a reminder
curl -X POST "http://localhost:3000/api/reminders" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "create-reminder",
    "userId": "test-user",
    "itemType": "task",
    "itemId": "test-task",
    "dueDate": "2025-04-15T10:00:00Z",
    "priority": "high"
  }'
```

## Troubleshooting

### Common Issues

1. **Reminders Not Sending**
   - Check user preferences are enabled
   - Verify email/Telegram configuration
   - Check scheduler is running: `isReminderSchedulerRunning()`

2. **Telegram Test Failing**
   - Verify bot token is correct
   - Check chat ID format (numeric or @username)
   - Ensure user has started conversation with bot

3. **Email Delivery Issues**
   - Check SMTP configuration
   - Verify email address format
   - Check spam/junk folders

4. **Performance Issues**
   - Monitor queue size and processing time
   - Check for excessive retry attempts
   - Review cleanup frequency

### Debug Commands

```javascript
// Check scheduler status
console.log('Scheduler running:', isReminderSchedulerRunning());

// Manual queue processing
await processReminderQueue();

// Check notification history
const history = getNotificationHistory('user-id', 10);
console.log('Recent notifications:', history);

// Test preferences
const prefs = getUserReminderPreferences('user-id');
console.log('User preferences:', prefs);
```

## Future Enhancements

- **Push Notifications**: Browser push notifications
- **SMS Integration**: SMS reminder delivery
- **Advanced Scheduling**: More complex reminder patterns
- **Analytics**: Notification delivery analytics and insights
- **Team Notifications**: Team-wide reminder management
- **Integration APIs**: Third-party calendar and task management integrations
