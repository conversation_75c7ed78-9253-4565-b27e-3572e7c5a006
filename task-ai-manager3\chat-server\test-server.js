const express = require('express');
const http = require('http');
const { Server } = require("socket.io");

// Добавляем обработку ошибок
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

const app = express();
const server = http.createServer(app);

// Настройка CORS для разрешения запросов с вашего Next.js приложения
const io = new Server(server, {
  cors: {
    origin: "*", // Разрешить все источники
    methods: ["GET", "POST"]
  }
});

const PORT = 3003;

app.get('/', (req, res) => {
  res.send('Test chat server is running');
});

io.on('connection', (socket) => {
  console.log('a user connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('user disconnected:', socket.id);
  });
});

server.listen(PORT, () => {
  console.log(`Test chat server listening on *:${PORT}`);
});
