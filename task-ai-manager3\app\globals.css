@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 20% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 262.1 83.3% 57.8%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 263.4 70% 50.4%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Font settings */
.font-sans {
  font-family: var(--font-inter), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
}

.font-mono {
  font-family: var(--font-jetbrains), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
}

/* Custom Calendar Styles */
@layer components {
  .rbc-calendar {
    @apply border-none rounded-lg bg-card text-card-foreground;
  }

  /* Toolbar is custom, so no specific styles needed here unless overriding base */

  /* Header row (MON, TUE, etc.) */
  .rbc-header {
    @apply text-center text-xs font-medium uppercase tracking-wider text-muted-foreground border-b border-border p-2;
  }

  /* Month view day cells */
  .rbc-month-view .rbc-day-bg {
    @apply border-l border-border;
  }
  .rbc-month-view .rbc-day-bg:first-child {
    @apply border-l-0;
  }
  .rbc-month-view .rbc-row-content {
     @apply border-t border-border;
  }
   .rbc-month-view .rbc-row:first-child .rbc-row-content {
     @apply border-t-0;
   }


  /* Week/Day view time column */
  .rbc-time-gutter {
    @apply border-l-0; /* Remove left border if calendar has outer border */
  }
  .rbc-time-header-gutter {
     @apply border-b border-border;
  }
  .rbc-timeslot-group {
    @apply border-b border-border;
  }
  .rbc-time-slot {
    @apply text-xs text-muted-foreground;
  }

  /* Week/Day view day columns */
  .rbc-day-slot .rbc-time-slot {
     @apply border-t border-border;
  }
   .rbc-time-view .rbc-header {
     @apply border-b-0; /* Remove bottom border from day headers in time view */
   }
   .rbc-day-slot {
     @apply relative;
   }

  /* Today's date background */
  .rbc-day-bg.rbc-today {
    @apply bg-accent; /* Use accent color from theme */
    opacity: 0.5; /* Make it subtle */
  }
   .rbc-month-view .rbc-today .rbc-day-number {
     @apply font-bold text-primary; /* Highlight today's number in month view */
   }

  /* Event styling */
  .rbc-event {
    @apply p-1 text-xs rounded-sm shadow-none border-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
    /* Background, text color, border are handled by eventStyleGetter */
  }
  .rbc-event-label {
    /* Hide default time label if needed */
     @apply hidden;
  }
  .rbc-event-content {
    @apply block whitespace-nowrap overflow-hidden text-ellipsis;
  }

  /* Overdue event indicator (using style from eventStyleGetter) */
  /* .rbc-event.rbc-event-overdue { */
    /* border-left: 3px solid theme('colors.red.500'); */
  /* } */

  /* Popup for overlapping events */
  .rbc-overlay {
    @apply absolute z-50 p-2 mt-1 rounded-md shadow-lg bg-popover text-popover-foreground border border-border;
  }
  .rbc-overlay-header {
    @apply text-xs font-medium border-b border-border pb-1 mb-1;
  }

  /* Current time indicator */
  .rbc-current-time-indicator {
    @apply absolute left-0 right-0 h-0.5 bg-primary z-30; /* Use primary color */
  }
  .rbc-current-time-indicator::before {
     @apply content-[''] absolute -left-1 -top-[3px] w-2 h-2 rounded-full bg-primary;
  }

   /* Ensure background image doesn't interfere */
   .rbc-timeslot-group, .rbc-day-bg {
     background-color: transparent !important; /* Override potential inline styles */
   }
   .rbc-time-view, .rbc-month-view {
      background: linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url('/placeholder.jpg'); /* Example background */
      background-size: cover;
      background-position: center;
   }
   .dark .rbc-time-view, .dark .rbc-month-view {
      background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('/placeholder.jpg'); /* Darker overlay */
      background-size: cover;
      background-position: center;
   }

}
