"use server";

import { createReminder, sendReminder } from './reminder-service';

// Интерфейс для задачи
interface Task {
  id: string;
  title: string;
  description?: string;
  status: "todo" | "in-progress" | "done";
  priority: "low" | "medium" | "high";
  dueDate?: string | Date;
  projectId: string;
  assignee?: {
    id: string;
    name: string;
    avatar?: string;
  };
  subtasks?: Subtask[];
}

// Интерфейс для подзадачи
interface Subtask {
  id: string;
  title: string;
  completed: boolean;
  description?: string;
  dueDate?: string | Date;
  priority?: "low" | "medium" | "high";
  assignee?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

// Интерфейс для проекта
interface Project {
  id: string;
  name: string;
  description?: string;
  dueDate?: string | Date;
  participants?: {
    id: string;
    name: string;
    avatar?: string;
  }[];
}

// Функция для создания напоминания для задачи
export async function createTaskReminder(task: Task): Promise<boolean> {
  try {
    // Проверяем, что задача имеет срок выполнения и назначена пользователю
    if (!task.dueDate || !task.assignee) {
      console.log(`Task ${task.id} does not have due date or assignee`);
      return false;
    }
    
    // Преобразуем дату в объект Date, если она строка
    const dueDate = typeof task.dueDate === 'string' ? new Date(task.dueDate) : task.dueDate;
    
    // Создаем время напоминания (за 1 день до срока)
    const reminderTime = new Date(dueDate);
    reminderTime.setDate(reminderTime.getDate() - 1);
    
    // Если время напоминания уже прошло, не создаем напоминание
    if (reminderTime < new Date()) {
      console.log(`Reminder time for task ${task.id} has already passed`);
      return false;
    }
    
    // Создаем напоминание
    const reminder = await createReminder(
      task.assignee.id,
      'task',
      task.id,
      reminderTime
    );
    
    return !!reminder;
  } catch (error) {
    console.error('Error creating task reminder:', error);
    return false;
  }
}

// Функция для создания напоминания для подзадачи
export async function createSubtaskReminder(subtask: Subtask, taskId: string): Promise<boolean> {
  try {
    // Проверяем, что подзадача имеет срок выполнения и назначена пользователю
    if (!subtask.dueDate || !subtask.assignee) {
      console.log(`Subtask ${subtask.id} does not have due date or assignee`);
      return false;
    }
    
    // Преобразуем дату в объект Date, если она строка
    const dueDate = typeof subtask.dueDate === 'string' ? new Date(subtask.dueDate) : subtask.dueDate;
    
    // Создаем время напоминания (за 1 день до срока)
    const reminderTime = new Date(dueDate);
    reminderTime.setDate(reminderTime.getDate() - 1);
    
    // Если время напоминания уже прошло, не создаем напоминание
    if (reminderTime < new Date()) {
      console.log(`Reminder time for subtask ${subtask.id} has already passed`);
      return false;
    }
    
    // Создаем напоминание
    const reminder = await createReminder(
      subtask.assignee.id,
      'subtask',
      subtask.id,
      reminderTime
    );
    
    return !!reminder;
  } catch (error) {
    console.error('Error creating subtask reminder:', error);
    return false;
  }
}

// Функция для создания напоминания для проекта
export async function createProjectReminder(project: Project): Promise<boolean[]> {
  try {
    // Проверяем, что проект имеет срок выполнения и участников
    if (!project.dueDate || !project.participants || project.participants.length === 0) {
      console.log(`Project ${project.id} does not have due date or participants`);
      return [false];
    }
    
    // Преобразуем дату в объект Date, если она строка
    const dueDate = typeof project.dueDate === 'string' ? new Date(project.dueDate) : project.dueDate;
    
    // Создаем время напоминания (за 3 дня до срока)
    const reminderTime = new Date(dueDate);
    reminderTime.setDate(reminderTime.getDate() - 3);
    
    // Если время напоминания уже прошло, не создаем напоминание
    if (reminderTime < new Date()) {
      console.log(`Reminder time for project ${project.id} has already passed`);
      return [false];
    }
    
    // Создаем напоминания для каждого участника проекта
    const results = await Promise.all(
      project.participants.map(participant => 
        createReminder(
          participant.id,
          'project',
          project.id,
          reminderTime
        )
      )
    );
    
    return results.map(result => !!result);
  } catch (error) {
    console.error('Error creating project reminder:', error);
    return [false];
  }
}

// Функция для обработки напоминаний для всех задач
export async function processTaskReminders(tasks: Task[]): Promise<void> {
  try {
    // Фильтруем задачи, для которых нужно создать напоминания
    const tasksWithDueDates = tasks.filter(task => 
      task.dueDate && 
      task.status !== 'done' && 
      task.assignee
    );
    
    console.log(`Found ${tasksWithDueDates.length} tasks with due dates`);
    
    // Создаем напоминания для каждой задачи
    for (const task of tasksWithDueDates) {
      await createTaskReminder(task);
      
      // Обрабатываем подзадачи
      if (task.subtasks && task.subtasks.length > 0) {
        const subtasksWithDueDates = task.subtasks.filter(subtask => 
          subtask.dueDate && 
          !subtask.completed && 
          subtask.assignee
        );
        
        console.log(`Found ${subtasksWithDueDates.length} subtasks with due dates for task ${task.id}`);
        
        for (const subtask of subtasksWithDueDates) {
          await createSubtaskReminder(subtask, task.id);
        }
      }
    }
  } catch (error) {
    console.error('Error processing task reminders:', error);
  }
}
