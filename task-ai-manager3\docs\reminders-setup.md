# Настройка системы напоминаний

В этом документе описывается, как настроить систему напоминаний для отправки уведомлений пользователям о предстоящих задачах, событиях и дедлайнах.

## Настройка SMTP-сервера

Для отправки напоминаний по электронной почте необходимо настроить SMTP-сервер:

1. Войдите в панель администратора
2. Перейдите в раздел "Настройки системы"
3. Откройте вкладку "Email"
4. Заполните следующие поля:
   - SMTP-сервер (например, smtp.gmail.com)
   - SMTP-порт (обычно 587 для TLS или 465 для SSL)
   - Имя пользователя SMTP
   - Пароль SMTP
   - Email отправителя (например, <EMAIL>)
5. Включите опцию "Включить уведомления по электронной почте"
6. Нажмите "Сохранить настройки"

## Настройка переменных окружения

Создайте или отредактируйте файл `.env` в корневой директории проекта и добавьте следующие переменные:

```
# SMTP Settings
SMTP_SERVER=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=your_username
SMTP_PASSWORD=your_password
FROM_EMAIL=<EMAIL>

# Telegram Bot Settings (если используется)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
```

## Настройка регулярной проверки напоминаний

### Для Linux/Unix (с использованием cron)

1. Откройте crontab для редактирования:
   ```
   crontab -e
   ```

2. Добавьте следующую строку для запуска проверки каждые 15 минут:
   ```
   */15 * * * * cd /path/to/task-ai-manager3 && node scripts/check-reminders.js >> logs/reminders.log 2>&1
   ```

3. Сохраните и закройте редактор

### Для Windows (с использованием Планировщика заданий)

1. Откройте "Планировщик заданий" (Task Scheduler)
2. Нажмите "Создать задачу..." (Create Task...)
3. На вкладке "Общие" (General) введите имя задачи, например "Check Reminders"
4. На вкладке "Триггеры" (Triggers) нажмите "Создать..." (New...) и настройте запуск каждые 15 минут
5. На вкладке "Действия" (Actions) нажмите "Создать..." (New...) и настройте:
   - Действие: Запустить программу (Start a program)
   - Программа/сценарий: node
   - Аргументы: scripts/check-reminders.js
   - Рабочая папка: C:\path\to\task-ai-manager3
6. Нажмите "ОК" для сохранения задачи

## Тестирование системы напоминаний

Для проверки работы системы напоминаний выполните следующие шаги:

1. Создайте тестовую задачу с дедлайном на ближайшее время
2. Убедитесь, что у задачи есть назначенный пользователь с указанным email
3. Вручную запустите скрипт проверки напоминаний:
   ```
   node scripts/check-reminders.js
   ```
4. Проверьте логи на наличие ошибок
5. Проверьте почтовый ящик пользователя на наличие напоминания

## Устранение неполадок

### Напоминания не отправляются

1. Проверьте настройки SMTP-сервера
2. Убедитесь, что у пользователя указан email в профиле
3. Проверьте, что в настройках пользователя включена опция "Напоминания на почту"
4. Проверьте логи на наличие ошибок
5. Убедитесь, что скрипт проверки напоминаний запускается по расписанию

### Ошибки при отправке напоминаний

1. Проверьте подключение к SMTP-серверу
2. Убедитесь, что учетные данные SMTP-сервера указаны верно
3. Проверьте, что порт SMTP-сервера не блокируется брандмауэром
4. Проверьте, что у отправителя есть права на отправку писем через SMTP-сервер
