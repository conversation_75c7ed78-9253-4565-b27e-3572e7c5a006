"use server";

// Интерфейс для данных Telegram-уведомления
export interface TelegramNotificationData {
  chatId: string; // Telegram chat ID или username
  message: string;
  subject?: string;
  parseMode?: 'Markdown' | 'HTML';
  disableWebPagePreview?: boolean;
  disableNotification?: boolean;
}

// Интерфейс для данных напоминания в Telegram
export interface TelegramReminderData {
  userId: string;
  telegramChatId: string;
  userName: string;
  subject: string;
  message: string;
  itemType: 'event' | 'task' | 'subtask' | 'project';
  itemId: string;
  itemTitle: string;
  dueDate?: Date;
  projectName?: string;
}

// Получение настроек Telegram Bot из переменных окружения
function getTelegramSettings() {
  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  
  if (!botToken) {
    throw new Error('TELEGRAM_BOT_TOKEN is not configured');
  }
  
  return {
    botToken,
    apiUrl: `https://api.telegram.org/bot${botToken}`,
  };
}

// Функция для отправки сообщения через Telegram Bot API
export async function sendTelegramMessage(data: TelegramNotificationData): Promise<boolean> {
  try {
    const settings = getTelegramSettings();
    
    // Проверяем, что все необходимые данные указаны
    if (!data.chatId || !data.message) {
      console.error('Telegram chat ID and message are required');
      return false;
    }
    
    // Формируем тело запроса
    const requestBody = {
      chat_id: data.chatId,
      text: data.message,
      parse_mode: data.parseMode || 'Markdown',
      disable_web_page_preview: data.disableWebPagePreview || false,
      disable_notification: data.disableNotification || false,
    };
    
    // Отправляем запрос к Telegram Bot API
    const response = await fetch(`${settings.apiUrl}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('Telegram API error:', errorData);
      return false;
    }
    
    const responseData = await response.json();
    console.log(`Telegram message sent to ${data.chatId}: ${responseData.result.message_id}`);
    
    // Записываем информацию об отправленном сообщении в лог
    await logTelegramMessageSent(data);
    
    return true;
  } catch (error) {
    console.error('Error sending Telegram message:', error);
    return false;
  }
}

// Функция для отправки напоминания в Telegram
export async function sendTelegramReminder(reminderData: TelegramReminderData): Promise<boolean> {
  try {
    // Проверяем, что у пользователя есть Telegram chat ID
    if (!reminderData.telegramChatId) {
      console.error(`User ${reminderData.userId} does not have a Telegram chat ID`);
      return false;
    }
    
    // Формируем сообщение для Telegram
    const messageText = formatTelegramReminderMessage(reminderData);
    
    // Отправляем сообщение
    const result = await sendTelegramMessage({
      chatId: reminderData.telegramChatId,
      message: messageText,
      parseMode: 'Markdown',
    });
    
    return result;
  } catch (error) {
    console.error('Error sending Telegram reminder:', error);
    return false;
  }
}

// Функция для форматирования сообщения напоминания для Telegram
function formatTelegramReminderMessage(reminderData: TelegramReminderData): string {
  const { userName, subject, message, itemTitle, itemType, dueDate, projectName } = reminderData;
  
  let messageText = `🔔 *${subject}*\n\n`;
  messageText += `Привет, ${userName}!\n\n`;
  messageText += `${message}\n\n`;
  
  // Добавляем информацию о элементе
  const typeEmoji = getTypeEmoji(itemType);
  messageText += `${typeEmoji} *${itemTitle}*\n`;
  
  if (projectName) {
    messageText += `📁 Проект: ${projectName}\n`;
  }
  
  if (dueDate) {
    messageText += `📅 Срок: ${dueDate.toLocaleDateString('ru-RU')}\n`;
  }
  
  messageText += `\n_AI Task Tracker_`;
  
  return messageText;
}

// Функция для получения эмодзи по типу элемента
function getTypeEmoji(itemType: string): string {
  switch (itemType) {
    case 'task':
      return '✅';
    case 'subtask':
      return '📝';
    case 'project':
      return '📁';
    case 'event':
      return '📅';
    default:
      return '📌';
  }
}

// Функция для записи информации об отправленном сообщении в лог
async function logTelegramMessageSent(data: TelegramNotificationData): Promise<void> {
  try {
    // В реальном приложении здесь будет запись в базу данных
    console.log(`Telegram message sent to ${data.chatId}: ${data.message.substring(0, 50)}...`);
    
    // Пример записи в базу данных (закомментировано, так как модель не существует)
    /*
    await prisma.telegramLog.create({
      data: {
        chatId: data.chatId,
        message: data.message,
        sentAt: new Date(),
        success: true,
      },
    });
    */
  } catch (error) {
    console.error('Error logging Telegram message:', error);
  }
}

// Функция для получения информации о боте
export async function getTelegramBotInfo(): Promise<any> {
  try {
    const settings = getTelegramSettings();
    
    const response = await fetch(`${settings.apiUrl}/getMe`);
    
    if (!response.ok) {
      throw new Error(`Failed to get bot info: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.result;
  } catch (error) {
    console.error('Error getting Telegram bot info:', error);
    return null;
  }
}

// Функция для проверки валидности Telegram chat ID
export async function validateTelegramChatId(chatId: string): Promise<boolean> {
  try {
    const result = await sendTelegramMessage({
      chatId,
      message: '🤖 Тест соединения с AI Task Tracker успешен!',
      disableNotification: true,
    });
    
    return result;
  } catch (error) {
    console.error('Error validating Telegram chat ID:', error);
    return false;
  }
}
