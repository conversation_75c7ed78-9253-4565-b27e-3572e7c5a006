"use client"

import { useEffect, useState } from "react"
import { useTranslation } from "@/lib/translations"
import { AnalyticsSection } from "@/components/analytics-section"
import { getProjects } from "@/lib/local-storage"

export default function AnalyticsPage() {
  const { t } = useTranslation()
  const [projects, setProjects] = useState([])

  useEffect(() => {
    // Загружаем проекты при монтировании компонента
    const loadedProjects = getProjects() || []
    setProjects(loadedProjects)
  }, [])

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">{t("analytics")}</h1>
      </div>
      
      <AnalyticsSection projects={projects} />
    </div>
  )
}
