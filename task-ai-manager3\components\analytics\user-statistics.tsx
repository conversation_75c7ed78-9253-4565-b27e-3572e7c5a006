"use client"

import { useState } from "react"
import { useTranslation } from "@/lib/translations"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Chart<PERSON>ontainer, ChartTooltipContent } from "@/components/ui/chart"
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Legend,
  Tooltip as ChartTooltip,
  ResponsiveContainer,
} from "recharts"

// Определение типов
interface User {
  id: string;
  name: string;
}

interface TeamMember {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

interface Project {
  id: string;
  name: string;
  description?: string;
  isPrivate: boolean;
  owner: User;
  participants: TeamMember[];
  createdAt: string | Date;
  completion: number;
  dueDate?: string | Date | null;
  tasks?: {
    todo?: { id: string; title: string; }[];
    inProgress?: { id: string; title: string; }[];
    done?: { id: string; title: string; }[];
  };
}

interface UserStatisticsProps {
  projects: Project[];
}

export default function UserStatistics({ projects }: UserStatisticsProps) {
  const { t } = useTranslation()
  const [chartView, setChartView] = useState<string>("participation")

  // Получаем актуальные данные о пользователях
  const allParticipants = projects.flatMap(project => {
    // Проверяем, что у проекта есть участники и владелец
    if (!project.participants || !Array.isArray(project.participants) || !project.owner) {
      return [];
    }

    return project.participants.map(participant => ({
      id: participant.id,
      name: participant.name,
      projectId: project.id,
      projectName: project.name,
      isOwner: project.owner.id === participant.id
    }));
  });

  // Статистика по участию пользователей в проектах
  const userParticipationData = allParticipants.reduce((acc: Record<string, any>, participant) => {
    if (!acc[participant.id]) {
      acc[participant.id] = {
        name: participant.name,
        projectCount: 0,
        ownedProjects: 0,
        participatedProjects: 0
      };
    }

    acc[participant.id].projectCount += 1;

    if (participant.isOwner) {
      acc[participant.id].ownedProjects += 1;
    } else {
      acc[participant.id].participatedProjects += 1;
    }

    return acc;
  }, {});

  // Преобразуем данные для графиков
  const userParticipationChartData = Object.values(userParticipationData)
    .sort((a: any, b: any) => b.projectCount - a.projectCount)
    .slice(0, 10); // Берем топ-10 пользователей

  // Статистика по активности пользователей
  const userActivityData = allParticipants.reduce((acc: Record<string, any>, participant) => {
    // Проверяем, что участник имеет все необходимые поля
    if (!participant || !participant.id || !participant.projectId) return acc;

    const project = projects.find(p => p.id === participant.projectId);
    if (!project || !project.tasks) return acc;

    if (!acc[participant.id]) {
      acc[participant.id] = {
        name: participant.name,
        completedTasks: 0,
        inProgressTasks: 0,
        todoTasks: 0,
        totalTasks: 0
      };
    }

    // Подсчитываем задачи пользователя
    const completedTasks = (project.tasks?.done || []).length;
    const inProgressTasks = (project.tasks?.inProgress || []).length;
    const todoTasks = (project.tasks?.todo || []).length;

    acc[participant.id].completedTasks += completedTasks;
    acc[participant.id].inProgressTasks += inProgressTasks;
    acc[participant.id].todoTasks += todoTasks;
    acc[participant.id].totalTasks += completedTasks + inProgressTasks + todoTasks;

    return acc;
  }, {});

  // Преобразуем данные для графиков
  const userActivityChartData = Object.values(userActivityData)
    .sort((a: any, b: any) => b.totalTasks - a.totalTasks)
    .slice(0, 10); // Берем топ-10 пользователей

  // Статистика по совместным проектам
  const collaborationData = projects.reduce((acc: Record<string, any>, project) => {
    // Проверяем, что у проекта есть участники
    if (!project.participants || !Array.isArray(project.participants)) {
      // Если нет участников, считаем что это проект с 0 участниками
      const participantCount = 0;

      if (!acc[participantCount]) {
        acc[participantCount] = {
          participantCount,
          projectCount: 0,
          name: t("noParticipants")
        };
      }

      acc[participantCount].projectCount += 1;
      return acc;
    }

    const participantCount = project.participants.length;

    if (!acc[participantCount]) {
      acc[participantCount] = {
        participantCount,
        projectCount: 0,
        name: participantCount === 1
          ? t("singleUserProjects")
          : t("multiUserProjects", { count: participantCount })
      };
    }

    acc[participantCount].projectCount += 1;

    return acc;
  }, {});

  // Преобразуем данные для графиков
  const collaborationChartData = Object.values(collaborationData)
    .sort((a: any, b: any) => a.participantCount - b.participantCount);

  // COLORS
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8", "#82ca9d", "#ffc658", "#8dd1e1", "#a4de6c", "#d0ed57"];

  return (
    <div className="space-y-6">
      <Tabs value={chartView} onValueChange={setChartView}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="participation">{t("userParticipation")}</TabsTrigger>
          <TabsTrigger value="activity">{t("userActivity")}</TabsTrigger>
          <TabsTrigger value="collaboration">{t("projectCollaboration")}</TabsTrigger>
        </TabsList>

        {/* Вкладка участия пользователей */}
        <TabsContent value="participation" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>{t("topUsersByProjects")}</CardTitle>
                <CardDescription>{t("topUsersByProjectsDescription")}</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    projectCount: {
                      label: t("totalProjects"),
                      color: "hsl(var(--chart-1))",
                    },
                    ownedProjects: {
                      label: t("ownedProjects"),
                      color: "hsl(var(--chart-2))",
                    },
                    participatedProjects: {
                      label: t("participatedProjects"),
                      color: "hsl(var(--chart-3))",
                    },
                  }}
                  className="h-full"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={userParticipationChartData}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 50, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="name" width={100} />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Legend />
                      <Bar dataKey="ownedProjects" fill="#0088FE" stackId="a" />
                      <Bar dataKey="participatedProjects" fill="#00C49F" stackId="a" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t("projectOwnershipDistribution")}</CardTitle>
                <CardDescription>{t("projectOwnershipDistributionDescription")}</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    value: {
                      label: t("projects"),
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-full"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={userParticipationChartData.map((user: any) => ({
                          name: user.name,
                          value: user.ownedProjects,
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) =>
                          percent > 0.05 ? `${name}: ${(percent * 100).toFixed(0)}%` : ''
                        }
                      >
                        {userParticipationChartData.map((_: any, index: number) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Вкладка активности пользователей */}
        <TabsContent value="activity" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>{t("topUsersByTasks")}</CardTitle>
                <CardDescription>{t("topUsersByTasksDescription")}</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    completedTasks: {
                      label: t("completedTasks"),
                      color: "hsl(var(--chart-1))",
                    },
                    inProgressTasks: {
                      label: t("inProgressTasks"),
                      color: "hsl(var(--chart-2))",
                    },
                    todoTasks: {
                      label: t("todoTasks"),
                      color: "hsl(var(--chart-3))",
                    },
                  }}
                  className="h-full"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={userActivityChartData}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 50, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="name" width={100} />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Legend />
                      <Bar dataKey="completedTasks" fill="#00C49F" stackId="a" />
                      <Bar dataKey="inProgressTasks" fill="#FFBB28" stackId="a" />
                      <Bar dataKey="todoTasks" fill="#FF8042" stackId="a" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t("taskCompletionByUser")}</CardTitle>
                <CardDescription>{t("taskCompletionByUserDescription")}</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    value: {
                      label: t("completedTasks"),
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-full"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={userActivityChartData.map((user: any) => ({
                          name: user.name,
                          value: user.completedTasks,
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) =>
                          percent > 0.05 ? `${name}: ${(percent * 100).toFixed(0)}%` : ''
                        }
                      >
                        {userActivityChartData.map((_: any, index: number) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Вкладка совместных проектов */}
        <TabsContent value="collaboration" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>{t("projectsByTeamSize")}</CardTitle>
                <CardDescription>{t("projectsByTeamSizeDescription")}</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    projectCount: {
                      label: t("projects"),
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-full"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={collaborationChartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Legend />
                      <Bar dataKey="projectCount" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t("collaborationDistribution")}</CardTitle>
                <CardDescription>{t("collaborationDistributionDescription")}</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    value: {
                      label: t("projects"),
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-full"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={collaborationChartData.map((item: any) => ({
                          name: item.name,
                          value: item.projectCount,
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) =>
                          percent > 0.05 ? `${name}: ${(percent * 100).toFixed(0)}%` : ''
                        }
                      >
                        {collaborationChartData.map((_: any, index: number) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
