// Enhanced AI Service for Task Management
// Provides intelligent task analysis, subtask generation, and solution suggestions

interface Task {
  id: string;
  title: string;
  description: string;
  status: "todo" | "in-progress" | "done";
  priority: "low" | "medium" | "high";
  dueDate?: string;
  tags?: string[];
  projectId: string;
  assignee?: {
    id: string;
    name: string;
    avatar?: string;
  };
  subtasks?: Subtask[];
}

interface Subtask {
  id: string;
  title: string;
  completed: boolean;
  description?: string;
  dueDate?: string;
  priority?: "low" | "medium" | "high";
  estimatedHours?: number;
  assignee?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

interface AIResponse {
  type: 'analysis' | 'subtasks' | 'solutions' | 'planning' | 'optimization';
  content: string;
  suggestions?: any[];
  metadata?: any;
}

// Main AI Response Generator
export function generateEnhancedAIResponse(prompt: string, context: any): AIResponse {
  const promptLower = prompt.toLowerCase();
  const currentTask = context.currentTask;
  const allTasks = context.tasks || [];
  
  // Task Analysis
  if (promptLower.includes('анализ') || promptLower.includes('analysis') || promptLower.includes('analyze')) {
    return {
      type: 'analysis',
      content: generateTaskAnalysis(currentTask, allTasks),
      metadata: { taskId: currentTask?.id }
    };
  }
  
  // Subtask Generation
  if (promptLower.includes('подзадачи') || promptLower.includes('subtasks') || 
      promptLower.includes('break down') || promptLower.includes('разбить')) {
    const subtasks = generateIntelligentSubtasks(currentTask);
    return {
      type: 'subtasks',
      content: formatSubtaskResponse(currentTask, subtasks),
      suggestions: subtasks,
      metadata: { taskId: currentTask?.id }
    };
  }
  
  // Solution Suggestions
  if (promptLower.includes('решение') || promptLower.includes('solution') || 
      promptLower.includes('как') || promptLower.includes('how')) {
    return {
      type: 'solutions',
      content: generateSolutionSuggestions(currentTask),
      metadata: { taskId: currentTask?.id }
    };
  }
  
  // Planning and Strategy
  if (promptLower.includes('планирование') || promptLower.includes('planning') || 
      promptLower.includes('план') || promptLower.includes('plan')) {
    return {
      type: 'planning',
      content: generatePlanningAdvice(currentTask, allTasks),
      metadata: { taskId: currentTask?.id }
    };
  }
  
  // Optimization
  if (promptLower.includes('оптимизация') || promptLower.includes('optimization') || 
      promptLower.includes('улучшить') || promptLower.includes('improve')) {
    return {
      type: 'optimization',
      content: generateOptimizationSuggestions(currentTask, allTasks),
      metadata: { taskId: currentTask?.id }
    };
  }
  
  // Default comprehensive response
  return {
    type: 'analysis',
    content: generateGeneralAssistance(currentTask, prompt),
    metadata: { taskId: currentTask?.id }
  };
}

// Task Analysis Function
function generateTaskAnalysis(task: Task | null, allTasks: Task[]): string {
  if (!task) return "Пожалуйста, выберите задачу для анализа.";
  
  const complexity = analyzeTaskComplexity(task);
  const risks = identifyTaskRisks(task);
  const recommendations = generateExecutionRecommendations(task);
  
  return `🔍 **Детальный анализ задачи: "${task.title}"**

📊 **Текущее состояние:**
• Статус: ${getStatusText(task.status)}
• Приоритет: ${getPriorityText(task.priority)}
• Срок выполнения: ${task.dueDate ? formatDate(task.dueDate) : 'Не установлен'}
• Подзадачи: ${task.subtasks?.length || 0} шт.
• Теги: ${task.tags?.join(', ') || 'Не указаны'}

🎯 **Анализ сложности:** ${complexity}

📈 **Рекомендации по выполнению:**
${recommendations}

⚠️ **Потенциальные риски:**
${risks}

🚀 **Следующие шаги:**
${suggestNextSteps(task)}

📊 **Контекст проекта:**
• Всего задач в проекте: ${allTasks.length}
• Выполнено: ${allTasks.filter(t => t.status === 'done').length}
• В работе: ${allTasks.filter(t => t.status === 'in-progress').length}
• Ожидают: ${allTasks.filter(t => t.status === 'todo').length}`;
}

// Intelligent Subtask Generation
function generateIntelligentSubtasks(task: Task | null): Subtask[] {
  if (!task) return [];
  
  const baseSubtasks = getBaseSubtasksByCategory(task);
  const contextualSubtasks = generateContextualSubtasks(task);
  
  return [...baseSubtasks, ...contextualSubtasks].map((subtask, index) => ({
    id: `ai-subtask-${Date.now()}-${index}`,
    title: subtask.title,
    completed: false,
    description: subtask.description,
    estimatedHours: subtask.estimatedHours,
    priority: subtask.priority || task.priority,
    dueDate: calculateSubtaskDueDate(task.dueDate, index, subtask.estimatedHours)
  }));
}

// Get base subtasks by task category
function getBaseSubtasksByCategory(task: Task): any[] {
  const title = task.title.toLowerCase();
  const description = task.description?.toLowerCase() || '';
  
  // Development tasks
  if (title.includes('разработ') || title.includes('develop') || title.includes('код') || title.includes('code')) {
    return [
      { title: 'Анализ требований и техническое планирование', description: 'Детальный анализ требований и создание технического плана', estimatedHours: 3, priority: 'high' },
      { title: 'Проектирование архитектуры решения', description: 'Создание архитектурной схемы и выбор технологий', estimatedHours: 4, priority: 'high' },
      { title: 'Разработка основного функционала', description: 'Реализация ключевых функций и компонентов', estimatedHours: 8, priority: 'high' },
      { title: 'Написание тестов', description: 'Создание unit и integration тестов', estimatedHours: 3, priority: 'medium' },
      { title: 'Тестирование и отладка', description: 'Проведение тестирования и исправление ошибок', estimatedHours: 4, priority: 'medium' },
      { title: 'Документирование кода', description: 'Создание технической документации', estimatedHours: 2, priority: 'low' }
    ];
  }
  
  // Design tasks
  if (title.includes('дизайн') || title.includes('design') || title.includes('ui') || title.includes('ux')) {
    return [
      { title: 'Исследование пользователей и конкурентов', description: 'Анализ целевой аудитории и конкурентных решений', estimatedHours: 4, priority: 'high' },
      { title: 'Создание пользовательских историй', description: 'Определение пользовательских сценариев', estimatedHours: 2, priority: 'high' },
      { title: 'Разработка wireframes', description: 'Создание каркасов интерфейса', estimatedHours: 3, priority: 'high' },
      { title: 'Создание UI-дизайна', description: 'Разработка визуального дизайна интерфейса', estimatedHours: 6, priority: 'medium' },
      { title: 'Прототипирование', description: 'Создание интерактивного прототипа', estimatedHours: 4, priority: 'medium' },
      { title: 'Тестирование с пользователями', description: 'Проведение пользовательского тестирования', estimatedHours: 3, priority: 'low' }
    ];
  }
  
  // Research tasks
  if (title.includes('исследован') || title.includes('research') || title.includes('анализ') || title.includes('analysis')) {
    return [
      { title: 'Определение целей и гипотез исследования', description: 'Формулирование четких целей и проверяемых гипотез', estimatedHours: 2, priority: 'high' },
      { title: 'Сбор и анализ данных', description: 'Поиск, сбор и первичный анализ релевантных данных', estimatedHours: 6, priority: 'high' },
      { title: 'Проведение интервью/опросов', description: 'Сбор качественных данных от экспертов или пользователей', estimatedHours: 4, priority: 'medium' },
      { title: 'Анализ и интерпретация результатов', description: 'Глубокий анализ собранных данных и выводы', estimatedHours: 4, priority: 'high' },
      { title: 'Подготовка отчета с рекомендациями', description: 'Создание структурированного отчета с выводами', estimatedHours: 3, priority: 'medium' }
    ];
  }
  
  // Default generic subtasks
  return [
    { title: 'Планирование и подготовка', description: 'Детальное планирование задачи и подготовка ресурсов', estimatedHours: 2, priority: 'high' },
    { title: 'Основная реализация', description: 'Выполнение основной части работы', estimatedHours: 6, priority: 'high' },
    { title: 'Проверка и тестирование', description: 'Контроль качества и тестирование результатов', estimatedHours: 2, priority: 'medium' },
    { title: 'Документирование и завершение', description: 'Оформление результатов и закрытие задачи', estimatedHours: 1, priority: 'low' }
  ];
}

// Generate contextual subtasks based on task content
function generateContextualSubtasks(task: Task): any[] {
  const contextualTasks = [];
  
  // Add deadline-related subtasks if due date is close
  if (task.dueDate && isDeadlineClose(task.dueDate)) {
    contextualTasks.push({
      title: 'Приоритизация критических элементов',
      description: 'Определение минимально жизнеспособного результата из-за близкого дедлайна',
      estimatedHours: 1,
      priority: 'high'
    });
  }
  
  // Add collaboration subtasks if assignee exists
  if (task.assignee) {
    contextualTasks.push({
      title: 'Координация с командой',
      description: 'Синхронизация с участниками команды и обновление статуса',
      estimatedHours: 1,
      priority: 'medium'
    });
  }
  
  return contextualTasks;
}

// Helper functions
function getStatusText(status: string): string {
  const statusMap = {
    'todo': '📋 К выполнению',
    'in-progress': '🔄 В работе',
    'done': '✅ Выполнено'
  };
  return statusMap[status] || status;
}

function getPriorityText(priority: string): string {
  const priorityMap = {
    'low': '🟢 Низкий',
    'medium': '🟡 Средний',
    'high': '🔴 Высокий'
  };
  return priorityMap[priority] || priority;
}

function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU');
  } catch {
    return dateString;
  }
}

function analyzeTaskComplexity(task: Task): string {
  let complexity = 'Средняя';
  let factors = [];
  
  if (task.description && task.description.length > 200) {
    complexity = 'Высокая';
    factors.push('подробное описание');
  }
  
  if (task.subtasks && task.subtasks.length > 5) {
    complexity = 'Высокая';
    factors.push('множество подзадач');
  }
  
  if (task.priority === 'high') {
    factors.push('высокий приоритет');
  }
  
  return `${complexity}${factors.length > 0 ? ` (${factors.join(', ')})` : ''}`;
}

function identifyTaskRisks(task: Task): string {
  const risks = [];
  
  if (!task.dueDate) {
    risks.push('• Отсутствие четкого дедлайна может привести к затягиванию');
  }
  
  if (!task.assignee) {
    risks.push('• Не назначен ответственный исполнитель');
  }
  
  if (task.priority === 'high' && task.status === 'todo') {
    risks.push('• Высокоприоритетная задача еще не начата');
  }
  
  if (task.subtasks && task.subtasks.length === 0) {
    risks.push('• Отсутствие детализации может усложнить выполнение');
  }
  
  return risks.length > 0 ? risks.join('\n') : '• Критических рисков не выявлено';
}

function generateExecutionRecommendations(task: Task): string {
  const recommendations = [];
  
  if (task.status === 'todo') {
    recommendations.push('• Начните с детального планирования и разбиения на подзадачи');
    recommendations.push('• Определите необходимые ресурсы и инструменты');
  }
  
  if (task.priority === 'high') {
    recommendations.push('• Сосредоточьтесь на этой задаче в первую очередь');
    recommendations.push('• Регулярно отчитывайтесь о прогрессе');
  }
  
  if (!task.assignee) {
    recommendations.push('• Назначьте ответственного исполнителя');
  }
  
  if (!task.dueDate) {
    recommendations.push('• Установите реалистичный дедлайн');
  }
  
  return recommendations.length > 0 ? recommendations.join('\n') : '• Задача готова к выполнению';
}

function suggestNextSteps(task: Task): string {
  if (task.status === 'todo') {
    return '1. Назначить ответственного\n2. Установить дедлайн\n3. Разбить на подзадачи\n4. Начать выполнение';
  } else if (task.status === 'in-progress') {
    return '1. Проверить прогресс подзадач\n2. Выявить блокеры\n3. Обновить статус\n4. Скорректировать план при необходимости';
  } else {
    return '1. Провести ретроспективу\n2. Зафиксировать уроки\n3. Подготовить отчет\n4. Архивировать задачу';
  }
}

function isDeadlineClose(dueDate: string): boolean {
  try {
    const due = new Date(dueDate);
    const now = new Date();
    const diffDays = (due.getTime() - now.getTime()) / (1000 * 3600 * 24);
    return diffDays <= 3 && diffDays > 0;
  } catch {
    return false;
  }
}

function calculateSubtaskDueDate(parentDueDate: string | undefined, index: number, estimatedHours: number): string | undefined {
  if (!parentDueDate) return undefined;

  try {
    const parentDue = new Date(parentDueDate);
    const daysBeforeParent = Math.max(1, Math.floor((estimatedHours * (index + 1)) / 8));
    const subtaskDue = new Date(parentDue);
    subtaskDue.setDate(subtaskDue.getDate() - daysBeforeParent);
    return subtaskDue.toISOString().split('T')[0];
  } catch {
    return undefined;
  }
}

// Format subtask response
function formatSubtaskResponse(task: Task | null, subtasks: Subtask[]): string {
  if (!task) return "Пожалуйста, выберите задачу для разбиения на подзадачи.";

  return `🔧 **Рекомендуемые подзадачи для: "${task.title}"**

📋 **Предлагаемое разбиение:**

${subtasks.map((subtask, index) => `
**${index + 1}. ${subtask.title}**
   • Описание: ${subtask.description || 'Детальное описание будет добавлено при создании'}
   • Оценка времени: ${subtask.estimatedHours || 2} часов
   • Приоритет: ${getPriorityText(subtask.priority || 'medium')}
   • Срок выполнения: ${subtask.dueDate ? formatDate(subtask.dueDate) : 'Будет установлен'}
`).join('')}

💡 **Советы по выполнению:**
• Начните с подзадач высокого приоритета
• Учитывайте зависимости между подзадачами
• Регулярно обновляйте статус выполнения
• При блокерах - немедленно эскалируйте проблему

📊 **Общая оценка:**
• Всего подзадач: ${subtasks.length}
• Общее время: ${subtasks.reduce((total, st) => total + (st.estimatedHours || 2), 0)} часов
• Рекомендуемый срок: ${Math.ceil(subtasks.reduce((total, st) => total + (st.estimatedHours || 2), 0) / 8)} рабочих дней`;
}

// Solution Suggestions Function
function generateSolutionSuggestions(task: Task | null): string {
  if (!task) return "Пожалуйста, выберите задачу для получения рекомендаций по решению.";

  return `💡 **Рекомендации по решению: "${task.title}"**

🛠️ **Подходы к реализации:**
${generateImplementationApproaches(task)}

📚 **Лучшие практики:**
${generateBestPractices(task)}

🔧 **Рекомендуемые инструменты:**
${suggestTools(task)}

⚡ **Потенциальные вызовы и решения:**
${identifyChallengesAndSolutions(task)}

📖 **Полезные ресурсы:**
${suggestResources(task)}`;
}

// Implementation approaches based on task type
function generateImplementationApproaches(task: Task): string {
  const title = task.title.toLowerCase();
  const description = task.description?.toLowerCase() || '';

  if (title.includes('разработ') || title.includes('develop') || title.includes('код')) {
    return `• **Итеративный подход**: Разбейте разработку на небольшие итерации
• **TDD (Test-Driven Development)**: Начните с написания тестов
• **MVP подход**: Сначала создайте минимально жизнеспособный продукт
• **Модульная архитектура**: Разделите код на независимые модули
• **Code Review**: Обязательное рецензирование кода коллегами`;
  }

  if (title.includes('дизайн') || title.includes('design')) {
    return `• **Design Thinking**: Используйте методологию дизайн-мышления
• **User-Centered Design**: Ставьте пользователя в центр процесса
• **Прототипирование**: Создавайте быстрые прототипы для тестирования идей
• **Итеративный дизайн**: Улучшайте дизайн на основе обратной связи
• **Система дизайна**: Создайте единую систему компонентов`;
  }

  if (title.includes('исследован') || title.includes('research')) {
    return `• **Смешанные методы**: Комбинируйте количественные и качественные методы
• **Структурированный подход**: Следуйте четкому плану исследования
• **Валидация данных**: Проверяйте достоверность источников
• **Peer Review**: Привлекайте коллег для проверки методологии
• **Документирование**: Тщательно фиксируйте процесс и результаты`;
  }

  return `• **Планирование**: Начните с детального планирования
• **Поэтапная реализация**: Разбейте на логические этапы
• **Регулярные проверки**: Устанавливайте контрольные точки
• **Гибкость**: Будьте готовы адаптировать подход
• **Документирование**: Фиксируйте процесс и решения`;
}

// Best practices suggestions
function generateBestPractices(task: Task): string {
  const practices = [
    '• **Четкие критерии готовности**: Определите, когда задача считается выполненной',
    '• **Регулярные обновления**: Обновляйте статус задачи минимум раз в день',
    '• **Управление рисками**: Выявляйте и планируйте решение потенциальных проблем',
    '• **Коммуникация**: Поддерживайте связь с заинтересованными сторонами',
    '• **Качество превыше скорости**: Лучше сделать качественно, чем быстро и плохо'
  ];

  if (task.priority === 'high') {
    practices.unshift('• **Фокус на приоритете**: Сосредоточьтесь только на этой задаче');
  }

  if (task.assignee) {
    practices.push('• **Делегирование**: Четко распределите ответственность между участниками');
  }

  return practices.join('\n');
}

// Tool suggestions based on task type
function suggestTools(task: Task): string {
  const title = task.title.toLowerCase();

  if (title.includes('разработ') || title.includes('develop')) {
    return `• **IDE**: VS Code, IntelliJ IDEA, или аналогичные
• **Контроль версий**: Git с GitHub/GitLab
• **Тестирование**: Jest, Cypress, или специфичные для технологии
• **CI/CD**: GitHub Actions, Jenkins, или GitLab CI
• **Мониторинг**: Sentry, LogRocket для отслеживания ошибок`;
  }

  if (title.includes('дизайн') || title.includes('design')) {
    return `• **Дизайн**: Figma, Sketch, Adobe XD
• **Прототипирование**: InVision, Marvel, Principle
• **Исследования**: Miro, Mural для воркшопов
• **Тестирование**: Maze, UserTesting для пользовательских тестов
• **Коллаборация**: Slack, Microsoft Teams`;
  }

  if (title.includes('исследован') || title.includes('research')) {
    return `• **Анализ данных**: Excel, Google Sheets, Tableau
• **Опросы**: Google Forms, Typeform, SurveyMonkey
• **Интервью**: Zoom, Google Meet с записью
• **Организация**: Notion, Obsidian для структурирования знаний
• **Визуализация**: Miro, Lucidchart для схем и диаграмм`;
  }

  return `• **Планирование**: Trello, Asana, или текущая система
• **Коммуникация**: Slack, Microsoft Teams
• **Документация**: Notion, Confluence, Google Docs
• **Время**: Toggl, RescueTime для отслеживания времени
• **Файлы**: Google Drive, Dropbox для совместной работы`;
}

// Challenges and solutions
function identifyChallengesAndSolutions(task: Task): string {
  const challenges = [];

  if (!task.dueDate) {
    challenges.push('• **Отсутствие дедлайна** → Установите реалистичный срок выполнения');
  }

  if (task.priority === 'high' && task.status === 'todo') {
    challenges.push('• **Высокий приоритет не начат** → Немедленно приступите к выполнению');
  }

  if (!task.assignee) {
    challenges.push('• **Нет ответственного** → Назначьте конкретного исполнителя');
  }

  if (!task.subtasks || task.subtasks.length === 0) {
    challenges.push('• **Отсутствие детализации** → Разбейте на более мелкие подзадачи');
  }

  if (task.description && task.description.length < 50) {
    challenges.push('• **Недостаточно деталей** → Дополните описание задачи');
  }

  // Add common challenges
  challenges.push('• **Прокрастинация** → Используйте технику "помидора" (25 мин работы, 5 мин отдыха)');
  challenges.push('• **Отвлечения** → Создайте фокусированную рабочую среду');
  challenges.push('• **Неопределенность** → Задавайте уточняющие вопросы заранее');

  return challenges.join('\n');
}

// Resource suggestions
function suggestResources(task: Task): string {
  const title = task.title.toLowerCase();

  if (title.includes('разработ') || title.includes('develop')) {
    return `• **Документация**: MDN, официальная документация технологий
• **Обучение**: freeCodeCamp, Codecademy, Coursera
• **Сообщества**: Stack Overflow, GitHub Discussions
• **Блоги**: Medium, Dev.to, Habr
• **Подкасты**: Software Engineering Daily, CodeNewbie`;
  }

  if (title.includes('дизайн') || title.includes('design')) {
    return `• **Вдохновение**: Dribbble, Behance, Awwwards
• **Обучение**: Interaction Design Foundation, Coursera UX курсы
• **Сообщества**: Designer Hangout, UX Mastery Community
• **Блоги**: Nielsen Norman Group, Smashing Magazine
• **Инструменты**: Figma Community, UI8 для ресурсов`;
  }

  return `• **Методологии**: Agile, Scrum, Kanban руководства
• **Книги**: "Getting Things Done", "The Lean Startup"
• **Курсы**: Coursera, edX по управлению проектами
• **Сообщества**: Reddit, профессиональные форумы
• **Блоги**: Harvard Business Review, Medium`;
}

// Planning advice function
function generatePlanningAdvice(task: Task | null, allTasks: Task[]): string {
  if (!task) return "Пожалуйста, выберите задачу для планирования.";

  const projectStats = analyzeProjectContext(allTasks);

  return `📋 **План выполнения задачи: "${task.title}"**

🎯 **Стратегический подход:**
${generateStrategicApproach(task, projectStats)}

⏱️ **Временное планирование:**
${generateTimelinePlanning(task)}

👥 **Ресурсное планирование:**
${generateResourcePlanning(task)}

🔗 **Управление зависимостями:**
${analyzeDependencies(task, allTasks)}

📊 **Контрольные точки:**
${generateMilestones(task)}

🎯 **Критерии успеха:**
${defineSuccessCriteria(task)}`;
}

// Optimization suggestions function
function generateOptimizationSuggestions(task: Task | null, allTasks: Task[]): string {
  if (!task) return "Пожалуйста, выберите задачу для оптимизации.";

  return `⚡ **Рекомендации по оптимизации: "${task.title}"**

🚀 **Повышение эффективности:**
${generateEfficiencyImprovements(task)}

🔄 **Оптимизация процессов:**
${generateProcessOptimizations(task, allTasks)}

⚖️ **Балансировка нагрузки:**
${generateWorkloadBalancing(task, allTasks)}

🎯 **Фокусировка усилий:**
${generateFocusRecommendations(task)}

📈 **Метрики и отслеживание:**
${generateMetricsRecommendations(task)}`;
}

// General assistance function
function generateGeneralAssistance(task: Task | null, prompt: string): string {
  if (!task) {
    return `🤖 **AI Ассистент готов помочь!**

Я могу помочь вам с:
• 📊 **Анализом задач** - детальный разбор задачи и рекомендации
• 🔧 **Генерацией подзадач** - разбиение сложных задач на управляемые части
• 💡 **Поиском решений** - предложение подходов и инструментов
• 📋 **Планированием** - создание планов выполнения
• ⚡ **Оптимизацией** - улучшение процессов и эффективности

**Примеры запросов:**
• "Проанализируй эту задачу"
• "Разбей задачу на подзадачи"
• "Как лучше решить эту задачу?"
• "Составь план выполнения"
• "Как оптимизировать работу?"

Выберите задачу и задайте вопрос для получения персонализированных рекомендаций!`;
  }

  return `🤖 **Анализ запроса для задачи: "${task.title}"**

Я проанализировал ваш запрос: "${prompt}"

📊 **Быстрый анализ задачи:**
• Статус: ${getStatusText(task.status)}
• Приоритет: ${getPriorityText(task.priority)}
• Сложность: ${analyzeTaskComplexity(task)}

💡 **Рекомендации:**
${generateQuickRecommendations(task)}

🎯 **Следующие шаги:**
${suggestNextSteps(task)}

Для получения более детальных рекомендаций используйте специфичные запросы:
• "анализ" - для подробного анализа
• "подзадачи" - для разбиения на части
• "решение" - для поиска подходов
• "план" - для планирования выполнения`;
}

// Helper functions for planning and optimization
function analyzeProjectContext(allTasks: Task[]): any {
  return {
    total: allTasks.length,
    todo: allTasks.filter(t => t.status === 'todo').length,
    inProgress: allTasks.filter(t => t.status === 'in-progress').length,
    done: allTasks.filter(t => t.status === 'done').length,
    highPriority: allTasks.filter(t => t.priority === 'high').length,
    withDeadlines: allTasks.filter(t => t.dueDate).length
  };
}

function generateStrategicApproach(task: Task, projectStats: any): string {
  const approaches = [];

  if (task.priority === 'high') {
    approaches.push('• **Приоритетный фокус**: Сосредоточьте максимум ресурсов на этой задаче');
  }

  if (projectStats.highPriority > 3) {
    approaches.push('• **Последовательное выполнение**: Избегайте многозадачности из-за множества приоритетных задач');
  }

  if (task.subtasks && task.subtasks.length > 0) {
    approaches.push('• **Инкрементальный подход**: Выполняйте подзадачи последовательно');
  } else {
    approaches.push('• **Разбиение на этапы**: Рекомендуется создать подзадачи для лучшего контроля');
  }

  approaches.push('• **Итеративная проверка**: Регулярно оценивайте прогресс и корректируйте план');

  return approaches.join('\n');
}

function generateTimelinePlanning(task: Task): string {
  const timeline = [];

  if (task.dueDate) {
    const daysUntilDeadline = Math.ceil((new Date(task.dueDate).getTime() - new Date().getTime()) / (1000 * 3600 * 24));

    if (daysUntilDeadline <= 1) {
      timeline.push('• **Критический дедлайн**: Сосредоточьтесь на минимально жизнеспособном результате');
    } else if (daysUntilDeadline <= 3) {
      timeline.push('• **Близкий дедлайн**: Работайте в режиме спринта, исключите все отвлечения');
    } else {
      timeline.push(`• **Планомерное выполнение**: У вас есть ${daysUntilDeadline} дней для качественного выполнения`);
    }
  } else {
    timeline.push('• **Установите дедлайн**: Рекомендуется установить конкретную дату завершения');
  }

  timeline.push('• **Ежедневные проверки**: Оценивайте прогресс каждый день');
  timeline.push('• **Буферное время**: Заложите 20% времени на непредвиденные сложности');

  return timeline.join('\n');
}

function generateResourcePlanning(task: Task): string {
  const resources = [];

  if (task.assignee) {
    resources.push(`• **Ответственный**: ${task.assignee.name} - основной исполнитель`);
  } else {
    resources.push('• **Назначение ответственного**: Необходимо определить основного исполнителя');
  }

  resources.push('• **Экспертная поддержка**: Определите, кто может помочь при возникновении вопросов');
  resources.push('• **Инструменты и доступы**: Убедитесь в наличии всех необходимых ресурсов');
  resources.push('• **Время фокуса**: Выделите непрерывные блоки времени для работы');

  return resources.join('\n');
}

function analyzeDependencies(task: Task, allTasks: Task[]): string {
  const dependencies = [];

  // Simple dependency analysis based on task relationships
  const relatedTasks = allTasks.filter(t =>
    t.id !== task.id &&
    (t.projectId === task.projectId ||
     t.tags?.some(tag => task.tags?.includes(tag)))
  );

  if (relatedTasks.length > 0) {
    dependencies.push('• **Связанные задачи**: Проверьте зависимости с другими задачами проекта');
  }

  dependencies.push('• **Внешние зависимости**: Определите, что нужно от других людей или систем');
  dependencies.push('• **Блокеры**: Выявите потенциальные препятствия заранее');
  dependencies.push('• **Коммуникация**: Уведомите заинтересованные стороны о начале работы');

  return dependencies.join('\n');
}

function generateMilestones(task: Task): string {
  const milestones = [];

  if (task.subtasks && task.subtasks.length > 0) {
    milestones.push('• **25% выполнения**: Завершение первой четверти подзадач');
    milestones.push('• **50% выполнения**: Достижение середины - время для промежуточной оценки');
    milestones.push('• **75% выполнения**: Подготовка к финальной стадии');
    milestones.push('• **100% выполнения**: Завершение всех подзадач и финальная проверка');
  } else {
    milestones.push('• **Планирование завершено**: Детальный план готов');
    milestones.push('• **Первые результаты**: Получены первые осязаемые результаты');
    milestones.push('• **Основная работа**: Выполнена основная часть задачи');
    milestones.push('• **Финализация**: Проверка, тестирование и завершение');
  }

  return milestones.join('\n');
}

function defineSuccessCriteria(task: Task): string {
  const criteria = [];

  criteria.push('• **Функциональность**: Все требования выполнены согласно описанию');
  criteria.push('• **Качество**: Результат соответствует стандартам качества');
  criteria.push('• **Сроки**: Задача завершена в установленные временные рамки');

  if (task.assignee) {
    criteria.push('• **Приемка**: Результат принят ответственным лицом');
  }

  criteria.push('• **Документация**: Процесс и результаты задокументированы');

  return criteria.join('\n');
}

function generateEfficiencyImprovements(task: Task): string {
  const improvements = [];

  improvements.push('• **Устранение отвлечений**: Создайте фокусированную рабочую среду');
  improvements.push('• **Батчинг**: Группируйте похожие активности');
  improvements.push('• **Автоматизация**: Автоматизируйте повторяющиеся действия');
  improvements.push('• **Шаблоны**: Используйте готовые шаблоны и чек-листы');

  if (task.priority === 'high') {
    improvements.push('• **Делегирование**: Передайте менее критичные задачи другим');
  }

  return improvements.join('\n');
}

function generateProcessOptimizations(task: Task, allTasks: Task[]): string {
  const optimizations = [];

  optimizations.push('• **Стандартизация**: Создайте стандартные процедуры для похожих задач');
  optimizations.push('• **Обратная связь**: Установите быстрые циклы получения фидбека');
  optimizations.push('• **Инструменты**: Используйте специализированные инструменты для вашего типа задач');

  const similarTasks = allTasks.filter(t =>
    t.id !== task.id &&
    t.title.toLowerCase().includes(task.title.toLowerCase().split(' ')[0])
  );

  if (similarTasks.length > 0) {
    optimizations.push('• **Переиспользование**: Используйте опыт и наработки из похожих задач');
  }

  return optimizations.join('\n');
}

function generateWorkloadBalancing(task: Task, allTasks: Task[]): string {
  const balancing = [];

  const activeTasks = allTasks.filter(t => t.status === 'in-progress').length;

  if (activeTasks > 3) {
    balancing.push('• **Снижение многозадачности**: Слишком много активных задач, сосредоточьтесь на приоритетных');
  }

  balancing.push('• **Энергетические циклы**: Выполняйте сложные задачи в периоды пиковой энергии');
  balancing.push('• **Перерывы**: Планируйте регулярные перерывы для поддержания продуктивности');
  balancing.push('• **Нагрузка команды**: Равномерно распределяйте задачи между участниками');

  return balancing.join('\n');
}

function generateFocusRecommendations(task: Task): string {
  const focus = [];

  if (task.priority === 'high') {
    focus.push('• **Единственный фокус**: Работайте только над этой задачей до завершения');
  }

  focus.push('• **Техника Помидора**: 25 минут фокусированной работы, 5 минут отдыха');
  focus.push('• **Глубокая работа**: Выделите 2-4 часа непрерывного времени');
  focus.push('• **Цифровой детокс**: Отключите уведомления во время работы');
  focus.push('• **Одна задача**: Избегайте переключения между задачами');

  return focus.join('\n');
}

function generateMetricsRecommendations(task: Task): string {
  const metrics = [];

  metrics.push('• **Время выполнения**: Отслеживайте фактическое время vs. планируемое');
  metrics.push('• **Качество**: Количество итераций до приемки результата');
  metrics.push('• **Прогресс**: Ежедневный процент выполнения');

  if (task.subtasks && task.subtasks.length > 0) {
    metrics.push('• **Завершение подзадач**: Процент выполненных подзадач');
  }

  metrics.push('• **Блокеры**: Количество и время решения препятствий');

  return metrics.join('\n');
}

function generateQuickRecommendations(task: Task): string {
  const recommendations = [];

  if (task.status === 'todo') {
    recommendations.push('• Начните с планирования и разбиения на подзадачи');
  } else if (task.status === 'in-progress') {
    recommendations.push('• Проверьте прогресс и выявите возможные блокеры');
  }

  if (task.priority === 'high') {
    recommendations.push('• Сосредоточьтесь на этой задаче в первую очередь');
  }

  if (!task.dueDate) {
    recommendations.push('• Установите конкретный дедлайн');
  }

  if (!task.assignee) {
    recommendations.push('• Назначьте ответственного исполнителя');
  }

  return recommendations.length > 0 ? recommendations.join('\n') : '• Задача готова к выполнению';
}
