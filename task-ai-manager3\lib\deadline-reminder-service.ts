"use client";

import { sendReminderEmail, ReminderData } from './email-service';
import { sendTelegramReminder, TelegramReminderData } from './telegram-service';

// Интерфейс для элемента с дедлайном
export interface DeadlineItem {
  id: string;
  title: string;
  description?: string;
  dueDate: Date;
  type: 'task' | 'subtask' | 'project' | 'event';
  projectId?: string;
  projectName?: string;
  assigneeId?: string;
  assigneeName?: string;
  assigneeEmail?: string;
  telegramChatId?: string;
  status: 'todo' | 'inProgress' | 'done' | 'overdue';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

// Интерфейс для настроек напоминаний пользователя
export interface UserReminderSettings {
  userId: string;
  emailReminders: boolean;
  telegramReminders: boolean;
  emailForReminders: string;
  telegramUsername: string;
  reminderTimes: number[]; // Время до дедлайна в минутах [1440, 60, 15] = за день, час, 15 минут
}

// Функция для получения настроек напоминаний пользователя
export function getUserReminderSettings(userId: string): UserReminderSettings {
  if (typeof window === 'undefined') {
    return {
      userId,
      emailReminders: false,
      telegramReminders: false,
      emailForReminders: '',
      telegramUsername: '',
      reminderTimes: [1440, 60, 15], // По умолчанию: за день, час, 15 минут
    };
  }

  return {
    userId,
    emailReminders: localStorage.getItem('emailReminders') === 'true',
    telegramReminders: localStorage.getItem('telegramReminders') === 'true',
    emailForReminders: localStorage.getItem('emailForReminders') || '',
    telegramUsername: localStorage.getItem('telegramUsername') || '',
    reminderTimes: JSON.parse(localStorage.getItem('reminderTimes') || '[1440, 60, 15]'),
  };
}

// Функция для проверки, нужно ли отправить напоминание
export function shouldSendReminder(item: DeadlineItem, reminderTimeMinutes: number): boolean {
  const now = new Date();
  const dueDate = new Date(item.dueDate);
  const timeDifference = dueDate.getTime() - now.getTime();
  const minutesUntilDue = Math.floor(timeDifference / (1000 * 60));
  
  // Проверяем, что элемент не завершен и время напоминания подошло
  return (
    item.status !== 'done' &&
    minutesUntilDue <= reminderTimeMinutes &&
    minutesUntilDue > (reminderTimeMinutes - 5) // Окно в 5 минут для отправки
  );
}

// Функция для отправки напоминания о дедлайне
export async function sendDeadlineReminder(
  item: DeadlineItem,
  settings: UserReminderSettings,
  reminderTimeMinutes: number
): Promise<boolean> {
  try {
    let emailSent = false;
    let telegramSent = false;
    
    const timeUntilDue = getTimeUntilDueText(item.dueDate, reminderTimeMinutes);
    const urgencyLevel = getUrgencyLevel(reminderTimeMinutes);
    
    // Отправляем email-напоминание
    if (settings.emailReminders && settings.emailForReminders) {
      const emailData: ReminderData = {
        userId: settings.userId,
        userEmail: settings.emailForReminders,
        userName: item.assigneeName || 'Пользователь',
        subject: `${urgencyLevel} Напоминание о дедлайне: ${item.title}`,
        message: `У вас есть ${item.type === 'task' ? 'задача' : item.type === 'subtask' ? 'подзадача' : item.type === 'project' ? 'проект' : 'событие'} "${item.title}", которая должна быть выполнена ${timeUntilDue}.`,
        itemType: item.type as 'event' | 'task' | 'subtask' | 'project',
        itemId: item.id,
        itemTitle: item.title,
        dueDate: new Date(item.dueDate),
        projectName: item.projectName,
      };
      
      emailSent = await sendReminderEmail(emailData);
    }
    
    // Отправляем Telegram-напоминание
    if (settings.telegramReminders && settings.telegramUsername) {
      const telegramData: TelegramReminderData = {
        userId: settings.userId,
        telegramChatId: settings.telegramUsername,
        userName: item.assigneeName || 'Пользователь',
        subject: `${urgencyLevel} Напоминание о дедлайне`,
        message: `У вас есть ${item.type === 'task' ? 'задача' : item.type === 'subtask' ? 'подзадача' : item.type === 'project' ? 'проект' : 'событие'} "${item.title}", которая должна быть выполнена ${timeUntilDue}.`,
        itemType: item.type as 'event' | 'task' | 'subtask' | 'project',
        itemId: item.id,
        itemTitle: item.title,
        dueDate: new Date(item.dueDate),
        projectName: item.projectName,
      };
      
      telegramSent = await sendTelegramReminder(telegramData);
    }
    
    // Логируем отправку напоминания
    console.log(`Deadline reminder sent for ${item.type} "${item.title}" (${reminderTimeMinutes} minutes before due)`);
    
    return emailSent || telegramSent;
  } catch (error) {
    console.error('Error sending deadline reminder:', error);
    return false;
  }
}

// Функция для получения текста времени до дедлайна
function getTimeUntilDueText(dueDate: Date, reminderTimeMinutes: number): string {
  if (reminderTimeMinutes >= 1440) {
    const days = Math.floor(reminderTimeMinutes / 1440);
    return `через ${days} ${days === 1 ? 'день' : days < 5 ? 'дня' : 'дней'}`;
  } else if (reminderTimeMinutes >= 60) {
    const hours = Math.floor(reminderTimeMinutes / 60);
    return `через ${hours} ${hours === 1 ? 'час' : hours < 5 ? 'часа' : 'часов'}`;
  } else {
    return `через ${reminderTimeMinutes} ${reminderTimeMinutes === 1 ? 'минуту' : reminderTimeMinutes < 5 ? 'минуты' : 'минут'}`;
  }
}

// Функция для получения уровня срочности
function getUrgencyLevel(reminderTimeMinutes: number): string {
  if (reminderTimeMinutes <= 15) {
    return '🚨 СРОЧНО!';
  } else if (reminderTimeMinutes <= 60) {
    return '⚠️ ВАЖНО!';
  } else if (reminderTimeMinutes <= 1440) {
    return '📅';
  } else {
    return '📋';
  }
}

// Функция для получения всех элементов с дедлайнами из localStorage
export function getDeadlineItemsFromStorage(): DeadlineItem[] {
  if (typeof window === 'undefined') {
    return [];
  }
  
  const items: DeadlineItem[] = [];
  
  try {
    // Получаем проекты
    const projectsData = localStorage.getItem('projects');
    if (projectsData) {
      const projects = JSON.parse(projectsData);
      projects.forEach((project: any) => {
        if (project.dueDate) {
          items.push({
            id: project.id,
            title: project.name,
            description: project.description,
            dueDate: new Date(project.dueDate),
            type: 'project',
            projectId: project.id,
            projectName: project.name,
            status: project.status || 'todo',
            priority: project.priority,
          });
        }
      });
    }
    
    // Получаем задачи
    const tasksData = localStorage.getItem('tasksData');
    if (tasksData) {
      const tasks = JSON.parse(tasksData);
      Object.keys(tasks).forEach(projectId => {
        const projectTasks = tasks[projectId];
        Object.keys(projectTasks).forEach(status => {
          projectTasks[status].forEach((task: any) => {
            if (task.dueDate) {
              items.push({
                id: task.id,
                title: task.title,
                description: task.description,
                dueDate: new Date(task.dueDate),
                type: 'task',
                projectId: projectId,
                projectName: task.projectName,
                assigneeId: task.assignee,
                assigneeName: task.assigneeName,
                assigneeEmail: task.assigneeEmail,
                status: status as any,
                priority: task.priority,
              });
              
              // Добавляем подзадачи
              if (task.subtasks) {
                task.subtasks.forEach((subtask: any) => {
                  if (subtask.dueDate) {
                    items.push({
                      id: subtask.id,
                      title: subtask.title,
                      description: subtask.description,
                      dueDate: new Date(subtask.dueDate),
                      type: 'subtask',
                      projectId: projectId,
                      projectName: task.projectName,
                      assigneeId: subtask.assignee,
                      assigneeName: subtask.assigneeName,
                      assigneeEmail: subtask.assigneeEmail,
                      status: subtask.completed ? 'done' : 'todo',
                      priority: subtask.priority,
                    });
                  }
                });
              }
            }
          });
        });
      });
    }
    
    // Получаем события календаря
    const eventsData = localStorage.getItem('calendarEvents');
    if (eventsData) {
      const events = JSON.parse(eventsData);
      events.forEach((event: any) => {
        items.push({
          id: event.id,
          title: event.title,
          description: event.description,
          dueDate: new Date(event.end || event.start),
          type: 'event',
          projectId: event.projectId,
          projectName: event.projectName,
          status: 'todo',
          priority: event.priority,
        });
      });
    }
  } catch (error) {
    console.error('Error getting deadline items from storage:', error);
  }
  
  return items;
}

// Функция для проверки и отправки всех напоминаний
export async function checkAndSendAllReminders(): Promise<void> {
  try {
    const items = getDeadlineItemsFromStorage();
    const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
    
    if (!currentUser.id) {
      console.log('No current user found, skipping reminder check');
      return;
    }
    
    const settings = getUserReminderSettings(currentUser.id);
    
    if (!settings.emailReminders && !settings.telegramReminders) {
      console.log('Reminders are disabled for user', currentUser.id);
      return;
    }
    
    console.log(`Checking ${items.length} items for reminders...`);
    
    for (const item of items) {
      for (const reminderTime of settings.reminderTimes) {
        if (shouldSendReminder(item, reminderTime)) {
          await sendDeadlineReminder(item, settings, reminderTime);
        }
      }
    }
  } catch (error) {
    console.error('Error checking and sending reminders:', error);
  }
}
