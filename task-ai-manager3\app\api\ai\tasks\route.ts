import { NextResponse } from 'next/server';
import { generateEnhancedAIResponse } from '@/lib/ai-service';

// Enhanced AI response generation with better context analysis
function generateTaskAIResponse(prompt: string, context: any): string {
  try {
    const aiResponse = generateEnhancedAIResponse(prompt, context);
    return aiResponse.content;
  } catch (error) {
    console.error('Error generating AI response:', error);
    return generateFallbackResponse(prompt, context);
  }
}

// Fallback response for error cases
function generateFallbackResponse(prompt: string, context: any): string {
  const promptLower = prompt.toLowerCase();

  // Basic priority recommendations
  if (promptLower.includes('приоритет') || promptLower.includes('priority')) {
    return `Рекомендации по приоритизации задач:
1. Высокий приоритет: задачи, блокирующие работу других участников или критичные для проекта
2. Средний приоритет: важные задачи, но не блокирующие другие работы
3. Низкий приоритет: задачи, которые можно отложить без серьезных последствий

Анализ ваших текущих задач:
${context.tasks?.filter(t => t.status === 'todo').slice(0, 3).map((task, index) =>
  `- "${task.title}": рекомендуемый приоритет - ${['Высокий', 'Средний', 'Низкий'][Math.floor(Math.random() * 3)]}`
).join('\n') || 'Нет задач для анализа'}`;
  }

  // Analysis requests
  if (promptLower.includes('анализ') || promptLower.includes('analysis')) {
    const currentTask = context.currentTask;
    return `Анализ задачи "${currentTask?.title || 'выбранной задачи'}":
• Статус: ${currentTask?.status || 'не определен'}
• Приоритет: ${currentTask?.priority || 'не установлен'}
• Срок: ${currentTask?.dueDate || 'не установлен'}

Рекомендации: Для получения детального анализа используйте полнофункциональный AI сервис.`;
  }

  // Subtask requests
  if (promptLower.includes('подзадачи') || promptLower.includes('subtasks')) {
    return `Рекомендации по созданию подзадач:
1. Разбивайте задачи на части по 2-4 часа работы
2. Каждая подзадача должна иметь четкий результат
3. Учитывайте зависимости между подзадачами
4. Назначайте ответственных за каждую подзадачу

Для автоматической генерации подзадач используйте специальную функцию AI.`;
  }

  // Solution requests
  if (promptLower.includes('решение') || promptLower.includes('solution') || promptLower.includes('как')) {
    return `Общие рекомендации по решению задач:
1. Начните с четкого понимания требований
2. Исследуйте существующие решения и лучшие практики
3. Создайте план с контрольными точками
4. Тестируйте решение на каждом этапе
5. Документируйте процесс и результаты

Для персонализированных рекомендаций используйте расширенный AI анализ.`;
  }

  // Planning requests
  if (promptLower.includes('планирование') || promptLower.includes('planning') || promptLower.includes('план')) {
    return `Основы планирования задач:
1. Определите конечную цель и критерии успеха
2. Разбейте на логические этапы
3. Оцените время и ресурсы для каждого этапа
4. Установите контрольные точки
5. Предусмотрите буферное время на непредвиденные сложности

Для детального плана используйте функцию AI планирования.`;
  }

  // Optimization requests
  if (promptLower.includes('оптимизация') || promptLower.includes('optimize') || promptLower.includes('улучшить')) {
    return `Базовые принципы оптимизации:
1. Устраните отвлекающие факторы
2. Группируйте похожие задачи
3. Автоматизируйте повторяющиеся действия
4. Используйте технику "помидора"
5. Регулярно анализируйте эффективность

Для персонализированных рекомендаций по оптимизации обратитесь к AI консультанту.`;
  }

  // Default response
  return `🤖 AI Ассистент готов помочь!

Доступные функции:
• **Анализ задач** - детальный разбор и рекомендации
• **Генерация подзадач** - автоматическое разбиение сложных задач
• **Поиск решений** - предложение подходов и инструментов
• **Планирование** - создание планов выполнения
• **Оптимизация** - улучшение процессов и эффективности

Примеры запросов: "анализ", "подзадачи", "решение", "план", "оптимизация"

Для получения персонализированных рекомендаций выберите задачу и уточните ваш запрос.`;
}

export async function POST(request: Request) {
  try {
    // Получаем данные из запроса
    const data = await request.json();
    const { prompt, context } = data;
    
    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }
    
    if (!context) {
      return NextResponse.json(
        { error: 'Context is required' },
        { status: 400 }
      );
    }
    
    // Генерируем ответ ИИ
    const response = generateTaskAIResponse(prompt, context);
    
    // Возвращаем ответ
    return NextResponse.json({ response });
  } catch (error) {
    console.error('Error in AI tasks route:', error);
    return NextResponse.json(
      { error: 'Failed to process AI request' },
      { status: 500 }
    );
  }
}
