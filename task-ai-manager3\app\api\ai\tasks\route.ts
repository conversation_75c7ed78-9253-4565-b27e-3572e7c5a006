import { NextResponse } from 'next/server';
import { streamText } from 'ai';
import { getAIConfig, getSystemPrompt, createOpenAIClientSync } from '@/lib/ai-config';
import { classifyAIError, generateFallbackResponse } from '@/lib/ai-error-handler';

// Enhanced AI response generation with real OpenAI integration
async function generateTaskAIResponse(prompt: string, context: any): Promise<string> {
  try {
    const config = await getAIConfig();

    // Check if AI is enabled
    if (!config.enabled) {
      const fallback = generateFallbackResponse(prompt, context, {
        type: 'ai_disabled',
        message: 'AI functionality is currently disabled'
      });
      return fallback.content;
    }

    // Detect language
    const isRussian = /[а-яА-Я]/.test(prompt);

    // Create specialized system prompt for task management
    const systemPrompt = await getTaskManagementSystemPrompt(isRussian);

    // Create OpenAI client
    const openai = createOpenAIClientSync();

    // Prepare context information
    const contextInfo = formatTaskContextForAI(context);

    // Generate AI response
    const result = await streamText({
      model: openai,
      system: systemPrompt,
      messages: [
        {
          role: 'user',
          content: `${prompt}\n\nКонтекст: ${contextInfo}`
        }
      ],
      temperature: config.temperature,
      maxTokens: config.maxTokens,
    });

    // Convert stream to text
    let fullResponse = '';
    for await (const chunk of result.textStream) {
      fullResponse += chunk;
    }

    return fullResponse || generateFallbackResponse(prompt, context).content;
  } catch (error) {
    console.error('Error generating AI response:', error);
    const aiError = classifyAIError(error);
    const fallback = generateFallbackResponse(prompt, context, aiError);
    return fallback.content;
  }
}

// Get specialized system prompt for task management
async function getTaskManagementSystemPrompt(isRussian: boolean): Promise<string> {
  const config = await getAIConfig();

  // Use custom prompt if available
  if (config.customPrompt && config.customPrompt !== "You are an AI assistant for a task management application. Help users organize their tasks and projects effectively.") {
    return config.customPrompt;
  }

  // Specialized task management prompts
  return isRussian
    ? `Ты - экспертный ИИ-ассистент по управлению задачами и проектами. Твоя специализация:

🎯 **Анализ задач**: Детальный разбор сложности, приоритетов и зависимостей
📋 **Планирование**: Создание структурированных планов выполнения
🔧 **Оптимизация**: Предложение улучшений процессов и методологий
💡 **Решения**: Поиск креативных подходов к решению проблем
📊 **Декомпозиция**: Разбиение сложных задач на управляемые подзадачи

Отвечай структурированно, используй эмодзи для наглядности, предлагай конкретные действия.`
    : `You are an expert AI assistant specializing in task and project management. Your expertise includes:

🎯 **Task Analysis**: Detailed breakdown of complexity, priorities, and dependencies
📋 **Planning**: Creating structured execution plans
🔧 **Optimization**: Suggesting process and methodology improvements
💡 **Solutions**: Finding creative approaches to problem-solving
📊 **Decomposition**: Breaking complex tasks into manageable subtasks

Respond in a structured way, use emojis for clarity, and suggest specific actionable steps.`;
}

// Format task context for AI
function formatTaskContextForAI(context: any): string {
  if (!context) return 'Контекст не предоставлен';

  let contextStr = '';

  if (context.currentTask) {
    const task = context.currentTask;
    contextStr += `📋 Текущая задача: ${task.title || task.name}\n`;

    if (task.description) {
      contextStr += `📝 Описание: ${task.description}\n`;
    }

    if (task.priority) {
      contextStr += `⚡ Приоритет: ${task.priority}\n`;
    }

    if (task.status) {
      contextStr += `📊 Статус: ${task.status}\n`;
    }

    if (task.dueDate) {
      contextStr += `📅 Срок: ${task.dueDate}\n`;
    }
  }

  if (context.tasks && context.tasks.length > 0) {
    contextStr += `\n📚 Связанные задачи (${context.tasks.length}):\n`;
    context.tasks.slice(0, 5).forEach((task: any, index: number) => {
      contextStr += `${index + 1}. ${task.title || task.name}\n`;
    });

    if (context.tasks.length > 5) {
      contextStr += `... и еще ${context.tasks.length - 5} задач\n`;
    }
  }

  return contextStr || 'Базовый контекст задачи';
}

// Fallback response for error cases
function generateFallbackResponse(prompt: string, context: any): string {
  const promptLower = prompt.toLowerCase();

  // Basic priority recommendations
  if (promptLower.includes('приоритет') || promptLower.includes('priority')) {
    return `Рекомендации по приоритизации задач:
1. Высокий приоритет: задачи, блокирующие работу других участников или критичные для проекта
2. Средний приоритет: важные задачи, но не блокирующие другие работы
3. Низкий приоритет: задачи, которые можно отложить без серьезных последствий

Анализ ваших текущих задач:
${context.tasks?.filter(t => t.status === 'todo').slice(0, 3).map((task, index) =>
  `- "${task.title}": рекомендуемый приоритет - ${['Высокий', 'Средний', 'Низкий'][Math.floor(Math.random() * 3)]}`
).join('\n') || 'Нет задач для анализа'}`;
  }

  // Analysis requests
  if (promptLower.includes('анализ') || promptLower.includes('analysis')) {
    const currentTask = context.currentTask;
    return `Анализ задачи "${currentTask?.title || 'выбранной задачи'}":
• Статус: ${currentTask?.status || 'не определен'}
• Приоритет: ${currentTask?.priority || 'не установлен'}
• Срок: ${currentTask?.dueDate || 'не установлен'}

Рекомендации: Для получения детального анализа используйте полнофункциональный AI сервис.`;
  }

  // Subtask requests
  if (promptLower.includes('подзадачи') || promptLower.includes('subtasks')) {
    return `Рекомендации по созданию подзадач:
1. Разбивайте задачи на части по 2-4 часа работы
2. Каждая подзадача должна иметь четкий результат
3. Учитывайте зависимости между подзадачами
4. Назначайте ответственных за каждую подзадачу

Для автоматической генерации подзадач используйте специальную функцию AI.`;
  }

  // Solution requests
  if (promptLower.includes('решение') || promptLower.includes('solution') || promptLower.includes('как')) {
    return `Общие рекомендации по решению задач:
1. Начните с четкого понимания требований
2. Исследуйте существующие решения и лучшие практики
3. Создайте план с контрольными точками
4. Тестируйте решение на каждом этапе
5. Документируйте процесс и результаты

Для персонализированных рекомендаций используйте расширенный AI анализ.`;
  }

  // Planning requests
  if (promptLower.includes('планирование') || promptLower.includes('planning') || promptLower.includes('план')) {
    return `Основы планирования задач:
1. Определите конечную цель и критерии успеха
2. Разбейте на логические этапы
3. Оцените время и ресурсы для каждого этапа
4. Установите контрольные точки
5. Предусмотрите буферное время на непредвиденные сложности

Для детального плана используйте функцию AI планирования.`;
  }

  // Optimization requests
  if (promptLower.includes('оптимизация') || promptLower.includes('optimize') || promptLower.includes('улучшить')) {
    return `Базовые принципы оптимизации:
1. Устраните отвлекающие факторы
2. Группируйте похожие задачи
3. Автоматизируйте повторяющиеся действия
4. Используйте технику "помидора"
5. Регулярно анализируйте эффективность

Для персонализированных рекомендаций по оптимизации обратитесь к AI консультанту.`;
  }

  // Default response
  return `🤖 AI Ассистент готов помочь!

Доступные функции:
• **Анализ задач** - детальный разбор и рекомендации
• **Генерация подзадач** - автоматическое разбиение сложных задач
• **Поиск решений** - предложение подходов и инструментов
• **Планирование** - создание планов выполнения
• **Оптимизация** - улучшение процессов и эффективности

Примеры запросов: "анализ", "подзадачи", "решение", "план", "оптимизация"

Для получения персонализированных рекомендаций выберите задачу и уточните ваш запрос.`;
}

export async function POST(request: Request) {
  try {
    // Получаем данные из запроса
    const data = await request.json();
    const { prompt, context } = data;

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!context) {
      return NextResponse.json(
        { error: 'Context is required' },
        { status: 400 }
      );
    }

    // Генерируем ответ ИИ
    const response = await generateTaskAIResponse(prompt, context);

    // Возвращаем ответ
    return NextResponse.json({ response });
  } catch (error) {
    console.error('Error in AI tasks route:', error);
    return NextResponse.json(
      { error: 'Failed to process AI request' },
      { status: 500 }
    );
  }
}
