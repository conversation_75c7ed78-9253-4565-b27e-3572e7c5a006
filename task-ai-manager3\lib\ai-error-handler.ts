// AI Error Handler and Fallback System

export interface AIError {
  type: 'api_key_missing' | 'api_limit_exceeded' | 'network_error' | 'invalid_response' | 'ai_disabled' | 'unknown';
  message: string;
  originalError?: any;
}

export interface AIFallbackResponse {
  content: string;
  isFallback: true;
  fallbackReason: string;
}

/**
 * Classify AI errors for better handling
 */
export function classifyAIError(error: any): AIError {
  const errorMessage = error?.message || error?.toString() || 'Unknown error';
  
  if (errorMessage.includes('API key') || errorMessage.includes('apiKey')) {
    return {
      type: 'api_key_missing',
      message: 'OpenAI API key is not configured or invalid',
      originalError: error
    };
  }
  
  if (errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
    return {
      type: 'api_limit_exceeded',
      message: 'API rate limit or quota exceeded',
      originalError: error
    };
  }
  
  if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
    return {
      type: 'network_error',
      message: 'Network connection error',
      originalError: error
    };
  }
  
  if (errorMessage.includes('disabled')) {
    return {
      type: 'ai_disabled',
      message: 'AI functionality is currently disabled',
      originalError: error
    };
  }
  
  return {
    type: 'unknown',
    message: errorMessage,
    originalError: error
  };
}

/**
 * Generate fallback responses based on prompt type and context
 */
export function generateFallbackResponse(prompt: string, context: any, error?: AIError): AIFallbackResponse {
  const promptLower = prompt.toLowerCase();
  const isRussian = /[а-яА-Я]/.test(prompt);
  
  let content = '';
  let fallbackReason = '';
  
  if (error) {
    fallbackReason = `AI service unavailable: ${error.message}`;
  } else {
    fallbackReason = 'Using fallback response system';
  }
  
  // Task analysis fallbacks
  if (promptLower.includes('анализ') || promptLower.includes('analysis') || promptLower.includes('analyze')) {
    content = isRussian 
      ? generateRussianTaskAnalysisFallback(context)
      : generateEnglishTaskAnalysisFallback(context);
  }
  // Project improvement fallbacks
  else if (promptLower.includes('улучшить') || promptLower.includes('improve')) {
    content = isRussian
      ? generateRussianImprovementFallback(context)
      : generateEnglishImprovementFallback(context);
  }
  // Planning fallbacks
  else if (promptLower.includes('план') || promptLower.includes('plan')) {
    content = isRussian
      ? generateRussianPlanningFallback(context)
      : generateEnglishPlanningFallback(context);
  }
  // General fallback
  else {
    content = isRussian
      ? generateRussianGeneralFallback(context, prompt)
      : generateEnglishGeneralFallback(context, prompt);
  }
  
  return {
    content,
    isFallback: true,
    fallbackReason
  };
}

// Russian fallback responses
function generateRussianTaskAnalysisFallback(context: any): string {
  const taskName = context?.currentTask?.title || context?.currentTask?.name || 'текущей задачи';
  
  return `📊 **Анализ задачи: ${taskName}**

⚠️ *Используется базовый анализ (ИИ недоступен)*

🎯 **Основные аспекты:**
• **Сложность**: Требует детального планирования
• **Приоритет**: Определите на основе срочности и важности
• **Ресурсы**: Оцените необходимые навыки и время
• **Зависимости**: Проверьте связи с другими задачами

📋 **Рекомендации:**
1. Разбейте задачу на более мелкие подзадачи
2. Определите критический путь выполнения
3. Установите промежуточные контрольные точки
4. Подготовьте план управления рисками

💡 **Следующие шаги:**
- Уточните требования и ожидания
- Создайте детальный план выполнения
- Назначьте ответственных исполнителей`;
}

function generateRussianImprovementFallback(context: any): string {
  const projectName = context?.name || 'проекта';
  
  return `🚀 **Рекомендации по улучшению: ${projectName}**

⚠️ *Базовые рекомендации (ИИ недоступен)*

📈 **Ключевые области улучшения:**
1. **Планирование**: Детализируйте цели и задачи
2. **Коммуникация**: Улучшите взаимодействие в команде
3. **Контроль**: Внедрите регулярные проверки прогресса
4. **Документация**: Ведите актуальную документацию
5. **Автоматизация**: Автоматизируйте рутинные процессы

🎯 **Практические шаги:**
• Проведите ретроспективу текущих процессов
• Определите узкие места и проблемы
• Внедрите метрики для отслеживания прогресса
• Организуйте регулярные встречи команды`;
}

function generateRussianPlanningFallback(context: any): string {
  return `📅 **План выполнения задач**

⚠️ *Базовый план (ИИ недоступен)*

🗓️ **Этапы планирования:**
1. **Анализ требований** (1-2 дня)
   - Сбор и уточнение требований
   - Определение критериев успеха

2. **Планирование** (1 день)
   - Создание детального плана
   - Распределение ресурсов

3. **Выполнение** (основное время)
   - Реализация согласно плану
   - Регулярные проверки прогресса

4. **Контроль качества** (1-2 дня)
   - Тестирование результатов
   - Исправление недочетов

5. **Завершение** (0.5 дня)
   - Финальная проверка
   - Документирование результатов`;
}

function generateRussianGeneralFallback(context: any, prompt: string): string {
  return `🤖 **Ответ ИИ-ассистента**

⚠️ *ИИ временно недоступен, используется базовый ответ*

Ваш запрос: "${prompt}"

📋 **Общие рекомендации:**
• Разбейте сложные задачи на простые шаги
• Установите четкие сроки и приоритеты
• Регулярно отслеживайте прогресс
• Документируйте важные решения

💡 **Для получения более точного ответа:**
- Проверьте настройки ИИ в админ-панели
- Убедитесь в наличии API-ключа OpenAI
- Попробуйте повторить запрос позже`;
}

// English fallback responses
function generateEnglishTaskAnalysisFallback(context: any): string {
  const taskName = context?.currentTask?.title || context?.currentTask?.name || 'current task';
  
  return `📊 **Task Analysis: ${taskName}**

⚠️ *Using basic analysis (AI unavailable)*

🎯 **Key Aspects:**
• **Complexity**: Requires detailed planning
• **Priority**: Determine based on urgency and importance
• **Resources**: Assess required skills and time
• **Dependencies**: Check connections with other tasks

📋 **Recommendations:**
1. Break down the task into smaller subtasks
2. Identify the critical execution path
3. Set intermediate checkpoints
4. Prepare a risk management plan

💡 **Next Steps:**
- Clarify requirements and expectations
- Create a detailed execution plan
- Assign responsible team members`;
}

function generateEnglishImprovementFallback(context: any): string {
  const projectName = context?.name || 'project';
  
  return `🚀 **Improvement Recommendations: ${projectName}**

⚠️ *Basic recommendations (AI unavailable)*

📈 **Key Improvement Areas:**
1. **Planning**: Detail goals and objectives
2. **Communication**: Improve team interaction
3. **Control**: Implement regular progress checks
4. **Documentation**: Maintain up-to-date documentation
5. **Automation**: Automate routine processes

🎯 **Practical Steps:**
• Conduct a retrospective of current processes
• Identify bottlenecks and issues
• Implement metrics for progress tracking
• Organize regular team meetings`;
}

function generateEnglishPlanningFallback(context: any): string {
  return `📅 **Task Execution Plan**

⚠️ *Basic plan (AI unavailable)*

🗓️ **Planning Phases:**
1. **Requirements Analysis** (1-2 days)
   - Gather and clarify requirements
   - Define success criteria

2. **Planning** (1 day)
   - Create detailed plan
   - Allocate resources

3. **Execution** (main timeframe)
   - Implement according to plan
   - Regular progress checks

4. **Quality Control** (1-2 days)
   - Test results
   - Fix issues

5. **Completion** (0.5 day)
   - Final review
   - Document results`;
}

function generateEnglishGeneralFallback(context: any, prompt: string): string {
  return `🤖 **AI Assistant Response**

⚠️ *AI temporarily unavailable, using basic response*

Your request: "${prompt}"

📋 **General Recommendations:**
• Break complex tasks into simple steps
• Set clear deadlines and priorities
• Regularly track progress
• Document important decisions

💡 **For more accurate responses:**
- Check AI settings in admin panel
- Ensure OpenAI API key is configured
- Try your request again later`;
}
