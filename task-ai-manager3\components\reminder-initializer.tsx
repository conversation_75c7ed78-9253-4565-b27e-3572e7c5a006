"use client";

import { useEffect } from 'react';
import { initializeReminderScheduler } from '@/lib/reminder-scheduler';

export function ReminderInitializer() {
  useEffect(() => {
    // Инициализируем планировщик напоминаний при загрузке приложения
    const timer = setTimeout(() => {
      initializeReminderScheduler();
    }, 1000); // Небольшая задержка для полной загрузки приложения

    return () => clearTimeout(timer);
  }, []);

  // Этот компонент не рендерит ничего видимого
  return null;
}
