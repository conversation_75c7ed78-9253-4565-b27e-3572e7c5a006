import { NextResponse } from 'next/server';

// Функция для генерации ответа ИИ на основе промпта и контекста
function generateAIResponse(prompt: string, context: any): string {
  const promptLower = prompt.toLowerCase();
  
  // Базовые ответы на основе ключевых слов
  if (promptLower.includes('улучшить') || promptLower.includes('improve')) {
    return `Для улучшения проекта "${context.name}" рекомендую:
1. Добавить подробное описание целей и задач
2. Создать график основных вех проекта
3. Определить ключевые показатели эффективности
4. Разработать план коммуникаций для всех участников
5. Внедрить еженедельные проверки прогресса`;
  } 
  
  if (promptLower.includes('задачи') || promptLower.includes('tasks')) {
    return `Рекомендуемые задачи для проекта "${context.name}":
1. Провести стартовое совещание команды
2. Разработать детальный план проекта
3. Определить необходимые ресурсы
4. Создать систему отслеживания прогресса
5. Настроить инструменты коммуникации`;
  }
  
  if (promptLower.includes('риски') || promptLower.includes('risks')) {
    return `Потенциальные риски для проекта "${context.name}":
1. Нехватка ресурсов для выполнения всех задач
2. Изменение требований в процессе работы
3. Технические сложности при реализации
4. Проблемы коммуникации между участниками
5. Срыв сроков из-за внешних факторов`;
  }
  
  if (promptLower.includes('сроки') || promptLower.includes('deadline') || promptLower.includes('timeline')) {
    return `Рекомендации по срокам для проекта "${context.name}":
1. Фаза планирования: 1-2 недели
2. Разработка основных компонентов: 3-4 недели
3. Тестирование и доработка: 2 недели
4. Подготовка к запуску: 1 неделя
5. Запуск и мониторинг: 1-2 недели`;
  }
  
  if (promptLower.includes('бюджет') || promptLower.includes('budget') || promptLower.includes('cost')) {
    return `Рекомендации по бюджету для проекта "${context.name}":
1. Персонал: 60% от общего бюджета
2. Инструменты и технологии: 20% от общего бюджета
3. Маркетинг и продвижение: 10% от общего бюджета
4. Непредвиденные расходы: 10% от общего бюджета
5. Рекомендую регулярно отслеживать расходы и корректировать бюджет при необходимости`;
  }
  
  if (promptLower.includes('команда') || promptLower.includes('team') || promptLower.includes('участники')) {
    return `Рекомендации по команде для проекта "${context.name}":
1. Оптимальный размер команды: 5-7 человек
2. Необходимые роли: руководитель проекта, разработчики, дизайнер, тестировщик
3. Регулярные встречи: ежедневные короткие и еженедельные подробные
4. Четкое распределение ответственности между участниками
5. Система мотивации и признания достижений`;
  }
  
  if (promptLower.includes('ресурсы') || promptLower.includes('resources')) {
    return `Необходимые ресурсы для проекта "${context.name}":
1. Персонал: руководитель проекта, разработчики, дизайнер, тестировщик
2. Программное обеспечение: инструменты разработки, системы управления проектами
3. Оборудование: компьютеры, серверы, сетевое оборудование
4. Время: минимум 8-12 недель для полного цикла разработки
5. Бюджет: зависит от масштаба проекта и стоимости ресурсов`;
  }
  
  // Анализ проекта на основе его статистики
  if (promptLower.includes('анализ') || promptLower.includes('analysis')) {
    let analysis = `Анализ проекта "${context.name}":\n`;
    
    if (context.taskStats) {
      analysis += `1. Прогресс: ${context.taskStats.donePercentage}% задач выполнено\n`;
      analysis += `2. Текущая активность: ${context.taskStats.inProgressPercentage}% задач в работе\n`;
      analysis += `3. Ожидающие задачи: ${context.taskStats.todoPercentage}% задач ожидают выполнения\n`;
    }
    
    if (context.participants) {
      analysis += `4. Команда: ${context.participants.length} участников\n`;
    }
    
    analysis += `5. Рекомендации: `;
    
    if (context.taskStats && context.taskStats.todoPercentage > 50) {
      analysis += `Необходимо активизировать работу над проектом, слишком много задач в ожидании.`;
    } else if (context.taskStats && context.taskStats.donePercentage > 80) {
      analysis += `Проект близок к завершению, рекомендую провести финальное тестирование и подготовить документацию.`;
    } else if (context.taskStats && context.taskStats.inProgressPercentage > 50) {
      analysis += `Много задач в работе одновременно, рекомендую сфокусироваться на завершении начатых задач.`;
    } else {
      analysis += `Проект развивается в нормальном темпе, продолжайте следовать плану.`;
    }
    
    return analysis;
  }
  
  // Генерация задач на основе названия проекта
  if (promptLower.includes('генерировать задачи') || promptLower.includes('generate tasks')) {
    const projectName = context.name.toLowerCase();
    let tasks = `Рекомендуемые задачи для проекта "${context.name}":\n`;
    
    if (projectName.includes('веб') || projectName.includes('сайт') || projectName.includes('web') || projectName.includes('site')) {
      tasks += `1. Разработать дизайн-концепцию сайта\n`;
      tasks += `2. Создать прототип основных страниц\n`;
      tasks += `3. Разработать фронтенд-часть\n`;
      tasks += `4. Разработать бэкенд-часть\n`;
      tasks += `5. Провести тестирование и оптимизацию\n`;
      tasks += `6. Запустить сайт и настроить аналитику`;
    } else if (projectName.includes('мобильн') || projectName.includes('приложение') || projectName.includes('app')) {
      tasks += `1. Разработать концепцию пользовательского интерфейса\n`;
      tasks += `2. Создать прототип основных экранов\n`;
      tasks += `3. Разработать клиентскую часть приложения\n`;
      tasks += `4. Разработать серверную часть\n`;
      tasks += `5. Провести тестирование на различных устройствах\n`;
      tasks += `6. Подготовить приложение к публикации в магазинах`;
    } else if (projectName.includes('маркетинг') || projectName.includes('marketing') || projectName.includes('реклам')) {
      tasks += `1. Провести анализ целевой аудитории\n`;
      tasks += `2. Разработать маркетинговую стратегию\n`;
      tasks += `3. Создать рекламные материалы\n`;
      tasks += `4. Запустить рекламные кампании\n`;
      tasks += `5. Отслеживать эффективность и корректировать стратегию\n`;
      tasks += `6. Подготовить отчет о результатах`;
    } else {
      tasks += `1. Провести стартовое совещание команды\n`;
      tasks += `2. Разработать детальный план проекта\n`;
      tasks += `3. Определить необходимые ресурсы\n`;
      tasks += `4. Создать систему отслеживания прогресса\n`;
      tasks += `5. Настроить инструменты коммуникации\n`;
      tasks += `6. Регулярно проводить обзор прогресса`;
    }
    
    return tasks;
  }
  
  // Общий ответ, если не найдено соответствий
  return `Я могу помочь с проектом "${context.name}". Пожалуйста, уточните ваш вопрос или запрос. Вы можете спросить о задачах, рисках, сроках, бюджете, команде, ресурсах или попросить провести анализ проекта.`;
}

export async function POST(request: Request) {
  try {
    // Получаем данные из запроса
    const data = await request.json();
    const { prompt, context } = data;
    
    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }
    
    if (!context) {
      return NextResponse.json(
        { error: 'Context is required' },
        { status: 400 }
      );
    }
    
    // Генерируем ответ ИИ
    const response = generateAIResponse(prompt, context);
    
    // Возвращаем ответ
    return NextResponse.json({ response });
  } catch (error) {
    console.error('Error in AI route:', error);
    return NextResponse.json(
      { error: 'Failed to process AI request' },
      { status: 500 }
    );
  }
}
