import { NextRequest, NextResponse } from 'next/server';
import { sendTelegramMessage } from '@/lib/telegram-service';

// Обработчик POST-запроса для отправки Telegram-уведомлений
export async function POST(request: NextRequest) {
  try {
    // Получаем данные из запроса
    const data = await request.json();

    // Проверяем наличие необходимых данных
    if (!data.telegramUsername || !data.message) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Формируем сообщение для Telegram
    const messageText = `📬 *${data.subject || 'Уведомление'}*\n\n${data.message}\n\n*Сообщение:* ${data.messageText}\n*От:* ${data.senderName}\n*Чат:* ${data.chatName}\n\n_AI Task Tracker_`;

    // Отправляем сообщение через Telegram Bot API
    const result = await sendTelegramMessage({
      chatId: data.telegramUsername,
      message: messageText,
      parseMode: 'Markdown',
    });

    if (result) {
      return NextResponse.json({
        success: true,
        message: `Telegram notification sent to @${data.telegramUsername}`
      });
    } else {
      return NextResponse.json(
        { success: false, message: 'Failed to send Telegram notification' },
        { status: 500 }
      );
    }

    /*
    // Пример реальной отправки сообщения через Telegram Bot API
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    if (!botToken) {
      throw new Error('TELEGRAM_BOT_TOKEN is not defined');
    }

    // Формируем текст сообщения
    const messageText = `
📬 *${data.subject}*

${data.message}

*Сообщение:* ${data.messageText}
*От:* ${data.senderName}
*Чат:* ${data.chatName}
    `;

    // Отправляем запрос к Telegram Bot API
    const response = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: data.telegramUsername,
        text: messageText,
        parse_mode: 'Markdown',
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Telegram API error: ${JSON.stringify(errorData)}`);
    }

    const responseData = await response.json();

    return NextResponse.json({
      success: true,
      message: `Telegram notification sent to @${data.telegramUsername}`,
      telegramResponse: responseData
    });
    */

  } catch (error) {
    console.error('Error sending Telegram notification:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to send Telegram notification', error: String(error) },
      { status: 500 }
    );
  }
}
