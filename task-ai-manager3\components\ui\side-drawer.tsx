"use client"

import * as React from "react"
import { X } from "lucide-react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const sideDrawerVariants = cva(
  "fixed top-0 bottom-0 z-50 flex flex-col bg-background shadow-xl transition-transform duration-300 ease-in-out",
  {
    variants: {
      side: {
        left: "left-0 border-r rounded-r-lg transform -translate-x-full data-[state=open]:translate-x-0",
        right: "right-0 border-l rounded-l-lg transform translate-x-full data-[state=open]:translate-x-0",
      },
    },
    defaultVariants: {
      side: "right",
    },
  }
)

interface SideDrawerProps extends React.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof sideDrawerVariants> {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: React.ReactNode
}

const SideDrawer = React.forwardRef<HTMLDivElement, SideDrawerProps>(
  ({ className, side, open, onOpenChange, children, ...props }, ref) => {
    return (
      <>
        {open && (
          <div className="fixed inset-0 z-40 bg-black/50" onClick={() => onOpenChange?.(false)} />
        )}
        <div
          ref={ref}
          data-state={open ? "open" : "closed"}
          className={cn(sideDrawerVariants({ side }), className)}
          {...props}
        >
          {children}
        </div>
      </>
    )
  }
)
SideDrawer.displayName = "SideDrawer"

const SideDrawerHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("flex items-center justify-between p-4 border-b", className)}
    {...props}
  />
)
SideDrawerHeader.displayName = "SideDrawerHeader"

const SideDrawerTitle = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) => (
  <h3
    className={cn("text-lg font-semibold", className)}
    {...props}
  />
)
SideDrawerTitle.displayName = "SideDrawerTitle"

const SideDrawerClose = ({
  className,
  onClick,
  ...props
}: React.ButtonHTMLAttributes<HTMLButtonElement>) => (
  <button
    className={cn("rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100", className)}
    onClick={onClick}
    {...props}
  >
    <X className="h-4 w-4" />
    <span className="sr-only">Close</span>
  </button>
)
SideDrawerClose.displayName = "SideDrawerClose"

const SideDrawerContent = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("flex-1 overflow-auto", className)}
    {...props}
  />
)
SideDrawerContent.displayName = "SideDrawerContent"

export {
  SideDrawer,
  SideDrawerHeader,
  SideDrawerTitle,
  SideDrawerClose,
  SideDrawerContent,
}
