#!/usr/bin/env node

/**
 * Скрипт для проверки и отправки напоминаний
 * 
 * Этот скрипт можно запускать по расписанию (например, с помощью cron) для проверки
 * и отправки напоминаний пользователям.
 * 
 * Пример запуска:
 * node scripts/check-reminders.js
 */

const https = require('https');
const http = require('http');

// Настройки API
const API_HOST = process.env.API_HOST || 'localhost';
const API_PORT = process.env.API_PORT || 3000;
const API_PATH = '/api/reminders';
const USE_HTTPS = process.env.USE_HTTPS === 'true';

// Функция для выполнения HTTP-запроса
function makeRequest() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: API_HOST,
      port: API_PORT,
      path: API_PATH,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const protocol = USE_HTTPS ? https : http;

    const req = protocol.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            const parsedData = JSON.parse(data);
            resolve(parsedData);
          } catch (error) {
            reject(new Error(`Error parsing response: ${error.message}`));
          }
        } else {
          reject(new Error(`Request failed with status code ${res.statusCode}: ${data}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Request error: ${error.message}`));
    });

    req.end();
  });
}

// Основная функция
async function main() {
  try {
    console.log('Checking and sending reminders...');
    
    const result = await makeRequest();
    
    console.log('Result:', result);
    console.log('Reminders check completed successfully.');
  } catch (error) {
    console.error('Error checking reminders:', error.message);
    process.exit(1);
  }
}

// Запускаем основную функцию
main();
