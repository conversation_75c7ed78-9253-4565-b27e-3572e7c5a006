"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  History, 
  Mail, 
  MessageCircle, 
  CheckCircle, 
  XCircle, 
  Clock,
  RefreshCw,
  Filter,
  Calendar,
  Task,
  FolderOpen
} from "lucide-react"
import { useTranslation } from "@/lib/translations"
import { getNotificationHistory, NotificationHistory } from "@/lib/enhanced-reminder-service"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface NotificationHistoryProps {
  userId: string
}

export function NotificationHistoryComponent({ userId }: NotificationHistoryProps) {
  const { t } = useTranslation()
  
  // State
  const [history, setHistory] = useState<NotificationHistory[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [filter, setFilter] = useState<'all' | 'email' | 'telegram'>('all')
  const [statusFilter, setStatusFilter] = useState<'all' | 'sent' | 'failed'>('all')
  const [limit, setLimit] = useState(50)
  
  // Load notification history
  const loadHistory = async () => {
    setIsLoading(true)
    try {
      const data = await getNotificationHistory(userId, limit)
      setHistory(data)
    } catch (error) {
      console.error('Error loading notification history:', error)
    } finally {
      setIsLoading(false)
    }
  }
  
  // Load history on component mount
  useEffect(() => {
    loadHistory()
  }, [userId, limit])
  
  // Filter history based on selected filters
  const filteredHistory = history.filter(entry => {
    if (filter !== 'all' && entry.notificationType !== filter) return false
    if (statusFilter !== 'all' && entry.status !== statusFilter) return false
    return true
  })
  
  // Format date for display
  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // Get icon for notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <Mail className="h-4 w-4" />
      case 'telegram':
        return <MessageCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }
  
  // Get icon for item type
  const getItemIcon = (type: string) => {
    switch (type) {
      case 'task':
        return <Task className="h-4 w-4" />
      case 'project':
        return <FolderOpen className="h-4 w-4" />
      case 'event':
        return <Calendar className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'text-green-600'
      case 'failed':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Notification History
        </CardTitle>
        <CardDescription>
          View your recent notification delivery history and status
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span className="text-sm font-medium">Filters:</span>
          </div>
          
          <Select value={filter} onValueChange={(value: any) => setFilter(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="telegram">Telegram</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="sent">Sent</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={limit.toString()} onValueChange={(value) => setLimit(parseInt(value))}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={loadHistory}
            disabled={isLoading}
          >
            {isLoading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh
          </Button>
        </div>
        
        <Separator />
        
        {/* History List */}
        <ScrollArea className="h-96">
          {filteredHistory.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <History className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No notification history found</p>
              {filter !== 'all' || statusFilter !== 'all' ? (
                <p className="text-sm">Try adjusting your filters</p>
              ) : null}
            </div>
          ) : (
            <div className="space-y-3">
              {filteredHistory.map((entry) => (
                <div
                  key={entry.id}
                  className="flex items-start gap-3 p-3 border rounded-md hover:bg-gray-50"
                >
                  {/* Notification Type Icon */}
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(entry.notificationType)}
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      {/* Item Type */}
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        {getItemIcon(entry.itemType)}
                        <span className="capitalize">{entry.itemType}</span>
                      </div>
                      
                      {/* Notification Type Badge */}
                      <Badge variant="outline" className="text-xs">
                        {entry.notificationType}
                      </Badge>
                      
                      {/* Status */}
                      <div className={`flex items-center gap-1 text-sm ${getStatusColor(entry.status)}`}>
                        {entry.status === 'sent' ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : (
                          <XCircle className="h-3 w-3" />
                        )}
                        <span className="capitalize">{entry.status}</span>
                      </div>
                    </div>
                    
                    {/* Item ID */}
                    <p className="text-sm font-medium text-gray-900 truncate">
                      Item ID: {entry.itemId}
                    </p>
                    
                    {/* Timestamp */}
                    <p className="text-xs text-muted-foreground">
                      {formatDate(entry.sentAt)}
                    </p>
                    
                    {/* Error Message */}
                    {entry.error && (
                      <p className="text-xs text-red-600 mt-1 bg-red-50 p-1 rounded">
                        Error: {entry.error}
                      </p>
                    )}
                    
                    {/* Metadata */}
                    {entry.metadata && (
                      <details className="mt-2">
                        <summary className="text-xs text-muted-foreground cursor-pointer">
                          View Details
                        </summary>
                        <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                          {JSON.stringify(entry.metadata, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
        
        {/* Summary */}
        {filteredHistory.length > 0 && (
          <div className="pt-4 border-t">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold">{filteredHistory.length}</p>
                <p className="text-sm text-muted-foreground">Total</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-green-600">
                  {filteredHistory.filter(h => h.status === 'sent').length}
                </p>
                <p className="text-sm text-muted-foreground">Sent</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-red-600">
                  {filteredHistory.filter(h => h.status === 'failed').length}
                </p>
                <p className="text-sm text-muted-foreground">Failed</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
