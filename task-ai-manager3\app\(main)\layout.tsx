"use client"

import React, { useState, useEffect, useCallback } from "react"
import { Sidebar } from "@/components/sidebar"
import { usePathname, useRouter } from 'next/navigation' // Import useRouter
// Remove SocketProvider import - it's now in the root layout

// Mock data/types - replace with actual data fetching/context/state management
// Consider using <PERSON>ust<PERSON>, <PERSON><PERSON>, or React Context for shared state like projects/user
const MOCK_PROJECTS = [
  { id: "proj1", name: "Website Redesign", isPrivate: false },
  { id: "proj2", name: "Mobile App Development", isPrivate: true },
]
const MOCK_USER = { name: "<PERSON>", role: "admin", avatar: "/placeholder-user.jpg" }
const MOCK_CURRENT_PROJECT = MOCK_PROJECTS[0]; // Example

export default function MainAppLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const router = useRouter()
  const [isSidebarOpen, setIsSidebarOpen] = useState(true) // Example state for sidebar visibility

  // Function to determine active tab based on current path
  const getActiveTabFromPath = useCallback(() => {
    if (pathname === '/calendar') return 'calendar';
    if (pathname === '/documents') return 'documents'; // Assuming route exists
    if (pathname === '/analytics') return 'analytics'; // Assuming route exists
    if (pathname === '/ai') return 'ai'; // Assuming route exists
    if (pathname === '/admin') return 'admin';
    if (pathname === '/') {
        // Determine if default page is 'projects' or 'tasks' based on your app logic
        return 'tasks'; // Defaulting to tasks for '/'
    }
    // Add more specific checks if needed, e.g., /projects/id should highlight 'projects'
    if (pathname.startsWith('/projects')) return 'projects';

    return 'tasks'; // Fallback
  }, [pathname])

  const [activeTab, setActiveTab] = useState(getActiveTabFromPath())

  // Update active tab if pathname changes (browser back/forward)
  useEffect(() => {
    setActiveTab(getActiveTabFromPath());
  }, [pathname, getActiveTabFromPath])


  // Handle navigation when a sidebar tab is clicked
  const handleTabChange = (tabId: string) => {
    // setActiveTab(tabId); // Set state immediately for visual feedback (optional)
    switch (tabId) {
      case 'projects':
        router.push('/'); // Or '/projects' if you have a dedicated projects page
        break;
      case 'tasks':
        router.push('/'); // Assuming '/' is the main task board
        break;
      case 'calendar':
        router.push('/calendar');
        break;
      case 'documents':
        router.push('/documents'); // Adjust route if needed
        break;
      case 'analytics':
        router.push('/analytics'); // Adjust route if needed
        break;
      case 'ai':
        router.push('/ai'); // Adjust route if needed
        break;
      case 'admin':
        router.push('/admin');
        break;
      default:
        router.push('/'); // Fallback to home/tasks
    }
  }

  // TODO: Fetch actual projects and user data using state management or server components
  const projects = MOCK_PROJECTS
  const user = MOCK_USER
  const currentProject = MOCK_CURRENT_PROJECT // This should also be dynamic state

  // TODO: Implement project selection logic
  const handleProjectSelect = (project: any) => {
    console.log("Selected project:", project.name)
    // Update currentProject state, potentially fetch new data
  }

  return (
    // Remove SocketProvider wrapper - it's now in the root layout
    <div className="flex h-screen overflow-hidden bg-secondary/50 dark:bg-background">
      {/* TODO: Add a button/mechanism to toggle isSidebarOpen */}
      <Sidebar
        isOpen={isSidebarOpen}
        activeTab={activeTab}
        onTabChange={handleTabChange} // Use the navigation handler
        projects={projects}
        currentProject={currentProject} // Pass current project
        onProjectSelect={handleProjectSelect} // Pass project selection handler
        user={user}
      />
      <main className="flex-1 overflow-y-auto">
        {/* Render the active page component determined by Next.js routing */}
        {children}
      </main>
    </div>
  )
}
