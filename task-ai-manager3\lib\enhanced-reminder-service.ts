import { sendReminderEmail, ReminderData } from './email-service';
import { sendTelegramReminder, TelegramReminderData } from './telegram-service';

// Enhanced interfaces for comprehensive reminder system
export interface UserReminderPreferences {
  userId: string;
  emailReminders: boolean;
  telegramReminders: boolean;
  emailForReminders: string;
  telegramChatId: string;
  reminderTimes: number[]; // minutes before due date [1440, 60, 15] = 1 day, 1 hour, 15 minutes
  timezone: string;
  taskReminders: boolean;
  projectReminders: boolean;
  calendarReminders: boolean;
  overdueReminders: boolean;
  reminderFrequency: 'once' | 'daily' | 'hourly';
  quietHours: {
    enabled: boolean;
    start: string; // "22:00"
    end: string;   // "08:00"
  };
}

export interface EnhancedReminder {
  id: string;
  userId: string;
  itemType: 'event' | 'task' | 'subtask' | 'project';
  itemId: string;
  reminderTime: Date;
  sent: boolean;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  updatedAt: Date;
  sentAt?: Date;
  error?: string;
  reminderType: 'deadline' | 'overdue' | 'milestone' | 'calendar';
  priority: 'low' | 'medium' | 'high';
}

export interface ReminderQueue {
  id: string;
  reminder: EnhancedReminder;
  scheduledTime: Date;
  status: 'pending' | 'processing' | 'sent' | 'failed' | 'cancelled';
  attempts: number;
  lastAttempt?: Date;
  nextRetry?: Date;
  error?: string;
}

export interface NotificationHistory {
  id: string;
  userId: string;
  itemType: string;
  itemId: string;
  notificationType: 'email' | 'telegram';
  status: 'sent' | 'failed';
  sentAt: Date;
  error?: string;
  metadata?: any;
}

// In-memory storage for development (replace with database in production)
let reminderQueue: ReminderQueue[] = [];
let notificationHistory: NotificationHistory[] = [];
let userPreferences: Map<string, UserReminderPreferences> = new Map();

// Default user preferences
export function getDefaultUserPreferences(userId: string): UserReminderPreferences {
  return {
    userId,
    emailReminders: false,
    telegramReminders: false,
    emailForReminders: '',
    telegramChatId: '',
    reminderTimes: [1440, 60, 15], // 1 day, 1 hour, 15 minutes before
    timezone: 'UTC',
    taskReminders: true,
    projectReminders: true,
    calendarReminders: true,
    overdueReminders: true,
    reminderFrequency: 'once',
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00'
    }
  };
}

// Get user reminder preferences
export function getUserReminderPreferences(userId: string): UserReminderPreferences {
  if (typeof window !== 'undefined') {
    // Client-side: load from localStorage
    const stored = localStorage.getItem(`reminderPrefs_${userId}`);
    if (stored) {
      try {
        return { ...getDefaultUserPreferences(userId), ...JSON.parse(stored) };
      } catch (error) {
        console.error('Error parsing stored preferences:', error);
      }
    }
  }
  
  // Server-side or fallback: use in-memory storage or defaults
  return userPreferences.get(userId) || getDefaultUserPreferences(userId);
}

// Save user reminder preferences
export function saveUserReminderPreferences(preferences: UserReminderPreferences): void {
  if (typeof window !== 'undefined') {
    // Client-side: save to localStorage
    localStorage.setItem(`reminderPrefs_${preferences.userId}`, JSON.stringify(preferences));
  }
  
  // Server-side: save to in-memory storage (replace with database in production)
  userPreferences.set(preferences.userId, preferences);
}

// Create multiple reminders based on user preferences
export async function createRemindersForItem(
  userId: string,
  itemType: 'event' | 'task' | 'subtask' | 'project',
  itemId: string,
  dueDate: Date,
  priority: 'low' | 'medium' | 'high' = 'medium'
): Promise<EnhancedReminder[]> {
  const preferences = getUserReminderPreferences(userId);
  const reminders: EnhancedReminder[] = [];
  
  // Check if reminders are enabled for this item type
  const isEnabled = (
    (itemType === 'task' && preferences.taskReminders) ||
    (itemType === 'project' && preferences.projectReminders) ||
    (itemType === 'event' && preferences.calendarReminders) ||
    (itemType === 'subtask' && preferences.taskReminders)
  );
  
  if (!isEnabled || (!preferences.emailReminders && !preferences.telegramReminders)) {
    return reminders;
  }
  
  // Create reminders for each configured time
  for (const minutesBefore of preferences.reminderTimes) {
    const reminderTime = new Date(dueDate.getTime() - (minutesBefore * 60 * 1000));
    
    // Skip if reminder time is in the past
    if (reminderTime <= new Date()) {
      continue;
    }
    
    // Adjust for quiet hours if enabled
    const adjustedTime = adjustForQuietHours(reminderTime, preferences);
    
    const reminder: EnhancedReminder = {
      id: `reminder-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      userId,
      itemType,
      itemId,
      reminderTime: adjustedTime,
      sent: false,
      retryCount: 0,
      maxRetries: 3,
      createdAt: new Date(),
      updatedAt: new Date(),
      reminderType: 'deadline',
      priority
    };
    
    reminders.push(reminder);
    
    // Add to queue
    addToReminderQueue(reminder);
  }
  
  return reminders;
}

// Adjust reminder time for quiet hours
function adjustForQuietHours(reminderTime: Date, preferences: UserReminderPreferences): Date {
  if (!preferences.quietHours.enabled) {
    return reminderTime;
  }
  
  const time = reminderTime.toTimeString().substr(0, 5); // "HH:MM"
  const startTime = preferences.quietHours.start;
  const endTime = preferences.quietHours.end;
  
  // Simple check if time falls within quiet hours
  if (startTime > endTime) {
    // Quiet hours span midnight (e.g., 22:00 to 08:00)
    if (time >= startTime || time <= endTime) {
      // Move to end of quiet hours
      const adjusted = new Date(reminderTime);
      const [endHour, endMinute] = endTime.split(':').map(Number);
      adjusted.setHours(endHour, endMinute, 0, 0);
      if (adjusted <= reminderTime) {
        adjusted.setDate(adjusted.getDate() + 1);
      }
      return adjusted;
    }
  } else {
    // Quiet hours within same day
    if (time >= startTime && time <= endTime) {
      const adjusted = new Date(reminderTime);
      const [endHour, endMinute] = endTime.split(':').map(Number);
      adjusted.setHours(endHour, endMinute, 0, 0);
      return adjusted;
    }
  }
  
  return reminderTime;
}

// Add reminder to queue
function addToReminderQueue(reminder: EnhancedReminder): void {
  const queueItem: ReminderQueue = {
    id: `queue-${reminder.id}`,
    reminder,
    scheduledTime: reminder.reminderTime,
    status: 'pending',
    attempts: 0
  };
  
  reminderQueue.push(queueItem);
  console.log(`Added reminder ${reminder.id} to queue for ${reminder.reminderTime.toISOString()}`);
}

// Process reminder queue
export async function processReminderQueue(): Promise<void> {
  const now = new Date();
  const pendingReminders = reminderQueue.filter(
    item => item.status === 'pending' && item.scheduledTime <= now
  );
  
  console.log(`Processing ${pendingReminders.length} pending reminders`);
  
  for (const queueItem of pendingReminders) {
    queueItem.status = 'processing';
    queueItem.attempts++;
    queueItem.lastAttempt = now;
    
    try {
      const success = await sendEnhancedReminder(queueItem.reminder);
      
      if (success) {
        queueItem.status = 'sent';
        queueItem.reminder.sent = true;
        queueItem.reminder.sentAt = now;
        queueItem.reminder.updatedAt = now;
      } else {
        throw new Error('Failed to send reminder');
      }
    } catch (error) {
      console.error(`Error sending reminder ${queueItem.reminder.id}:`, error);
      
      queueItem.error = error instanceof Error ? error.message : 'Unknown error';
      queueItem.reminder.error = queueItem.error;
      queueItem.reminder.retryCount++;
      queueItem.reminder.updatedAt = now;
      
      if (queueItem.reminder.retryCount >= queueItem.reminder.maxRetries) {
        queueItem.status = 'failed';
        console.error(`Reminder ${queueItem.reminder.id} failed after ${queueItem.reminder.maxRetries} attempts`);
      } else {
        // Schedule retry
        queueItem.status = 'pending';
        queueItem.nextRetry = new Date(now.getTime() + (queueItem.reminder.retryCount * 5 * 60 * 1000)); // Exponential backoff
        queueItem.scheduledTime = queueItem.nextRetry;
        console.log(`Scheduled retry for reminder ${queueItem.reminder.id} at ${queueItem.nextRetry.toISOString()}`);
      }
    }
  }
}

// Send enhanced reminder with both email and Telegram support
async function sendEnhancedReminder(reminder: EnhancedReminder): Promise<boolean> {
  const preferences = getUserReminderPreferences(reminder.userId);
  let emailSent = false;
  let telegramSent = false;
  
  // Get item details
  const itemDetails = await getItemDetails(reminder.itemType, reminder.itemId);
  if (!itemDetails) {
    throw new Error(`Item ${reminder.itemId} not found`);
  }
  
  // Send email reminder
  if (preferences.emailReminders && preferences.emailForReminders) {
    try {
      const emailData = createEmailReminderData(reminder, itemDetails, preferences);
      emailSent = await sendReminderEmail(emailData);
      
      // Log to history
      addToNotificationHistory({
        id: `email-${Date.now()}`,
        userId: reminder.userId,
        itemType: reminder.itemType,
        itemId: reminder.itemId,
        notificationType: 'email',
        status: emailSent ? 'sent' : 'failed',
        sentAt: new Date(),
        error: emailSent ? undefined : 'Failed to send email'
      });
    } catch (error) {
      console.error('Error sending email reminder:', error);
    }
  }
  
  // Send Telegram reminder
  if (preferences.telegramReminders && preferences.telegramChatId) {
    try {
      const telegramData = createTelegramReminderData(reminder, itemDetails, preferences);
      telegramSent = await sendTelegramReminder(telegramData);
      
      // Log to history
      addToNotificationHistory({
        id: `telegram-${Date.now()}`,
        userId: reminder.userId,
        itemType: reminder.itemType,
        itemId: reminder.itemId,
        notificationType: 'telegram',
        status: telegramSent ? 'sent' : 'failed',
        sentAt: new Date(),
        error: telegramSent ? undefined : 'Failed to send Telegram message'
      });
    } catch (error) {
      console.error('Error sending Telegram reminder:', error);
    }
  }
  
  return emailSent || telegramSent;
}

// Get item details based on type and ID
async function getItemDetails(itemType: string, itemId: string): Promise<any> {
  // In a real application, this would fetch from database
  // For now, return mock data or fetch from localStorage
  
  if (typeof window !== 'undefined') {
    try {
      switch (itemType) {
        case 'task':
          const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
          return tasks.find((task: any) => task.id === itemId);
        case 'project':
          const projects = JSON.parse(localStorage.getItem('projects') || '[]');
          return projects.find((project: any) => project.id === itemId);
        case 'event':
          const events = JSON.parse(localStorage.getItem('events') || '[]');
          return events.find((event: any) => event.id === itemId);
        default:
          return null;
      }
    } catch (error) {
      console.error('Error fetching item details:', error);
      return null;
    }
  }
  
  // Server-side fallback
  return {
    id: itemId,
    title: `${itemType} ${itemId}`,
    description: `Description for ${itemType} ${itemId}`
  };
}

// Create email reminder data
function createEmailReminderData(
  reminder: EnhancedReminder,
  itemDetails: any,
  preferences: UserReminderPreferences
): ReminderData {
  const urgencyMap = {
    'low': '📅',
    'medium': '⚠️',
    'high': '🚨'
  };
  
  const urgencyPrefix = urgencyMap[reminder.priority];
  
  return {
    userId: reminder.userId,
    userEmail: preferences.emailForReminders,
    userName: 'User', // Would be fetched from user data
    subject: `${urgencyPrefix} Reminder: ${itemDetails.title}`,
    message: `You have a ${reminder.itemType} "${itemDetails.title}" that requires your attention.`,
    itemType: reminder.itemType as any,
    itemId: reminder.itemId,
    itemTitle: itemDetails.title,
    dueDate: new Date(itemDetails.dueDate || Date.now()),
    projectName: itemDetails.projectName
  };
}

// Create Telegram reminder data
function createTelegramReminderData(
  reminder: EnhancedReminder,
  itemDetails: any,
  preferences: UserReminderPreferences
): TelegramReminderData {
  const urgencyMap = {
    'low': '📅',
    'medium': '⚠️',
    'high': '🚨'
  };
  
  const urgencyPrefix = urgencyMap[reminder.priority];
  
  return {
    userId: reminder.userId,
    telegramChatId: preferences.telegramChatId,
    userName: 'User',
    subject: `${urgencyPrefix} Reminder`,
    message: `You have a ${reminder.itemType} "${itemDetails.title}" that requires your attention.`,
    itemType: reminder.itemType as any,
    itemId: reminder.itemId,
    itemTitle: itemDetails.title,
    dueDate: new Date(itemDetails.dueDate || Date.now()),
    projectName: itemDetails.projectName
  };
}

// Add to notification history
function addToNotificationHistory(entry: NotificationHistory): void {
  notificationHistory.push(entry);
  
  // Keep only last 1000 entries
  if (notificationHistory.length > 1000) {
    notificationHistory = notificationHistory.slice(-1000);
  }
}

// Get notification history for user
export function getNotificationHistory(userId: string, limit: number = 50): NotificationHistory[] {
  return notificationHistory
    .filter(entry => entry.userId === userId)
    .sort((a, b) => b.sentAt.getTime() - a.sentAt.getTime())
    .slice(0, limit);
}

// Clean up old reminders and history
export function cleanupOldData(): void {
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  
  // Remove old completed/failed reminders from queue
  reminderQueue = reminderQueue.filter(
    item => item.status === 'pending' || 
           (item.lastAttempt && item.lastAttempt > oneWeekAgo)
  );
  
  // Remove old notification history
  notificationHistory = notificationHistory.filter(
    entry => entry.sentAt > oneWeekAgo
  );
  
  console.log(`Cleaned up old reminder data. Queue size: ${reminderQueue.length}, History size: ${notificationHistory.length}`);
}
