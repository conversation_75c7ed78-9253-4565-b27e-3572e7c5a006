# Настройка системы уведомлений и напоминаний

## Обзор

Система AI Task Tracker включает в себя комплексную систему уведомлений и напоминаний, поддерживающую:
- Email-уведомления через SMTP
- Telegram-уведомления через Bot API
- Автоматические напоминания о дедлайнах
- Настраиваемые интервалы напоминаний
- Уведомления о новых сообщениях в чатах

## Установка зависимостей

```bash
npm install nodemailer @types/nodemailer node-cron
```

## Настройка переменных окружения

Создайте файл `.env.local` в корне проекта:

```env
# Email Configuration (SMTP)
SMTP_SERVER="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your-app-password"
FROM_EMAIL="<EMAIL>"

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"

# OpenAI API Key (для AI-функций)
OPENAI_API_KEY="your-openai-api-key"

# Application Settings
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

## Настройка Email (SMTP)

### Gmail
1. Включите двухфакторную аутентификацию в Google аккаунте
2. Создайте пароль приложения:
   - Перейдите в настройки Google аккаунта
   - Безопасность → Пароли приложений
   - Создайте новый пароль для "Почта"
3. Используйте этот пароль в `SMTP_PASSWORD`

### Другие провайдеры
- **Outlook/Hotmail**: `smtp-mail.outlook.com:587`
- **Yahoo**: `smtp.mail.yahoo.com:587`
- **Yandex**: `smtp.yandex.ru:465`

## Настройка Telegram Bot

### Создание бота
1. Найдите @BotFather в Telegram
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания бота
4. Получите токен бота и добавьте в `TELEGRAM_BOT_TOKEN`

### Получение Chat ID
1. Добавьте бота в чат или начните диалог
2. Отправьте сообщение боту
3. Перейдите по ссылке: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
4. Найдите `chat.id` в ответе

## Использование системы

### Настройка пользователя
1. Перейдите в Профиль → Настройки напоминаний
2. Включите нужные типы уведомлений
3. Укажите email и/или Telegram username
4. Настройте время напоминаний (за сколько до дедлайна)
5. Протестируйте соединение с Telegram

### Автоматические напоминания
Система автоматически проверяет дедлайны каждые 5 минут (настраивается) и отправляет напоминания:
- За день до дедлайна
- За час до дедлайна  
- За 15 минут до дедлайна
- Пользовательские интервалы

### Уведомления о чатах
При получении нового сообщения в чате система отправляет уведомления всем участникам проекта/задачи.

## Администрирование

### Панель администратора
Доступна по адресу `/admin` и включает:
- Мониторинг статуса системы напоминаний
- Управление планировщиком
- Статистика уведомлений
- Тестирование API

### API эндпоинты
- `GET /api/reminders?action=check-all` - Проверить все напоминания
- `POST /api/notifications/email` - Отправить email
- `POST /api/notifications/telegram` - Отправить Telegram сообщение

## Устранение неполадок

### Email не отправляются
1. Проверьте настройки SMTP
2. Убедитесь, что используете пароль приложения (для Gmail)
3. Проверьте логи в консоли браузера

### Telegram не работает
1. Проверьте токен бота
2. Убедитесь, что бот добавлен в чат
3. Проверьте правильность Chat ID
4. Используйте кнопку "Тест" в настройках

### Напоминания не приходят
1. Проверьте, включены ли напоминания в настройках
2. Убедитесь, что планировщик запущен (админ-панель)
3. Проверьте, что у задач/проектов указаны дедлайны

## Безопасность

- Никогда не коммитьте файл `.env.local` в репозиторий
- Используйте пароли приложений вместо основных паролей
- Регулярно обновляйте токены и пароли
- Ограничьте права доступа к админ-панели

## Производительность

- Система использует localStorage для хранения данных
- Планировщик работает в фоновом режиме
- Уведомления отправляются асинхронно
- Рекомендуется интервал проверки 5-15 минут

## Масштабирование

Для продакшена рекомендуется:
- Использовать базу данных вместо localStorage
- Настроить очереди для уведомлений
- Добавить логирование и мониторинг
- Использовать внешний планировщик задач (cron)
