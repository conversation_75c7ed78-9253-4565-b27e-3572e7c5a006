"use client";

import { useEffect, useCallback } from 'react';
import { createRemindersForItem, getUserReminderPreferences } from '@/lib/enhanced-reminder-service';
import { startReminderScheduler, isReminderSchedulerRunning } from '@/lib/reminder-scheduler';

// Hook for integrating reminders with task management
export function useReminderIntegration() {
  
  // Initialize reminder scheduler on app start
  useEffect(() => {
    if (!isReminderSchedulerRunning()) {
      startReminderScheduler();
    }
  }, []);
  
  // Function to schedule reminders for a task
  const scheduleTaskReminders = useCallback(async (
    task: {
      id: string;
      title: string;
      dueDate?: string;
      priority?: 'low' | 'medium' | 'high';
      assignee?: { id: string; name: string };
    }
  ) => {
    if (!task.dueDate || !task.assignee?.id) {
      return;
    }
    
    try {
      const preferences = getUserReminderPreferences(task.assignee.id);
      
      // Only create reminders if user has them enabled
      if (preferences.emailReminders || preferences.telegramReminders) {
        await createRemindersForItem(
          task.assignee.id,
          'task',
          task.id,
          new Date(task.dueDate),
          task.priority || 'medium'
        );
        
        console.log(`Scheduled reminders for task: ${task.title}`);
      }
    } catch (error) {
      console.error('Error scheduling task reminders:', error);
    }
  }, []);
  
  // Function to schedule reminders for a project
  const scheduleProjectReminders = useCallback(async (
    project: {
      id: string;
      name: string;
      dueDate?: string;
      priority?: 'low' | 'medium' | 'high';
      owner?: { id: string; name: string };
    }
  ) => {
    if (!project.dueDate || !project.owner?.id) {
      return;
    }
    
    try {
      const preferences = getUserReminderPreferences(project.owner.id);
      
      if (preferences.projectReminders && (preferences.emailReminders || preferences.telegramReminders)) {
        await createRemindersForItem(
          project.owner.id,
          'project',
          project.id,
          new Date(project.dueDate),
          project.priority || 'medium'
        );
        
        console.log(`Scheduled reminders for project: ${project.name}`);
      }
    } catch (error) {
      console.error('Error scheduling project reminders:', error);
    }
  }, []);
  
  // Function to schedule reminders for a calendar event
  const scheduleEventReminders = useCallback(async (
    event: {
      id: string;
      title: string;
      start: string;
      attendees?: Array<{ id: string; name: string }>;
    }
  ) => {
    if (!event.start || !event.attendees?.length) {
      return;
    }
    
    try {
      // Schedule reminders for all attendees
      for (const attendee of event.attendees) {
        const preferences = getUserReminderPreferences(attendee.id);
        
        if (preferences.calendarReminders && (preferences.emailReminders || preferences.telegramReminders)) {
          await createRemindersForItem(
            attendee.id,
            'event',
            event.id,
            new Date(event.start),
            'medium'
          );
        }
      }
      
      console.log(`Scheduled reminders for event: ${event.title}`);
    } catch (error) {
      console.error('Error scheduling event reminders:', error);
    }
  }, []);
  
  // Function to schedule reminders for a subtask
  const scheduleSubtaskReminders = useCallback(async (
    subtask: {
      id: string;
      title: string;
      dueDate?: string;
      priority?: 'low' | 'medium' | 'high';
      assignee?: { id: string; name: string };
    }
  ) => {
    if (!subtask.dueDate || !subtask.assignee?.id) {
      return;
    }
    
    try {
      const preferences = getUserReminderPreferences(subtask.assignee.id);
      
      // Subtasks use task reminder settings
      if (preferences.taskReminders && (preferences.emailReminders || preferences.telegramReminders)) {
        await createRemindersForItem(
          subtask.assignee.id,
          'subtask',
          subtask.id,
          new Date(subtask.dueDate),
          subtask.priority || 'medium'
        );
        
        console.log(`Scheduled reminders for subtask: ${subtask.title}`);
      }
    } catch (error) {
      console.error('Error scheduling subtask reminders:', error);
    }
  }, []);
  
  // Function to handle task updates (reschedule reminders)
  const handleTaskUpdate = useCallback(async (
    oldTask: any,
    newTask: any
  ) => {
    // If due date or assignee changed, reschedule reminders
    if (
      oldTask.dueDate !== newTask.dueDate ||
      oldTask.assignee?.id !== newTask.assignee?.id
    ) {
      // Cancel old reminders (in a real app, this would remove from database)
      console.log(`Rescheduling reminders for updated task: ${newTask.title}`);
      
      // Schedule new reminders
      await scheduleTaskReminders(newTask);
    }
  }, [scheduleTaskReminders]);
  
  // Function to handle project updates
  const handleProjectUpdate = useCallback(async (
    oldProject: any,
    newProject: any
  ) => {
    if (
      oldProject.dueDate !== newProject.dueDate ||
      oldProject.owner?.id !== newProject.owner?.id
    ) {
      console.log(`Rescheduling reminders for updated project: ${newProject.name}`);
      await scheduleProjectReminders(newProject);
    }
  }, [scheduleProjectReminders]);
  
  // Function to handle event updates
  const handleEventUpdate = useCallback(async (
    oldEvent: any,
    newEvent: any
  ) => {
    if (
      oldEvent.start !== newEvent.start ||
      JSON.stringify(oldEvent.attendees) !== JSON.stringify(newEvent.attendees)
    ) {
      console.log(`Rescheduling reminders for updated event: ${newEvent.title}`);
      await scheduleEventReminders(newEvent);
    }
  }, [scheduleEventReminders]);
  
  // Function to bulk schedule reminders for existing items
  const bulkScheduleReminders = useCallback(async () => {
    try {
      console.log('Starting bulk reminder scheduling...');
      
      // Get all items from localStorage
      const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
      const projects = JSON.parse(localStorage.getItem('projects') || '[]');
      const events = JSON.parse(localStorage.getItem('events') || '[]');
      
      // Schedule reminders for tasks
      for (const task of tasks) {
        if (task.dueDate && task.assignee?.id && !task.completed) {
          await scheduleTaskReminders(task);
        }
        
        // Schedule for subtasks
        if (task.subtasks) {
          for (const subtask of task.subtasks) {
            if (subtask.dueDate && subtask.assignee?.id && !subtask.completed) {
              await scheduleSubtaskReminders(subtask);
            }
          }
        }
      }
      
      // Schedule reminders for projects
      for (const project of projects) {
        if (project.dueDate && project.owner?.id && project.status !== 'completed') {
          await scheduleProjectReminders(project);
        }
      }
      
      // Schedule reminders for events
      for (const event of events) {
        if (event.start && event.attendees?.length) {
          await scheduleEventReminders(event);
        }
      }
      
      console.log('Bulk reminder scheduling completed');
    } catch (error) {
      console.error('Error in bulk reminder scheduling:', error);
    }
  }, [scheduleTaskReminders, scheduleProjectReminders, scheduleEventReminders, scheduleSubtaskReminders]);
  
  // Auto-schedule reminders for existing items on app start
  useEffect(() => {
    const timer = setTimeout(() => {
      bulkScheduleReminders();
    }, 3000); // Wait 3 seconds after app start
    
    return () => clearTimeout(timer);
  }, [bulkScheduleReminders]);
  
  return {
    scheduleTaskReminders,
    scheduleProjectReminders,
    scheduleEventReminders,
    scheduleSubtaskReminders,
    handleTaskUpdate,
    handleProjectUpdate,
    handleEventUpdate,
    bulkScheduleReminders
  };
}

// Hook for monitoring reminder preferences changes
export function useReminderPreferencesSync(userId: string) {
  useEffect(() => {
    const handlePreferencesChange = () => {
      // Restart scheduler when preferences change
      if (isReminderSchedulerRunning()) {
        startReminderScheduler();
      }
    };
    
    // Listen for preference changes
    window.addEventListener('reminderPreferencesChanged', handlePreferencesChange);
    
    return () => {
      window.removeEventListener('reminderPreferencesChanged', handlePreferencesChange);
    };
  }, [userId]);
}

// Utility function to trigger preference change event
export function triggerPreferencesChange() {
  const event = new CustomEvent('reminderPreferencesChanged');
  window.dispatchEvent(event);
}
