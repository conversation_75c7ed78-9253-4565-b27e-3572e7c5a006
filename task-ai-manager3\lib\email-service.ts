"use server";

import nodemailer from 'nodemailer';
// import { prisma } from './prisma'; // Закомментировано до настройки БД

// Интерфейс для настроек SMTP
interface EmailSettings {
  smtpServer: string;
  smtpPort: string;
  smtpUsername: string;
  smtpPassword: string;
  fromEmail: string;
}

// Интерфейс для данных напоминания
export interface ReminderData {
  userId: string;
  userEmail: string;
  userName: string;
  subject: string;
  message: string;
  itemType: 'event' | 'task' | 'subtask' | 'project';
  itemId: string;
  itemTitle: string;
  dueDate?: Date;
  projectName?: string;
}

// Получение настроек SMTP из базы данных или из переменных окружения
async function getEmailSettings(): Promise<EmailSettings> {
  // В реальном приложении здесь будет запрос к базе данных для получения настроек
  // Для примера используем переменные окружения
  return {
    smtpServer: process.env.SMTP_SERVER || '',
    smtpPort: process.env.SMTP_PORT || '587',
    smtpUsername: process.env.SMTP_USERNAME || '',
    smtpPassword: process.env.SMTP_PASSWORD || '',
    fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
  };
}

// Создание транспорта для отправки писем
async function createTransport() {
  const settings = await getEmailSettings();

  // Проверяем, что все необходимые настройки указаны
  if (!settings.smtpServer || !settings.smtpUsername || !settings.smtpPassword) {
    throw new Error('SMTP settings are not configured');
  }

  return nodemailer.createTransport({
    host: settings.smtpServer,
    port: parseInt(settings.smtpPort, 10),
    secure: parseInt(settings.smtpPort, 10) === 465, // true для порта 465, false для других портов
    auth: {
      user: settings.smtpUsername,
      pass: settings.smtpPassword,
    },
  });
}

// Функция для отправки напоминания по электронной почте
export async function sendReminderEmail(reminderData: ReminderData): Promise<boolean> {
  try {
    // Получаем настройки электронной почты
    const settings = await getEmailSettings();

    // Проверяем, что все необходимые настройки указаны
    if (!settings.smtpServer || !settings.smtpUsername || !settings.smtpPassword) {
      console.error('SMTP settings are not configured');
      return false;
    }

    // Проверяем, что у пользователя есть email
    if (!reminderData.userEmail) {
      console.error(`User ${reminderData.userId} does not have an email address`);
      return false;
    }

    // Создаем транспорт
    const transporter = await createTransport();

    // Формируем HTML для письма
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Напоминание</h2>
        <p>Здравствуйте, ${reminderData.userName || 'пользователь'}!</p>
        <p>${reminderData.message}</p>
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #333;">${reminderData.itemTitle}</h3>
          ${reminderData.projectName ? `<p><strong>Проект:</strong> ${reminderData.projectName}</p>` : ''}
          ${reminderData.dueDate ? `<p><strong>Срок:</strong> ${reminderData.dueDate.toLocaleDateString('ru-RU')}</p>` : ''}
        </div>
        <p>С уважением,<br>Команда AI Task Tracker</p>
      </div>
    `;

    // Отправляем письмо
    const info = await transporter.sendMail({
      from: `"AI Task Tracker" <${settings.fromEmail}>`,
      to: reminderData.userEmail,
      subject: reminderData.subject,
      html: htmlContent,
    });

    console.log(`Email sent to ${reminderData.userEmail}: ${info.messageId}`);

    // Записываем информацию об отправленном напоминании в лог
    await logReminderSent(reminderData);

    return true;
  } catch (error) {
    console.error('Error sending reminder email:', error);
    return false;
  }
}

// Функция для записи информации об отправленном напоминании в лог
async function logReminderSent(reminderData: ReminderData): Promise<void> {
  try {
    // В реальном приложении здесь будет запись в базу данных
    console.log(`Reminder sent to ${reminderData.userEmail} for ${reminderData.itemType} "${reminderData.itemTitle}"`);

    // Пример записи в базу данных (закомментировано, так как модель не существует)
    /*
    await prisma.reminderLog.create({
      data: {
        userId: reminderData.userId,
        itemType: reminderData.itemType,
        itemId: reminderData.itemId,
        sentAt: new Date(),
        success: true,
      },
    });
    */
  } catch (error) {
    console.error('Error logging reminder:', error);
  }
}
