// Comprehensive test suite for the reminder and notification system
// Run this in the browser console to test all reminder functionality

function testReminderSystem() {
  console.log('🔔 Testing Reminder and Notification System...');
  
  // Test data
  const testUserId = 'test-user-123';
  const testTask = {
    id: 'task-test-1',
    title: 'Test Task with Deadline',
    description: 'This is a test task for reminder system',
    dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
    priority: 'high',
    assignee: {
      id: testUserId,
      name: 'Test User'
    }
  };
  
  const testProject = {
    id: 'project-test-1',
    name: 'Test Project',
    description: 'Test project for reminders',
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Next week
    priority: 'medium',
    owner: {
      id: testUserId,
      name: 'Test User'
    }
  };
  
  // Test 1: User Preferences Management
  console.log('📋 Test 1: User Preferences Management');
  testUserPreferences(testUserId);
  
  // Test 2: Reminder Creation
  console.log('⏰ Test 2: Reminder Creation');
  testReminderCreation(testUserId, testTask, testProject);
  
  // Test 3: API Endpoints
  console.log('🌐 Test 3: API Endpoints');
  testAPIEndpoints(testUserId);
  
  // Test 4: Scheduler Functions
  console.log('⚙️ Test 4: Scheduler Functions');
  testSchedulerFunctions();
  
  // Test 5: Email and Telegram Integration
  console.log('📧 Test 5: Email and Telegram Integration');
  testNotificationIntegration(testUserId);
  
  console.log('✅ Reminder System Tests Completed');
}

// Test user preferences management
function testUserPreferences(userId) {
  try {
    // Test getting default preferences
    const defaultPrefs = getUserReminderPreferences(userId);
    console.log('✅ Default preferences loaded:', {
      emailReminders: defaultPrefs.emailReminders,
      telegramReminders: defaultPrefs.telegramReminders,
      reminderTimes: defaultPrefs.reminderTimes
    });
    
    // Test saving preferences
    const testPrefs = {
      ...defaultPrefs,
      emailReminders: true,
      telegramReminders: true,
      emailForReminders: '<EMAIL>',
      telegramChatId: '123456789',
      reminderTimes: [1440, 60, 15], // 1 day, 1 hour, 15 minutes
      taskReminders: true,
      projectReminders: true,
      calendarReminders: true,
      overdueReminders: true
    };
    
    saveUserReminderPreferences(testPrefs);
    console.log('✅ Preferences saved successfully');
    
    // Test loading saved preferences
    const loadedPrefs = getUserReminderPreferences(userId);
    console.log('✅ Preferences loaded after save:', {
      emailReminders: loadedPrefs.emailReminders,
      telegramReminders: loadedPrefs.telegramReminders,
      emailForReminders: loadedPrefs.emailForReminders
    });
    
  } catch (error) {
    console.error('❌ User preferences test failed:', error);
  }
}

// Test reminder creation
function testReminderCreation(userId, task, project) {
  try {
    // Test task reminder creation
    createRemindersForItem(
      userId,
      'task',
      task.id,
      new Date(task.dueDate),
      task.priority
    ).then(reminders => {
      console.log('✅ Task reminders created:', reminders.length);
    }).catch(error => {
      console.error('❌ Task reminder creation failed:', error);
    });
    
    // Test project reminder creation
    createRemindersForItem(
      userId,
      'project',
      project.id,
      new Date(project.dueDate),
      project.priority
    ).then(reminders => {
      console.log('✅ Project reminders created:', reminders.length);
    }).catch(error => {
      console.error('❌ Project reminder creation failed:', error);
    });
    
  } catch (error) {
    console.error('❌ Reminder creation test failed:', error);
  }
}

// Test API endpoints
function testAPIEndpoints(userId) {
  // Test reminder status endpoint
  fetch('/api/reminders?action=status')
    .then(response => response.json())
    .then(data => {
      console.log('✅ Reminder status API:', data.success ? 'Working' : 'Failed');
    })
    .catch(error => {
      console.error('❌ Reminder status API failed:', error);
    });
  
  // Test preferences endpoint
  fetch(`/api/reminders?action=preferences&userId=${userId}`)
    .then(response => response.json())
    .then(data => {
      console.log('✅ Preferences API:', data.success ? 'Working' : 'Failed');
    })
    .catch(error => {
      console.error('❌ Preferences API failed:', error);
    });
  
  // Test history endpoint
  fetch(`/api/reminders?action=history&userId=${userId}&limit=10`)
    .then(response => response.json())
    .then(data => {
      console.log('✅ History API:', data.success ? 'Working' : 'Failed');
    })
    .catch(error => {
      console.error('❌ History API failed:', error);
    });
  
  // Test reminder creation endpoint
  fetch('/api/reminders', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action: 'create-reminder',
      userId: userId,
      itemType: 'task',
      itemId: 'test-task-api',
      dueDate: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
      priority: 'medium'
    })
  })
    .then(response => response.json())
    .then(data => {
      console.log('✅ Create reminder API:', data.success ? 'Working' : 'Failed');
    })
    .catch(error => {
      console.error('❌ Create reminder API failed:', error);
    });
}

// Test scheduler functions
function testSchedulerFunctions() {
  try {
    // Test scheduler status
    const isRunning = isReminderSchedulerRunning();
    console.log('✅ Scheduler status:', isRunning ? 'Running' : 'Stopped');
    
    // Test manual queue processing
    fetch('/api/reminders?action=check-queue')
      .then(response => response.json())
      .then(data => {
        console.log('✅ Manual queue processing:', data.success ? 'Working' : 'Failed');
      })
      .catch(error => {
        console.error('❌ Manual queue processing failed:', error);
      });
    
  } catch (error) {
    console.error('❌ Scheduler functions test failed:', error);
  }
}

// Test notification integration
function testNotificationIntegration(userId) {
  try {
    // Test email service
    const emailData = {
      userId: userId,
      userEmail: '<EMAIL>',
      userName: 'Test User',
      subject: 'Test Reminder',
      message: 'This is a test reminder message',
      itemType: 'task',
      itemId: 'test-task-email',
      itemTitle: 'Test Task',
      dueDate: new Date(),
      projectName: 'Test Project'
    };
    
    console.log('📧 Email service test data prepared:', emailData.subject);
    
    // Test Telegram service
    const telegramData = {
      userId: userId,
      telegramChatId: '123456789',
      userName: 'Test User',
      subject: 'Test Reminder',
      message: 'This is a test Telegram reminder',
      itemType: 'task',
      itemId: 'test-task-telegram',
      itemTitle: 'Test Task',
      dueDate: new Date(),
      projectName: 'Test Project'
    };
    
    console.log('📱 Telegram service test data prepared:', telegramData.subject);
    
    // Test notification history
    const history = getNotificationHistory(userId, 10);
    console.log('✅ Notification history:', `${history.length} entries found`);
    
  } catch (error) {
    console.error('❌ Notification integration test failed:', error);
  }
}

// Test reminder integration hook functionality
function testReminderIntegration() {
  console.log('🔗 Testing Reminder Integration...');
  
  const testTask = {
    id: 'integration-task-1',
    title: 'Integration Test Task',
    dueDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
    priority: 'high',
    assignee: {
      id: 'test-user-integration',
      name: 'Integration Test User'
    }
  };
  
  // Simulate task creation with reminder scheduling
  console.log('📝 Simulating task creation with reminders...');
  
  // This would normally be called by the useReminderIntegration hook
  if (typeof scheduleTaskReminders === 'function') {
    scheduleTaskReminders(testTask)
      .then(() => {
        console.log('✅ Task reminders scheduled via integration');
      })
      .catch(error => {
        console.error('❌ Task reminder integration failed:', error);
      });
  } else {
    console.log('ℹ️ scheduleTaskReminders function not available (normal in test environment)');
  }
}

// Performance test for reminder system
function testReminderPerformance() {
  console.log('⚡ Testing Reminder System Performance...');
  
  const startTime = performance.now();
  const testUserId = 'perf-test-user';
  
  // Create multiple reminders
  const promises = [];
  for (let i = 0; i < 10; i++) {
    const promise = createRemindersForItem(
      testUserId,
      'task',
      `perf-task-${i}`,
      new Date(Date.now() + (i + 1) * 60 * 60 * 1000), // Staggered hours
      'medium'
    );
    promises.push(promise);
  }
  
  Promise.all(promises)
    .then(results => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.log(`✅ Performance test completed: ${results.length} reminder sets created in ${duration.toFixed(2)}ms`);
    })
    .catch(error => {
      console.error('❌ Performance test failed:', error);
    });
}

// Export test functions for individual testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testReminderSystem,
    testUserPreferences,
    testReminderCreation,
    testAPIEndpoints,
    testSchedulerFunctions,
    testNotificationIntegration,
    testReminderIntegration,
    testReminderPerformance
  };
}

// Auto-run tests if in browser
if (typeof window !== 'undefined') {
  console.log('🚀 Reminder System Test Suite Ready!');
  console.log('Run testReminderSystem() to test all functionality');
  console.log('Run individual test functions for specific components');
  console.log('Available tests:');
  console.log('- testUserPreferences()');
  console.log('- testReminderCreation()');
  console.log('- testAPIEndpoints()');
  console.log('- testSchedulerFunctions()');
  console.log('- testNotificationIntegration()');
  console.log('- testReminderIntegration()');
  console.log('- testReminderPerformance()');
}
