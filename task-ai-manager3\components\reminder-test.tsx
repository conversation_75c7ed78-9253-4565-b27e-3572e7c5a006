"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { 
  TestTube, 
  CheckCircle, 
  XCircle, 
  Clock,
  Mail,
  MessageCircle,
  Bell
} from "lucide-react"
import { 
  createRemindersForItem,
  getUserReminderPreferences,
  saveUserReminderPreferences,
  processReminderQueue,
  getNotificationHistory
} from "@/lib/enhanced-reminder-service"
import { 
  isReminderSchedulerRunning,
  startReminderScheduler,
  manualReminderCheck
} from "@/lib/reminder-scheduler"

export function ReminderTest() {
  const { toast } = useToast()
  const [testResults, setTestResults] = useState<Record<string, 'pending' | 'success' | 'error'>>({})
  const [isRunning, setIsRunning] = useState(false)
  
  const testUserId = 'test-user-123'
  
  const runTest = async (testName: string, testFn: () => Promise<void>) => {
    setTestResults(prev => ({ ...prev, [testName]: 'pending' }))
    
    try {
      await testFn()
      setTestResults(prev => ({ ...prev, [testName]: 'success' }))
      return true
    } catch (error) {
      console.error(`Test ${testName} failed:`, error)
      setTestResults(prev => ({ ...prev, [testName]: 'error' }))
      return false
    }
  }
  
  const runAllTests = async () => {
    setIsRunning(true)
    setTestResults({})
    
    let passedTests = 0
    let totalTests = 0
    
    // Test 1: User Preferences
    totalTests++
    const test1Passed = await runTest('preferences', async () => {
      // Test default preferences
      const defaultPrefs = getUserReminderPreferences(testUserId)
      if (!defaultPrefs || typeof defaultPrefs.userId !== 'string') {
        throw new Error('Failed to get default preferences')
      }
      
      // Test saving preferences
      const testPrefs = {
        ...defaultPrefs,
        emailReminders: true,
        telegramReminders: true,
        emailForReminders: '<EMAIL>',
        telegramChatId: '*********',
        reminderTimes: [60, 15] // 1 hour, 15 minutes
      }
      
      saveUserReminderPreferences(testPrefs)
      
      // Test loading saved preferences
      const loadedPrefs = getUserReminderPreferences(testUserId)
      if (!loadedPrefs.emailReminders || loadedPrefs.emailForReminders !== '<EMAIL>') {
        throw new Error('Failed to save/load preferences')
      }
    })
    if (test1Passed) passedTests++
    
    // Test 2: Reminder Creation
    totalTests++
    const test2Passed = await runTest('reminderCreation', async () => {
      const dueDate = new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now
      
      const reminders = await createRemindersForItem(
        testUserId,
        'task',
        'test-task-123',
        dueDate,
        'high'
      )
      
      if (!Array.isArray(reminders) || reminders.length === 0) {
        throw new Error('Failed to create reminders')
      }
    })
    if (test2Passed) passedTests++
    
    // Test 3: Scheduler Status
    totalTests++
    const test3Passed = await runTest('scheduler', async () => {
      const isRunning = isReminderSchedulerRunning()
      
      if (!isRunning) {
        // Try to start scheduler
        startReminderScheduler()
        
        // Check again
        const isRunningAfterStart = isReminderSchedulerRunning()
        if (!isRunningAfterStart) {
          throw new Error('Failed to start reminder scheduler')
        }
      }
    })
    if (test3Passed) passedTests++
    
    // Test 4: Queue Processing
    totalTests++
    const test4Passed = await runTest('queueProcessing', async () => {
      await processReminderQueue()
      // If no error thrown, consider it successful
    })
    if (test4Passed) passedTests++
    
    // Test 5: Manual Check
    totalTests++
    const test5Passed = await runTest('manualCheck', async () => {
      await manualReminderCheck()
      // If no error thrown, consider it successful
    })
    if (test5Passed) passedTests++
    
    // Test 6: Notification History
    totalTests++
    const test6Passed = await runTest('notificationHistory', async () => {
      const history = getNotificationHistory(testUserId, 10)
      if (!Array.isArray(history)) {
        throw new Error('Failed to get notification history')
      }
    })
    if (test6Passed) passedTests++
    
    setIsRunning(false)
    
    // Show results
    toast({
      title: "Test Results",
      description: `${passedTests}/${totalTests} tests passed`,
      variant: passedTests === totalTests ? "default" : "destructive"
    })
  }
  
  const getTestIcon = (status: 'pending' | 'success' | 'error' | undefined) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 animate-spin" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <TestTube className="h-4 w-4" />
    }
  }
  
  const getTestBadge = (status: 'pending' | 'success' | 'error' | undefined) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">Running...</Badge>
      case 'success':
        return <Badge variant="default" className="bg-green-600">Passed</Badge>
      case 'error':
        return <Badge variant="destructive">Failed</Badge>
      default:
        return <Badge variant="outline">Not Run</Badge>
    }
  }
  
  const tests = [
    {
      id: 'preferences',
      name: 'User Preferences',
      description: 'Test preference saving and loading'
    },
    {
      id: 'reminderCreation',
      name: 'Reminder Creation',
      description: 'Test creating reminders for items'
    },
    {
      id: 'scheduler',
      name: 'Scheduler Status',
      description: 'Test reminder scheduler functionality'
    },
    {
      id: 'queueProcessing',
      name: 'Queue Processing',
      description: 'Test reminder queue processing'
    },
    {
      id: 'manualCheck',
      name: 'Manual Check',
      description: 'Test manual reminder checking'
    },
    {
      id: 'notificationHistory',
      name: 'Notification History',
      description: 'Test notification history retrieval'
    }
  ]
  
  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Reminder System Test Suite
        </CardTitle>
        <CardDescription>
          Test the reminder and notification system functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={runAllTests} 
            disabled={isRunning}
            className="flex items-center gap-2"
          >
            <TestTube className="h-4 w-4" />
            {isRunning ? 'Running Tests...' : 'Run All Tests'}
          </Button>
          
          <Button 
            variant="outline"
            onClick={() => {
              setTestResults({})
              toast({
                title: "Tests Reset",
                description: "Test results have been cleared"
              })
            }}
          >
            Reset
          </Button>
        </div>
        
        <div className="space-y-3">
          {tests.map((test) => (
            <div 
              key={test.id}
              className="flex items-center justify-between p-3 border rounded-md"
            >
              <div className="flex items-center gap-3">
                {getTestIcon(testResults[test.id])}
                <div>
                  <p className="font-medium">{test.name}</p>
                  <p className="text-sm text-muted-foreground">{test.description}</p>
                </div>
              </div>
              {getTestBadge(testResults[test.id])}
            </div>
          ))}
        </div>
        
        <div className="pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div className="flex items-center justify-center gap-2">
              <Mail className="h-4 w-4" />
              <span className="text-sm">Email Service</span>
            </div>
            <div className="flex items-center justify-center gap-2">
              <MessageCircle className="h-4 w-4" />
              <span className="text-sm">Telegram Service</span>
            </div>
          </div>
        </div>
        
        <div className="text-xs text-muted-foreground">
          <p>
            This test suite verifies the core functionality of the reminder system.
            For full email and Telegram testing, configure your credentials in the settings.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
