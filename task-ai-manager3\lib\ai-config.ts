import { openai } from "@ai-sdk/openai"

// AI Configuration Interface
export interface AIConfig {
  enabled: boolean
  model: string
  apiKey: string
  temperature: number
  maxTokens: number
  customPrompt: string
}

// Default AI Configuration
const DEFAULT_AI_CONFIG: AIConfig = {
  enabled: true,
  model: "gpt-4o",
  apiKey: process.env.OPENAI_API_KEY || "",
  temperature: 0.7,
  maxTokens: 1000,
  customPrompt: "You are an AI assistant for a task management application. Help users organize their tasks and projects effectively."
}

// In-memory cache for settings (replace with database in production)
let cachedSettings: AIConfig | null = null
let lastFetchTime = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

/**
 * Get AI configuration from admin settings with fallback to environment variables
 */
export async function getAIConfig(): Promise<AIConfig> {
  // Check cache first
  const now = Date.now()
  if (cachedSettings && (now - lastFetchTime) < CACHE_DURATION) {
    return cachedSettings
  }

  try {
    // Try to fetch from database/settings API
    const response = await fetch('/api/settings/ai', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.ok) {
      const settings = await response.json()
      cachedSettings = {
        enabled: settings.enableAI ?? DEFAULT_AI_CONFIG.enabled,
        model: settings.aiModel || DEFAULT_AI_CONFIG.model,
        apiKey: settings.aiApiKey || DEFAULT_AI_CONFIG.apiKey,
        temperature: settings.aiTemperature ?? DEFAULT_AI_CONFIG.temperature,
        maxTokens: settings.maxTokens ?? DEFAULT_AI_CONFIG.maxTokens,
        customPrompt: settings.customPrompt || DEFAULT_AI_CONFIG.customPrompt,
      }
      lastFetchTime = now
      return cachedSettings
    }
  } catch (error) {
    console.warn('Failed to fetch AI settings from API, using defaults:', error)
  }

  // Fallback to default configuration
  cachedSettings = DEFAULT_AI_CONFIG
  lastFetchTime = now
  return cachedSettings
}

/**
 * Get AI configuration synchronously (for server-side usage)
 * Falls back to environment variables and defaults
 */
export function getAIConfigSync(): AIConfig {
  return {
    enabled: true,
    model: process.env.AI_MODEL || "gpt-4o",
    apiKey: process.env.OPENAI_API_KEY || "",
    temperature: parseFloat(process.env.AI_TEMPERATURE || "0.7"),
    maxTokens: parseInt(process.env.AI_MAX_TOKENS || "1000"),
    customPrompt: process.env.AI_CUSTOM_PROMPT || DEFAULT_AI_CONFIG.customPrompt,
  }
}

/**
 * Create OpenAI client with current configuration
 */
export async function createOpenAIClient() {
  const config = await getAIConfig()
  
  if (!config.apiKey) {
    throw new Error('OpenAI API key is not configured')
  }

  return openai(config.model, {
    apiKey: config.apiKey,
  })
}

/**
 * Create OpenAI client synchronously
 */
export function createOpenAIClientSync() {
  const config = getAIConfigSync()
  
  if (!config.apiKey) {
    throw new Error('OpenAI API key is not configured')
  }

  return openai(config.model, {
    apiKey: config.apiKey,
  })
}

/**
 * Clear configuration cache (useful when settings are updated)
 */
export function clearAIConfigCache() {
  cachedSettings = null
  lastFetchTime = 0
}

/**
 * Get system prompt based on language and custom configuration
 */
export async function getSystemPrompt(isRussian: boolean = false): Promise<string> {
  const config = await getAIConfig()
  
  // Use custom prompt if available
  if (config.customPrompt && config.customPrompt !== DEFAULT_AI_CONFIG.customPrompt) {
    return config.customPrompt
  }

  // Default language-specific prompts
  return isRussian
    ? `Ты - ИИ-ассистент по управлению задачами, который действует как продукт-менеджер.
       Твоя задача - помогать пользователю в планировании, организации и выполнении задач.
       Ты должен быть проактивным, предлагать решения и разбивать большие цели на конкретные задачи.
       Отвечай на русском языке, используй профессиональную терминологию из области управления проектами.`
    : `You are an AI task management assistant that acts as a product manager.
       Your job is to help the user plan, organize, and complete tasks.
       You should be proactive, suggest solutions, and break down big goals into specific tasks.
       Respond in English, using professional terminology from the project management field.`
}

/**
 * Validate AI configuration
 */
export function validateAIConfig(config: Partial<AIConfig>): string[] {
  const errors: string[] = []

  if (config.temperature !== undefined && (config.temperature < 0 || config.temperature > 1)) {
    errors.push('Temperature must be between 0 and 1')
  }

  if (config.maxTokens !== undefined && (config.maxTokens < 1 || config.maxTokens > 4000)) {
    errors.push('Max tokens must be between 1 and 4000')
  }

  if (config.model && !['gpt-4o', 'gpt-4', 'gpt-3.5-turbo'].includes(config.model)) {
    errors.push('Unsupported AI model')
  }

  return errors
}
