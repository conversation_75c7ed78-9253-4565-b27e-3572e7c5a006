import { NextRequest, NextResponse } from 'next/server'
import { clearAIConfigCache, validateAIConfig } from '@/lib/ai-config'

// In-memory storage for development (replace with database in production)
let systemSettings = {
  // General Settings
  siteName: "AI Task Tracker",
  siteDescription: "AI-powered task management system",
  allowRegistration: true,
  requireEmailVerification: true,
  maxProjectsPerUser: 10,
  maxTasksPerProject: 50,
  
  // AI Settings
  enableAI: true,
  aiModel: "gpt-4o",
  aiApiKey: process.env.OPENAI_API_KEY || "",
  aiTemperature: 0.7,
  maxTokens: 1000,
  customPrompt: "You are an AI assistant for a task management application. Help users organize their tasks and projects effectively.",
  
  // Email Settings
  enableEmailNotifications: false,
  smtpServer: process.env.SMTP_SERVER || "",
  smtpPort: process.env.SMTP_PORT || "587",
  smtpUsername: process.env.SMTP_USERNAME || "",
  smtpPassword: process.env.SMTP_PASSWORD || "",
  fromEmail: process.env.FROM_EMAIL || "<EMAIL>",
}

/**
 * GET /api/settings - Retrieve all system settings
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')

    // Return specific category if requested
    if (category) {
      switch (category) {
        case 'ai':
          return NextResponse.json({
            enableAI: systemSettings.enableAI,
            aiModel: systemSettings.aiModel,
            aiApiKey: systemSettings.aiApiKey,
            aiTemperature: systemSettings.aiTemperature,
            maxTokens: systemSettings.maxTokens,
            customPrompt: systemSettings.customPrompt,
          })
        case 'general':
          return NextResponse.json({
            siteName: systemSettings.siteName,
            siteDescription: systemSettings.siteDescription,
            allowRegistration: systemSettings.allowRegistration,
            requireEmailVerification: systemSettings.requireEmailVerification,
            maxProjectsPerUser: systemSettings.maxProjectsPerUser,
            maxTasksPerProject: systemSettings.maxTasksPerProject,
          })
        case 'email':
          return NextResponse.json({
            enableEmailNotifications: systemSettings.enableEmailNotifications,
            smtpServer: systemSettings.smtpServer,
            smtpPort: systemSettings.smtpPort,
            smtpUsername: systemSettings.smtpUsername,
            smtpPassword: systemSettings.smtpPassword,
            fromEmail: systemSettings.fromEmail,
          })
        default:
          return NextResponse.json(
            { error: 'Invalid category' },
            { status: 400 }
          )
      }
    }

    // Return all settings (excluding sensitive data)
    const publicSettings = {
      ...systemSettings,
      aiApiKey: systemSettings.aiApiKey ? '***' : '', // Mask API key
      smtpPassword: systemSettings.smtpPassword ? '***' : '', // Mask password
    }

    return NextResponse.json(publicSettings)
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/settings - Update system settings
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { category, settings } = body

    if (!category || !settings) {
      return NextResponse.json(
        { error: 'Category and settings are required' },
        { status: 400 }
      )
    }

    // Validate and update settings by category
    switch (category) {
      case 'general':
        // Validate general settings
        if (settings.maxProjectsPerUser && (settings.maxProjectsPerUser < 1 || settings.maxProjectsPerUser > 100)) {
          return NextResponse.json(
            { error: 'Max projects per user must be between 1 and 100' },
            { status: 400 }
          )
        }
        if (settings.maxTasksPerProject && (settings.maxTasksPerProject < 1 || settings.maxTasksPerProject > 1000)) {
          return NextResponse.json(
            { error: 'Max tasks per project must be between 1 and 1000' },
            { status: 400 }
          )
        }

        // Update general settings
        systemSettings = {
          ...systemSettings,
          siteName: settings.siteName || systemSettings.siteName,
          siteDescription: settings.siteDescription || systemSettings.siteDescription,
          allowRegistration: settings.allowRegistration ?? systemSettings.allowRegistration,
          requireEmailVerification: settings.requireEmailVerification ?? systemSettings.requireEmailVerification,
          maxProjectsPerUser: settings.maxProjectsPerUser ?? systemSettings.maxProjectsPerUser,
          maxTasksPerProject: settings.maxTasksPerProject ?? systemSettings.maxTasksPerProject,
        }
        break

      case 'ai':
        // Validate AI settings
        const aiErrors = validateAIConfig(settings)
        if (aiErrors.length > 0) {
          return NextResponse.json(
            { error: aiErrors.join(', ') },
            { status: 400 }
          )
        }

        // Update AI settings
        systemSettings = {
          ...systemSettings,
          enableAI: settings.enableAI ?? systemSettings.enableAI,
          aiModel: settings.aiModel || systemSettings.aiModel,
          aiApiKey: settings.aiApiKey || systemSettings.aiApiKey,
          aiTemperature: settings.aiTemperature ?? systemSettings.aiTemperature,
          maxTokens: settings.maxTokens ?? systemSettings.maxTokens,
          customPrompt: settings.customPrompt || systemSettings.customPrompt,
        }

        // Clear AI config cache when settings change
        clearAIConfigCache()
        break

      case 'email':
        // Update email settings
        systemSettings = {
          ...systemSettings,
          enableEmailNotifications: settings.enableEmailNotifications ?? systemSettings.enableEmailNotifications,
          smtpServer: settings.smtpServer || systemSettings.smtpServer,
          smtpPort: settings.smtpPort || systemSettings.smtpPort,
          smtpUsername: settings.smtpUsername || systemSettings.smtpUsername,
          smtpPassword: settings.smtpPassword || systemSettings.smtpPassword,
          fromEmail: settings.fromEmail || systemSettings.fromEmail,
        }
        break

      default:
        return NextResponse.json(
          { error: 'Invalid category' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: `${category} settings updated successfully`,
      category,
    })
  } catch (error) {
    console.error('Error updating settings:', error)
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/settings - Replace all settings (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    const newSettings = await request.json()

    // Validate all settings
    const aiErrors = validateAIConfig({
      temperature: newSettings.aiTemperature,
      maxTokens: newSettings.maxTokens,
      model: newSettings.aiModel,
    })

    if (aiErrors.length > 0) {
      return NextResponse.json(
        { error: aiErrors.join(', ') },
        { status: 400 }
      )
    }

    // Replace all settings
    systemSettings = {
      ...systemSettings,
      ...newSettings,
    }

    // Clear AI config cache
    clearAIConfigCache()

    return NextResponse.json({
      success: true,
      message: 'All settings updated successfully',
    })
  } catch (error) {
    console.error('Error replacing settings:', error)
    return NextResponse.json(
      { error: 'Failed to replace settings' },
      { status: 500 }
    )
  }
}
