"use server";

import { createReminder, sendReminder } from './reminder-service';

// Интерфейс для события календаря
interface CalendarEvent {
  id: string | number;
  title: string;
  start: Date;
  end: Date;
  projectId?: string;
  projectName?: string;
  taskId?: string;
  subtaskId?: string;
  type: 'meeting' | 'task' | 'deadline' | 'reminder' | 'subtask';
  description?: string;
  reminder?: boolean;
  reminderTime?: Date;
  assignee?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

// Функция для создания напоминания для события календаря
export async function createCalendarReminder(event: CalendarEvent, userId: string): Promise<boolean> {
  try {
    // Проверяем, что событие имеет настройки напоминания
    if (!event.reminder || !event.reminderTime) {
      console.log(`Event ${event.id} does not have reminder settings`);
      return false;
    }
    
    // Определяем тип элемента для напоминания
    let itemType: 'event' | 'task' | 'subtask' | 'project';
    let itemId: string;
    
    switch (event.type) {
      case 'task':
        itemType = 'task';
        itemId = event.taskId as string;
        break;
      case 'subtask':
        itemType = 'subtask';
        itemId = event.subtaskId as string;
        break;
      case 'meeting':
      case 'deadline':
      case 'reminder':
      default:
        itemType = 'event';
        itemId = String(event.id);
        break;
    }
    
    // Создаем напоминание
    const reminder = await createReminder(
      userId,
      itemType,
      itemId,
      event.reminderTime
    );
    
    return !!reminder;
  } catch (error) {
    console.error('Error creating calendar reminder:', error);
    return false;
  }
}

// Функция для обработки напоминаний для всех событий календаря
export async function processCalendarReminders(events: CalendarEvent[], userId: string): Promise<void> {
  try {
    // Фильтруем события, для которых нужно создать напоминания
    const eventsWithReminders = events.filter(event => 
      event.reminder && 
      event.reminderTime && 
      new Date(event.reminderTime) > new Date()
    );
    
    console.log(`Found ${eventsWithReminders.length} events with reminders`);
    
    // Создаем напоминания для каждого события
    for (const event of eventsWithReminders) {
      await createCalendarReminder(event, userId);
    }
  } catch (error) {
    console.error('Error processing calendar reminders:', error);
  }
}

// Функция для обновления напоминания при изменении события
export async function updateCalendarReminder(event: CalendarEvent, userId: string): Promise<boolean> {
  try {
    // В реальном приложении здесь будет удаление старого напоминания и создание нового
    // Для примера просто создаем новое напоминание
    return await createCalendarReminder(event, userId);
  } catch (error) {
    console.error('Error updating calendar reminder:', error);
    return false;
  }
}
