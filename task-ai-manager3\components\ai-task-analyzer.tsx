"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogDescription, Di<PERSON>Footer, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Brain, TrendingUp, AlertTriangle, Target, Clock, Users } from "lucide-react"
import { useTranslation } from "@/lib/translations"

interface AITaskAnalyzerProps {
  task: any
  open: boolean
  onOpenChange: (open: boolean) => void
  allTasks?: any[]
}

export function AITaskAnalyzer({
  task,
  open,
  onOpenChange,
  allTasks = []
}: AITaskAnalyzerProps) {
  const { t } = useTranslation()
  const { toast } = useToast()
  
  // States
  const [isLoading, setIsLoading] = useState(false)
  const [analysis, setAnalysis] = useState<any>(null)
  const [customPrompt, setCustomPrompt] = useState("")
  const [analysisType, setAnalysisType] = useState<'full' | 'risks' | 'optimization' | 'planning'>('full')
  
  // Auto-analyze when dialog opens
  useEffect(() => {
    if (open && task) {
      performAnalysis('full')
    }
  }, [open, task])
  
  // Perform AI analysis
  const performAnalysis = async (type: string = 'full') => {
    if (!task) return
    
    setIsLoading(true)
    setAnalysisType(type as any)
    
    try {
      const prompt = generatePromptByType(type)
      
      const response = await fetch('/api/ai/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt,
          context: {
            currentTask: task,
            tasks: allTasks
          }
        }),
      })

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`)
      }

      const data = await response.json()
      
      setAnalysis({
        type: type,
        content: data.response,
        timestamp: new Date().toISOString()
      })
      
    } catch (error) {
      console.error("Error performing analysis:", error)
      
      // Fallback analysis
      setAnalysis({
        type: type,
        content: generateFallbackAnalysis(task, type),
        timestamp: new Date().toISOString()
      })
      
      toast({
        title: "Анализ выполнен в базовом режиме",
        description: "AI сервис недоступен, использован упрощенный анализ.",
        variant: "default"
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  // Generate prompt based on analysis type
  const generatePromptByType = (type: string): string => {
    switch (type) {
      case 'risks':
        return 'Проанализируй риски и потенциальные проблемы этой задачи'
      case 'optimization':
        return 'Как можно оптимизировать выполнение этой задачи?'
      case 'planning':
        return 'Создай детальный план выполнения этой задачи'
      case 'custom':
        return customPrompt || 'Проанализируй эту задачу'
      default:
        return 'Проведи полный анализ этой задачи'
    }
  }
  
  // Fallback analysis generation
  const generateFallbackAnalysis = (task: any, type: string): string => {
    const baseInfo = `Анализ задачи: "${task.title}"
    
Статус: ${task.status}
Приоритет: ${task.priority}
Срок выполнения: ${task.dueDate || 'Не установлен'}
Подзадачи: ${task.subtasks?.length || 0} шт.`

    switch (type) {
      case 'risks':
        return `${baseInfo}

🚨 АНАЛИЗ РИСКОВ:

• Отсутствие четкого дедлайна может привести к затягиванию
• Недостаточная детализация может усложнить выполнение
• Высокий приоритет требует немедленного внимания
• Необходимо назначить ответственного исполнителя

Рекомендации по снижению рисков:
1. Установите конкретные сроки
2. Разбейте на более мелкие подзадачи
3. Назначьте ответственного
4. Создайте план контрольных точек`

      case 'optimization':
        return `${baseInfo}

⚡ РЕКОМЕНДАЦИИ ПО ОПТИМИЗАЦИИ:

• Используйте технику "помидора" для фокусированной работы
• Группируйте похожие активности
• Автоматизируйте повторяющиеся действия
• Устраните отвлекающие факторы
• Делегируйте менее критичные части

Инструменты для повышения эффективности:
1. Планировщики задач
2. Таймеры для отслеживания времени
3. Шаблоны для стандартных процессов
4. Инструменты автоматизации`

      case 'planning':
        return `${baseInfo}

📋 ПЛАН ВЫПОЛНЕНИЯ:

Этап 1: Подготовка (20% времени)
• Анализ требований
• Планирование ресурсов
• Подготовка инструментов

Этап 2: Основная работа (60% времени)
• Выполнение ключевых задач
• Регулярные проверки прогресса
• Корректировка плана при необходимости

Этап 3: Завершение (20% времени)
• Тестирование результатов
• Документирование
• Финальная проверка`

      default:
        return `${baseInfo}

📊 ПОЛНЫЙ АНАЛИЗ:

Сложность: Средняя
Время выполнения: 4-8 часов
Необходимые ресурсы: Стандартные

Рекомендации:
• Начните с детального планирования
• Разбейте на подзадачи по 2-4 часа
• Установите промежуточные контрольные точки
• Регулярно обновляйте статус выполнения

Следующие шаги:
1. Уточните требования
2. Создайте план работы
3. Начните выполнение
4. Отслеживайте прогресс`
    }
  }
  
  // Custom analysis with user prompt
  const performCustomAnalysis = async () => {
    if (!customPrompt.trim()) {
      toast({
        title: "Введите запрос",
        description: "Пожалуйста, введите ваш вопрос для анализа.",
        variant: "default"
      })
      return
    }
    
    await performAnalysis('custom')
    setCustomPrompt("")
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Brain className="h-5 w-5 mr-2 text-primary" />
            AI Анализ задачи: "{task?.title}"
          </DialogTitle>
          <DialogDescription>
            Получите детальный анализ задачи с рекомендациями по выполнению
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* Analysis Type Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={analysisType === 'full' ? 'default' : 'outline'}
              size="sm"
              onClick={() => performAnalysis('full')}
              disabled={isLoading}
            >
              <Target className="h-4 w-4 mr-1" />
              Полный анализ
            </Button>
            <Button
              variant={analysisType === 'risks' ? 'default' : 'outline'}
              size="sm"
              onClick={() => performAnalysis('risks')}
              disabled={isLoading}
            >
              <AlertTriangle className="h-4 w-4 mr-1" />
              Анализ рисков
            </Button>
            <Button
              variant={analysisType === 'optimization' ? 'default' : 'outline'}
              size="sm"
              onClick={() => performAnalysis('optimization')}
              disabled={isLoading}
            >
              <TrendingUp className="h-4 w-4 mr-1" />
              Оптимизация
            </Button>
            <Button
              variant={analysisType === 'planning' ? 'default' : 'outline'}
              size="sm"
              onClick={() => performAnalysis('planning')}
              disabled={isLoading}
            >
              <Clock className="h-4 w-4 mr-1" />
              Планирование
            </Button>
          </div>
          
          {/* Custom Analysis Input */}
          <div className="space-y-2">
            <div className="flex gap-2">
              <Textarea
                placeholder="Задайте свой вопрос об этой задаче..."
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                className="flex-1"
                rows={2}
              />
              <Button
                onClick={performCustomAnalysis}
                disabled={isLoading || !customPrompt.trim()}
                size="sm"
              >
                Анализ
              </Button>
            </div>
          </div>
          
          {/* Analysis Results */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Анализирую задачу...
                  </>
                ) : (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Результаты анализа
                  </>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      AI анализирует задачу и готовит рекомендации...
                    </p>
                  </div>
                </div>
              ) : analysis ? (
                <div className="space-y-4">
                  <div className="prose prose-sm max-w-none">
                    <div className="whitespace-pre-line text-sm">
                      {analysis.content}
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground border-t pt-2">
                    Анализ выполнен: {new Date(analysis.timestamp).toLocaleString('ru-RU')}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Brain className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Выберите тип анализа или задайте свой вопрос</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Закрыть
          </Button>
          {analysis && (
            <Button onClick={() => performAnalysis(analysisType)}>
              Обновить анализ
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
