"use client"

import React, { useEffect, useState, useRef } from 'react'
import { format } from "date-fns"
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Trash2 } from 'lucide-react'
import { useToast } from "@/components/ui/use-toast"
import { useTranslation } from "@/lib/translations"

// Типы для событий и задач
type EventType = 'task' | 'subtask' | 'meeting' | 'deadline' | 'reminder';
type TaskStatus = 'todo' | 'in-progress' | 'done';

// Интерфейс для исполнителя
interface Assignee {
  id: string;
  name: string;
  avatar?: string;
}

// Интерфейс для подзадачи
interface Subtask {
  id: string;
  title: string;
  completed: boolean;
  description?: string;
  dueDate?: string;
  priority?: string;
  assignee?: string;
}

interface CalendarEvent {
  id: number | string;
  title: string;
  start: Date;
  end: Date;
  projectId?: string;
  projectName?: string;
  taskId?: string;
  subtaskId?: string;
  type: EventType;
  description?: string;
  bgColor: string;
  isCompleted?: boolean;
  status?: TaskStatus;
  priority?: 'low' | 'medium' | 'high';
  reminder?: boolean;
  reminderTime?: Date;
  assignee?: Assignee;
  responsible?: Assignee; // Добавляем ответственного за задачу/подзадачу
  subtasks?: Subtask[];
  tags?: string[];
}

// Функция для создания цвета в зависимости от типа события и приоритета
const getEventColor = (type: EventType, priority?: 'low' | 'medium' | 'high') => {
  switch (type) {
    case 'task':
      return priority === 'high' ? '#f43f5e' : priority === 'medium' ? '#f97316' : '#22c55e';
    case 'subtask':
      return priority === 'high' ? '#ef4444' : priority === 'medium' ? '#fb923c' : '#4ade80';
    case 'meeting':
      return '#3b82f6';
    case 'deadline':
      return '#dc2626';
    case 'reminder':
      return '#a855f7';
    default:
      return '#3b82f6';
  }
};

interface EventDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  event: CalendarEvent | null;
  isNew: boolean;
  onSave: (event: CalendarEvent) => void;
  onDelete: () => void;
  onOpenDeleteDialog: () => void;
  projects: any[];
}

export default function EventDialog({
  isOpen,
  onOpenChange,
  event,
  isNew,
  onSave,
  onDelete,
  onOpenDeleteDialog,
  projects = []
}: EventDialogProps) {
  const { toast } = useToast();
  const { t } = useTranslation();
  const [editedEvent, setEditedEvent] = useState<CalendarEvent | null>(null);

  // Обновляем editedEvent при изменении event или при открытии диалога
  // Используем useRef для отслеживания предыдущего состояния, чтобы избежать циклических обновлений
  const prevEventIdRef = useRef<string | null>(null);
  const prevIsOpenRef = useRef<boolean>(false);

  useEffect(() => {
    // Проверяем, что изменилось событие или состояние диалога
    const eventId = event?.id?.toString() || null;
    if ((eventId !== prevEventIdRef.current || isOpen !== prevIsOpenRef.current) &&
        event && isOpen) {
      console.log('Updating editedEvent from event in dialog');
      setEditedEvent({...event});
    }

    // Обновляем ссылки на предыдущие значения
    prevEventIdRef.current = eventId;
    prevIsOpenRef.current = isOpen;
  }, [event, isOpen]);

  // Проекты для фильтрации
  const projectOptions = [
    { id: 'none', name: 'Без проекта' },
    ...projects.map(p => ({ id: p.id, name: p.name })),
  ];

  const handleChange = (field: keyof CalendarEvent, value: any) => {
    try {
      // Если поле - дата, убедимся, что она является объектом Date
      if (field === 'start' || field === 'end') {
        value = value instanceof Date ? value : new Date(value);
      }

      setEditedEvent(prev => prev ? ({ ...prev, [field]: value }) : null);
    } catch (error) {
      console.error(`Error updating ${field}:`, error);
      toast({
        title: 'Ошибка',
        description: `Не удалось обновить поле ${field}`,
        variant: 'destructive'
      });
    }
  };

  const handleSave = () => {
    if (!editedEvent) return;
    onSave(editedEvent);
  };

  if (!event || !editedEvent) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isNew ? 'Создать событие' : 'Редактировать событие'}</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="title">{t("eventTitle")}</Label>
            <Input
              id="title"
              value={editedEvent.title}
              onChange={(e) => handleChange('title', e.target.value)}
              placeholder={t("eventTitlePlaceholder")}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label>{t("eventType")}</Label>
              <Select
                value={editedEvent.type}
                onValueChange={(value: EventType) => {
                  handleChange('type', value);
                  handleChange('bgColor', getEventColor(value, editedEvent.priority));
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("selectEventType")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="task">{t("eventTask")}</SelectItem>
                  <SelectItem value="subtask">{t("eventSubtask")}</SelectItem>
                  <SelectItem value="meeting">{t("eventMeeting")}</SelectItem>
                  <SelectItem value="deadline">{t("deadline")}</SelectItem>
                  <SelectItem value="reminder">{t("eventReminder")}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {(editedEvent.type === 'task' || editedEvent.type === 'subtask') && (
              <div className="grid gap-2">
                <Label>{t("priority")}</Label>
                <Select
                  value={editedEvent.priority || 'medium'}
                  onValueChange={(value: 'low' | 'medium' | 'high') => {
                    handleChange('priority', value);
                    handleChange('bgColor', getEventColor(editedEvent.type, value));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("selectEventPriority")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">{t("low")}</SelectItem>
                    <SelectItem value="medium">{t("medium")}</SelectItem>
                    <SelectItem value="high">{t("high")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {(editedEvent.type === 'task' || editedEvent.type === 'subtask') && (
              <div className="grid gap-2">
                <Label>{t("eventStatus")}</Label>
                <Select
                  value={editedEvent.isCompleted ? 'completed' : 'active'}
                  onValueChange={(value) => handleChange('isCompleted', value === 'completed')}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("selectEventStatus")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">{t("eventActive")}</SelectItem>
                    <SelectItem value="completed">{t("eventCompleted")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="grid gap-2">
              <Label>{t("eventProject")}</Label>
              <Select
                value={editedEvent.projectId || 'none'}
                onValueChange={(value) => handleChange('projectId', value === 'none' ? '' : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("selectEventProject")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">{t("noProject")}</SelectItem>
                  {projectOptions.filter(p => p.id !== 'none').map(project => (
                    <SelectItem key={project.id} value={project.id}>{project.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">{t("eventDescription")}</Label>
            <Textarea
              id="description"
              value={editedEvent.description || ''}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder={t("eventDescriptionPlaceholder")}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label>{t("startDate")}</Label>
              <Input
                type="datetime-local"
                value={format(editedEvent.start, "yyyy-MM-dd'T'HH:mm")}
                onChange={(e) => handleChange('start', new Date(e.target.value))}
              />
            </div>

            <div className="grid gap-2">
              <Label>{t("endDate")}</Label>
              <Input
                type="datetime-local"
                value={format(editedEvent.end, "yyyy-MM-dd'T'HH:mm")}
                onChange={(e) => handleChange('end', new Date(e.target.value))}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="reminder"
              checked={editedEvent.reminder || false}
              onCheckedChange={(checked) => {
                handleChange('reminder', checked);
                if (checked && !editedEvent.reminderTime) {
                  // Установить напоминание за 15 минут до начала по умолчанию
                  const reminderTime = new Date(editedEvent.start.getTime() - 15 * 60 * 1000);
                  handleChange('reminderTime', reminderTime);
                }
              }}
            />
            <Label htmlFor="reminder">Установить напоминание</Label>
          </div>

          {editedEvent.reminder && (
            <div className="grid gap-2">
              <Label>Время напоминания</Label>
              <Input
                type="datetime-local"
                value={editedEvent.reminderTime ? format(editedEvent.reminderTime, "yyyy-MM-dd'T'HH:mm") : ''}
                onChange={(e) => handleChange('reminderTime', new Date(e.target.value))}
              />
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          {!isNew && (
            <Button variant="destructive" onClick={onOpenDeleteDialog}>
              <Trash2 className="h-4 w-4 mr-1" />
              Удалить
            </Button>
          )}
          <div>
            <Button variant="outline" onClick={() => onOpenChange(false)} className="mr-2">
              Отмена
            </Button>
            <Button onClick={handleSave}>
              {isNew ? 'Создать' : 'Сохранить'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
