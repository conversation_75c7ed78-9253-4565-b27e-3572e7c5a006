"use client";

import { ChatMessage } from './chat-storage';

// Интерфейс для данных уведомления
export interface NotificationData {
  userId: string;
  userEmail?: string;
  userName?: string;
  telegramUsername?: string;
  subject: string;
  message: string;
  chatId: string;
  chatName: string;
  entityType: 'project' | 'task' | 'subtask';
  entityId: string;
  messageText: string;
  senderName: string;
}

// Функция для отправки уведомления на почту
export async function sendEmailNotification(data: NotificationData): Promise<boolean> {
  try {
    // В реальном приложении здесь был бы запрос к API для отправки email
    console.log(`Sending email notification to ${data.userEmail}:`, data);
    
    // Имитируем отправку запроса к API
    const response = await fetch('/api/notifications/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to send email notification: ${response.statusText}`);
    }
    
    return true;
  } catch (error) {
    console.error('Error sending email notification:', error);
    return false;
  }
}

// Функция для отправки уведомления в Telegram
export async function sendTelegramNotification(data: NotificationData): Promise<boolean> {
  try {
    // В реальном приложении здесь был бы запрос к API для отправки сообщения в Telegram
    console.log(`Sending Telegram notification to @${data.telegramUsername}:`, data);
    
    // Имитируем отправку запроса к API
    const response = await fetch('/api/notifications/telegram', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to send Telegram notification: ${response.statusText}`);
    }
    
    return true;
  } catch (error) {
    console.error('Error sending Telegram notification:', error);
    return false;
  }
}

// Функция для отправки уведомления о новом сообщении
export async function sendChatNotification(
  message: ChatMessage, 
  chatId: string, 
  chatName: string
): Promise<void> {
  try {
    // Получаем настройки пользователя из localStorage
    const emailReminders = localStorage.getItem('emailReminders') === 'true';
    const telegramReminders = localStorage.getItem('telegramReminders') === 'true';
    
    // Если уведомления отключены, выходим
    if (!emailReminders && !telegramReminders) {
      return;
    }
    
    // Получаем данные пользователя
    const userDataStr = localStorage.getItem('user');
    if (!userDataStr) {
      console.error('User data not found in localStorage');
      return;
    }
    
    const userData = JSON.parse(userDataStr);
    
    // Парсим chatId для получения типа сущности и ID
    const [entityType, entityId] = chatId.split('-');
    
    // Формируем данные для уведомления
    const notificationData: NotificationData = {
      userId: userData.id,
      userEmail: userData.email,
      userName: userData.name,
      telegramUsername: localStorage.getItem('telegramUsername') || '',
      subject: `Новое сообщение в чате "${chatName}"`,
      message: `У вас новое сообщение от ${message.senderName} в чате "${chatName}"`,
      chatId,
      chatName,
      entityType: entityType as 'project' | 'task' | 'subtask',
      entityId,
      messageText: message.text,
      senderName: message.senderName || 'Пользователь',
    };
    
    // Отправляем уведомления в зависимости от настроек
    if (emailReminders && userData.email) {
      await sendEmailNotification(notificationData);
    }
    
    if (telegramReminders && notificationData.telegramUsername) {
      await sendTelegramNotification(notificationData);
    }
  } catch (error) {
    console.error('Error sending chat notification:', error);
  }
}
