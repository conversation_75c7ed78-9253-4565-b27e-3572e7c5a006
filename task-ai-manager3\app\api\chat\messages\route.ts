import { NextResponse } from 'next/server';

// Моковые данные для сообщений чата
const mockMessages = {
  'chat-p1': [
    {
      id: 'msg1',
      text: 'Привет всем! Давайте обсудим редизайн сайта.',
      senderId: 'user1',
      senderName: 'Иван Петров',
      timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 час назад
    },
    {
      id: 'msg2',
      text: 'Я подготовил несколько макетов, можем их обсудить.',
      senderId: 'user2',
      senderName: 'Анна Смирнова',
      timestamp: new Date(Date.now() - 1800000).toISOString(), // 30 минут назад
    },
    {
      id: 'msg3',
      text: 'Отлично! Когда можем встретиться для обсуждения?',
      senderId: 'user3',
      senderName: 'Алексей Иванов',
      timestamp: new Date(Date.now() - 900000).toISOString(), // 15 минут назад
    },
  ],
  'chat-p2': [
    {
      id: 'msg4',
      text: 'Всем привет! Какие новости по мобильному приложению?',
      senderId: 'user1',
      senderName: 'Иван Петров',
      timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 часа назад
    },
    {
      id: 'msg5',
      text: 'Мы закончили разработку основных экранов.',
      senderId: 'user3',
      senderName: 'Алексей Иванов',
      timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 час назад
    },
  ],
  'chat-t1': [
    {
      id: 'msg6',
      text: 'Какие требования к дизайну главной страницы?',
      senderId: 'user2',
      senderName: 'Анна Смирнова',
      timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 день назад
    },
    {
      id: 'msg7',
      text: 'Нужно сделать современный дизайн с акцентом на UX.',
      senderId: 'user1',
      senderName: 'Иван Петров',
      timestamp: new Date(Date.now() - 43200000).toISOString(), // 12 часов назад
    },
  ],
  'chat-st1': [
    {
      id: 'msg8',
      text: 'Я начал работу над компонентом хедера.',
      senderId: 'user2',
      senderName: 'Анна Смирнова',
      timestamp: new Date(Date.now() - 172800000).toISOString(), // 2 дня назад
    },
    {
      id: 'msg9',
      text: 'Отлично! Не забудь про адаптивность.',
      senderId: 'user1',
      senderName: 'Иван Петров',
      timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 день назад
    },
  ],
};

// Функция для загрузки сообщений из localStorage
const getMessagesFromLocalStorage = (chatId) => {
  try {
    // В реальном приложении это был бы запрос к базе данных
    // Сначала проверяем моковые данные
    if (mockMessages[chatId] && mockMessages[chatId].length > 0) {
      return mockMessages[chatId];
    }

    // Если в моковых данных нет, проверяем localStorage
    if (typeof window !== 'undefined') {
      const savedMessages = localStorage.getItem('chatMessages');
      if (savedMessages) {
        const allMessages = JSON.parse(savedMessages);
        if (allMessages[chatId]) {
          // Сохраняем в моковые данные для быстрого доступа
          mockMessages[chatId] = allMessages[chatId];
          return allMessages[chatId];
        }
      }
    }

    return [];
  } catch (error) {
    console.error('Error loading messages:', error);
    return [];
  }
};

export async function GET(request: Request) {
  // Получаем chatId из URL
  const url = new URL(request.url);
  const chatId = url.searchParams.get('chatId');

  if (!chatId) {
    return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
  }

  // Получаем сообщения для указанного чата
  const messages = getMessagesFromLocalStorage(chatId);

  return NextResponse.json(messages);
}

// Функция для сохранения сообщений в localStorage
const saveMessageToLocalStorage = (chatId, message) => {
  try {
    // В реальном приложении это был бы запрос к базе данных
    // Для демонстрации сохраняем в моковые данные
    if (!mockMessages[chatId]) {
      mockMessages[chatId] = [];
    }
    mockMessages[chatId].push(message);

    // Сохраняем в глобальной области видимости, чтобы сообщения не пропадали при перезагрузке страницы
    if (typeof window !== 'undefined') {
      // Загружаем текущие сообщения из localStorage
      const savedMessages = localStorage.getItem('chatMessages');
      let allMessages = savedMessages ? JSON.parse(savedMessages) : {};

      // Добавляем новое сообщение
      if (!allMessages[chatId]) {
        allMessages[chatId] = [];
      }
      allMessages[chatId].push(message);

      // Сохраняем обновленные сообщения в localStorage
      localStorage.setItem('chatMessages', JSON.stringify(allMessages));
    }

    return true;
  } catch (error) {
    console.error('Error saving message:', error);
    return false;
  }
};

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { chatId, text, senderId, senderName } = body;

    if (!chatId || !text || !senderId || !senderName) {
      return NextResponse.json(
        { error: 'Missing required fields: chatId, text, senderId, senderName' },
        { status: 400 }
      );
    }

    // Создаем новое сообщение
    const newMessage = {
      id: `msg${Date.now()}`,
      text,
      senderId,
      senderName,
      timestamp: new Date().toISOString(),
      read: false // Добавляем флаг прочтения
    };

    // Сохраняем сообщение в localStorage
    saveMessageToLocalStorage(chatId, newMessage);

    return NextResponse.json(newMessage);
  } catch (error) {
    console.error('Error creating message:', error);
    return NextResponse.json({ error: 'Failed to create message' }, { status: 500 });
  }
}
