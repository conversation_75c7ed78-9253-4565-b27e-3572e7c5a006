"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Users,
  Settings,
  Bell,
  BarChart3,
  Database,
  Shield,
  Activity,
  Mail,
  MessageSquare
} from "lucide-react";
import { ReminderStatus } from '@/components/admin/reminder-status';

const AdminPage = () => {
  const [systemStats, setSystemStats] = useState({
    totalUsers: 1,
    totalProjects: 0,
    totalTasks: 0,
    activeChats: 0,
    emailNotifications: 0,
    telegramNotifications: 0,
  });

  // Загрузка статистики системы
  useEffect(() => {
    loadSystemStats();
  }, []);

  const loadSystemStats = () => {
    if (typeof window === 'undefined') return;

    try {
      // Получаем данные из localStorage
      const projects = JSON.parse(localStorage.getItem('projects') || '[]');
      const tasksData = JSON.parse(localStorage.getItem('tasksData') || '{}');
      const chats = JSON.parse(localStorage.getItem('chats') || '[]');

      // Подсчитываем задачи
      let totalTasks = 0;
      Object.values(tasksData).forEach((projectTasks: any) => {
        Object.values(projectTasks).forEach((statusTasks: any) => {
          totalTasks += Array.isArray(statusTasks) ? statusTasks.length : 0;
        });
      });

      setSystemStats({
        totalUsers: 1, // В реальном приложении это будет из API
        totalProjects: projects.length,
        totalTasks,
        activeChats: chats.length,
        emailNotifications: localStorage.getItem('emailReminders') === 'true' ? 1 : 0,
        telegramNotifications: localStorage.getItem('telegramReminders') === 'true' ? 1 : 0,
      });
    } catch (error) {
      console.error('Error loading system stats:', error);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Панель администратора</h1>
          <p className="text-muted-foreground">
            Управление системой AI Task Tracker
          </p>
        </div>
        <Badge variant="outline" className="px-3 py-1">
          <Shield className="mr-2 h-4 w-4" />
          Администратор
        </Badge>
      </div>

      {/* Общая статистика */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Пользователи</p>
                <p className="text-2xl font-bold">{systemStats.totalUsers}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Проекты</p>
                <p className="text-2xl font-bold">{systemStats.totalProjects}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Задачи</p>
                <p className="text-2xl font-bold">{systemStats.totalTasks}</p>
              </div>
              <Activity className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Чаты</p>
                <p className="text-2xl font-bold">{systemStats.activeChats}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Вкладки управления */}
      <Tabs defaultValue="reminders" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="reminders">
            <Bell className="mr-2 h-4 w-4" />
            Напоминания
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="mr-2 h-4 w-4" />
            Пользователи
          </TabsTrigger>
          <TabsTrigger value="system">
            <Settings className="mr-2 h-4 w-4" />
            Система
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart3 className="mr-2 h-4 w-4" />
            Аналитика
          </TabsTrigger>
        </TabsList>

        <TabsContent value="reminders" className="space-y-4">
          <ReminderStatus />
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Управление пользователями</CardTitle>
              <CardDescription>
                Просмотр и управление пользователями системы
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Текущий пользователь</p>
                    <p className="text-sm text-muted-foreground">Администратор системы</p>
                  </div>
                  <Badge>Активен</Badge>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg text-center">
                    <Mail className="h-6 w-6 mx-auto mb-2 text-blue-500" />
                    <p className="text-sm text-muted-foreground">Email уведомления</p>
                    <Badge variant={systemStats.emailNotifications > 0 ? "default" : "secondary"}>
                      {systemStats.emailNotifications > 0 ? "Включены" : "Отключены"}
                    </Badge>
                  </div>

                  <div className="p-4 border rounded-lg text-center">
                    <MessageSquare className="h-6 w-6 mx-auto mb-2 text-green-500" />
                    <p className="text-sm text-muted-foreground">Telegram уведомления</p>
                    <Badge variant={systemStats.telegramNotifications > 0 ? "default" : "secondary"}>
                      {systemStats.telegramNotifications > 0 ? "Включены" : "Отключены"}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Системные настройки</CardTitle>
              <CardDescription>
                Конфигурация и мониторинг системы
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Database className="h-5 w-5 text-blue-500" />
                      <p className="font-medium">Хранилище данных</p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Данные хранятся в localStorage браузера
                    </p>
                    <Badge variant="outline" className="mt-2">LocalStorage</Badge>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Activity className="h-5 w-5 text-green-500" />
                      <p className="font-medium">Статус системы</p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Все сервисы работают нормально
                    </p>
                    <Badge variant="default" className="mt-2">Активна</Badge>
                  </div>
                </div>

                <Button onClick={loadSystemStats} className="w-full">
                  Обновить статистику системы
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Аналитика системы</CardTitle>
              <CardDescription>
                Статистика использования и производительности
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="font-medium">Активность пользователей</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Проекты созданы</span>
                      <span className="text-sm font-medium">{systemStats.totalProjects}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Задачи созданы</span>
                      <span className="text-sm font-medium">{systemStats.totalTasks}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Активные чаты</span>
                      <span className="text-sm font-medium">{systemStats.activeChats}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-medium">Уведомления</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Email активны</span>
                      <span className="text-sm font-medium">{systemStats.emailNotifications}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Telegram активны</span>
                      <span className="text-sm font-medium">{systemStats.telegramNotifications}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminPage;

