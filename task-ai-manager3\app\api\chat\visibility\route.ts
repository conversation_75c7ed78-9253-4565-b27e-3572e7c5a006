import { NextResponse } from 'next/server';

// Моковые данные для хранения видимости чатов
const chatVisibility = {};

export async function POST(req: Request) {
  try {
    const { chatId, hidden } = await req.json();

    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
    }

    // Определяем тип чата (проект, задача, подзадача) по формату ID
    const [entityType, entityId] = chatId.split('-');

    if (!entityType || !entityId) {
      return NextResponse.json({ error: 'Invalid chat ID format' }, { status: 400 });
    }

    // Сохраняем видимость чата в моковом хранилище
    chatVisibility[chatId] = hidden;

    // В реальном приложении здесь была бы логика сохранения в базу данных

    return NextResponse.json({
      success: true,
      data: {
        chatId,
        hidden,
        entityType,
        entityId
      }
    });
  } catch (error) {
    console.error('Error updating chat visibility:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
