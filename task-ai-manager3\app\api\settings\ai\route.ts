import { NextRequest, NextResponse } from 'next/server'
import { clearAIConfigCache, validateAIConfig } from '@/lib/ai-config'

// In-memory storage for AI settings (replace with database in production)
let aiSettings = {
  enableAI: true,
  aiModel: "gpt-4o",
  aiApiKey: process.env.OPENAI_API_KEY || "",
  aiTemperature: 0.7,
  maxTokens: 1000,
  customPrompt: "You are an AI assistant for a task management application. Help users organize their tasks and projects effectively.",
}

/**
 * GET /api/settings/ai - Retrieve AI settings
 */
export async function GET() {
  try {
    // Return AI settings (mask sensitive data)
    return NextResponse.json({
      enableAI: aiSettings.enableAI,
      aiModel: aiSettings.aiModel,
      aiApiKey: aiSettings.aiApiKey ? '***' : '', // Mask API key for security
      aiTemperature: aiSettings.aiTemperature,
      maxTokens: aiSettings.maxTokens,
      customPrompt: aiSettings.customPrompt,
    })
  } catch (error) {
    console.error('Error fetching AI settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch AI settings' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/settings/ai - Update AI settings
 */
export async function POST(request: NextRequest) {
  try {
    const settings = await request.json()

    // Validate AI settings
    const errors = validateAIConfig(settings)
    if (errors.length > 0) {
      return NextResponse.json(
        { error: errors.join(', ') },
        { status: 400 }
      )
    }

    // Update AI settings
    aiSettings = {
      ...aiSettings,
      enableAI: settings.enableAI ?? aiSettings.enableAI,
      aiModel: settings.aiModel || aiSettings.aiModel,
      aiApiKey: settings.aiApiKey || aiSettings.aiApiKey,
      aiTemperature: settings.aiTemperature ?? aiSettings.aiTemperature,
      maxTokens: settings.maxTokens ?? aiSettings.maxTokens,
      customPrompt: settings.customPrompt || aiSettings.customPrompt,
    }

    // Clear AI config cache when settings change
    clearAIConfigCache()

    return NextResponse.json({
      success: true,
      message: 'AI settings updated successfully',
      settings: {
        enableAI: aiSettings.enableAI,
        aiModel: aiSettings.aiModel,
        aiApiKey: aiSettings.aiApiKey ? '***' : '',
        aiTemperature: aiSettings.aiTemperature,
        maxTokens: aiSettings.maxTokens,
        customPrompt: aiSettings.customPrompt,
      }
    })
  } catch (error) {
    console.error('Error updating AI settings:', error)
    return NextResponse.json(
      { error: 'Failed to update AI settings' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/settings/ai - Reset AI settings to defaults
 */
export async function DELETE() {
  try {
    // Reset to default settings
    aiSettings = {
      enableAI: true,
      aiModel: "gpt-4o",
      aiApiKey: process.env.OPENAI_API_KEY || "",
      aiTemperature: 0.7,
      maxTokens: 1000,
      customPrompt: "You are an AI assistant for a task management application. Help users organize their tasks and projects effectively.",
    }

    // Clear AI config cache
    clearAIConfigCache()

    return NextResponse.json({
      success: true,
      message: 'AI settings reset to defaults',
      settings: {
        enableAI: aiSettings.enableAI,
        aiModel: aiSettings.aiModel,
        aiApiKey: aiSettings.aiApiKey ? '***' : '',
        aiTemperature: aiSettings.aiTemperature,
        maxTokens: aiSettings.maxTokens,
        customPrompt: aiSettings.customPrompt,
      }
    })
  } catch (error) {
    console.error('Error resetting AI settings:', error)
    return NextResponse.json(
      { error: 'Failed to reset AI settings' },
      { status: 500 }
    )
  }
}
