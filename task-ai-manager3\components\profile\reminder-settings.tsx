"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Save, Bell, Plus, X, TestTube } from "lucide-react";
import { useTranslation } from '@/lib/translations';
import { validateTelegramChatId } from '@/lib/telegram-service';
import { updateReminderScheduler } from '@/lib/reminder-scheduler';

interface ReminderSettingsProps {
  userId: string;
  userEmail?: string;
}

export default function ReminderSettings({ userId, userEmail }: ReminderSettingsProps) {
  const { t } = useTranslation();
  const { toast } = useToast();

  // Состояния для настроек напоминаний
  const [emailReminders, setEmailReminders] = useState(false);
  const [telegramReminders, setTelegramReminders] = useState(false);
  const [emailForReminders, setEmailForReminders] = useState(userEmail || '');
  const [telegramUsername, setTelegramUsername] = useState('');
  const [reminderTimes, setReminderTimes] = useState<number[]>([1440, 60, 15]); // За день, час, 15 минут
  const [newReminderTime, setNewReminderTime] = useState('');
  const [newReminderUnit, setNewReminderUnit] = useState('minutes');
  const [isTestingTelegram, setIsTestingTelegram] = useState(false);

  // Загружаем настройки из localStorage при монтировании компонента
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedEmailReminders = localStorage.getItem('emailReminders');
      if (savedEmailReminders !== null) {
        setEmailReminders(savedEmailReminders === 'true');
      }

      const savedTelegramReminders = localStorage.getItem('telegramReminders');
      if (savedTelegramReminders !== null) {
        setTelegramReminders(savedTelegramReminders === 'true');
      }

      const savedEmailForReminders = localStorage.getItem('emailForReminders');
      if (savedEmailForReminders !== null) {
        setEmailForReminders(savedEmailForReminders);
      } else if (userEmail) {
        setEmailForReminders(userEmail);
      }

      const savedTelegramUsername = localStorage.getItem('telegramUsername');
      if (savedTelegramUsername !== null) {
        setTelegramUsername(savedTelegramUsername);
      }

      const savedReminderTimes = localStorage.getItem('reminderTimes');
      if (savedReminderTimes !== null) {
        setReminderTimes(JSON.parse(savedReminderTimes));
      }
    }
  }, [userEmail]);

  // Функция для добавления нового времени напоминания
  const handleAddReminderTime = () => {
    const timeValue = parseInt(newReminderTime);
    if (isNaN(timeValue) || timeValue <= 0) {
      toast({
        title: "Ошибка",
        description: "Введите корректное время",
        variant: "destructive",
      });
      return;
    }

    let timeInMinutes = timeValue;
    if (newReminderUnit === 'hours') {
      timeInMinutes = timeValue * 60;
    } else if (newReminderUnit === 'days') {
      timeInMinutes = timeValue * 1440;
    }

    if (!reminderTimes.includes(timeInMinutes)) {
      setReminderTimes([...reminderTimes, timeInMinutes].sort((a, b) => b - a));
      setNewReminderTime('');
    } else {
      toast({
        title: "Предупреждение",
        description: "Такое время напоминания уже добавлено",
        variant: "destructive",
      });
    }
  };

  // Функция для удаления времени напоминания
  const handleRemoveReminderTime = (timeToRemove: number) => {
    setReminderTimes(reminderTimes.filter(time => time !== timeToRemove));
  };

  // Функция для форматирования времени напоминания
  const formatReminderTime = (minutes: number): string => {
    if (minutes >= 1440) {
      const days = Math.floor(minutes / 1440);
      return `${days} ${days === 1 ? 'день' : days < 5 ? 'дня' : 'дней'}`;
    } else if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      return `${hours} ${hours === 1 ? 'час' : hours < 5 ? 'часа' : 'часов'}`;
    } else {
      return `${minutes} ${minutes === 1 ? 'минуту' : minutes < 5 ? 'минуты' : 'минут'}`;
    }
  };

  // Функция для тестирования Telegram-соединения
  const handleTestTelegram = async () => {
    if (!telegramUsername) {
      toast({
        title: "Ошибка",
        description: "Введите имя пользователя Telegram",
        variant: "destructive",
      });
      return;
    }

    setIsTestingTelegram(true);

    try {
      const isValid = await validateTelegramChatId(telegramUsername);

      if (isValid) {
        toast({
          title: "Успешно!",
          description: "Тестовое сообщение отправлено в Telegram",
        });
      } else {
        toast({
          title: "Ошибка",
          description: "Не удалось отправить сообщение в Telegram. Проверьте настройки бота и имя пользователя.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Произошла ошибка при тестировании Telegram",
        variant: "destructive",
      });
    } finally {
      setIsTestingTelegram(false);
    }
  };

  // Функция для сохранения настроек
  const handleSaveSettings = () => {
    // Сохраняем настройки в localStorage
    localStorage.setItem('emailReminders', String(emailReminders));
    localStorage.setItem('telegramReminders', String(telegramReminders));
    localStorage.setItem('emailForReminders', emailForReminders);
    localStorage.setItem('telegramUsername', telegramUsername);
    localStorage.setItem('reminderTimes', JSON.stringify(reminderTimes));

    // В реальном приложении здесь был бы запрос к API для сохранения настроек в базе данных

    // Обновляем планировщик напоминаний
    updateReminderScheduler();

    // Показываем уведомление об успешном сохранении
    toast({
      title: t("settingsSaved") || "Настройки сохранены",
      description: t("reminderSettingsSaved") || "Настройки напоминаний успешно обновлены",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Bell className="mr-2 h-5 w-5" />
          {t("reminderSettings") || "Настройки напоминаний"}
        </CardTitle>
        <CardDescription>
          {t("reminderSettingsDescription") || "Настройте способы получения напоминаний о задачах и событиях"}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email-reminders" className="text-base">
                {t("emailReminders") || "Напоминания на почту"}
              </Label>
              <p className="text-sm text-muted-foreground">
                {t("emailRemindersDescription") || "Получать напоминания на электронную почту"}
              </p>
            </div>
            <Switch
              id="email-reminders"
              checked={emailReminders}
              onCheckedChange={setEmailReminders}
            />
          </div>

          {emailReminders && (
            <div className="ml-6 space-y-2">
              <Label htmlFor="email-for-reminders">
                {t("emailForReminders") || "Email для напоминаний"}
              </Label>
              <Input
                id="email-for-reminders"
                type="email"
                value={emailForReminders}
                onChange={(e) => setEmailForReminders(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
          )}
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="telegram-reminders" className="text-base">
                {t("telegramReminders") || "Напоминания в Telegram"}
              </Label>
              <p className="text-sm text-muted-foreground">
                {t("telegramRemindersDescription") || "Получать напоминания в Telegram"}
              </p>
            </div>
            <Switch
              id="telegram-reminders"
              checked={telegramReminders}
              onCheckedChange={setTelegramReminders}
            />
          </div>

          {telegramReminders && (
            <div className="ml-6 space-y-2">
              <Label htmlFor="telegram-username">
                {t("telegramUsername") || "Имя пользователя в Telegram"}
              </Label>
              <div className="flex gap-2">
                <Input
                  id="telegram-username"
                  value={telegramUsername}
                  onChange={(e) => setTelegramUsername(e.target.value)}
                  placeholder="@username"
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleTestTelegram}
                  disabled={isTestingTelegram || !telegramUsername}
                >
                  <TestTube className="mr-2 h-4 w-4" />
                  {isTestingTelegram ? "Тест..." : "Тест"}
                </Button>
              </div>
            </div>
          )}
        </div>

        <div className="space-y-4">
          <div>
            <Label className="text-base">
              {t("reminderTimes") || "Время напоминаний"}
            </Label>
            <p className="text-sm text-muted-foreground">
              {t("reminderTimesDescription") || "Настройте, за сколько времени до дедлайна отправлять напоминания"}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex flex-wrap gap-2">
              {reminderTimes.map((time) => (
                <Badge key={time} variant="secondary" className="flex items-center gap-1">
                  за {formatReminderTime(time)}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                    onClick={() => handleRemoveReminderTime(time)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>

            <div className="flex gap-2">
              <Input
                type="number"
                placeholder="Время"
                value={newReminderTime}
                onChange={(e) => setNewReminderTime(e.target.value)}
                className="w-20"
                min="1"
              />
              <Select value={newReminderUnit} onValueChange={setNewReminderUnit}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="minutes">минут</SelectItem>
                  <SelectItem value="hours">часов</SelectItem>
                  <SelectItem value="days">дней</SelectItem>
                </SelectContent>
              </Select>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddReminderTime}
                disabled={!newReminderTime}
              >
                <Plus className="mr-2 h-4 w-4" />
                Добавить
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleSaveSettings} className="ml-auto">
          <Save className="mr-2 h-4 w-4" />
          {t("saveSettings") || "Сохранить настройки"}
        </Button>
      </CardFooter>
    </Card>
  );
}
