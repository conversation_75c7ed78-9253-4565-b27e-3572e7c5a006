// Simple test for AI service functionality
// This can be run in the browser console to verify AI features

function testAIService() {
  console.log('🤖 Testing AI Service Functionality...');
  
  // Test data
  const testTask = {
    id: 'test-1',
    title: 'Разработка API для мобильного приложения',
    description: 'Создать RESTful API для взаимодействия мобильного приложения с базой данных',
    status: 'todo',
    priority: 'high',
    dueDate: '2025-04-15',
    tags: ['development', 'api', 'mobile'],
    projectId: 'p1'
  };
  
  const testContext = {
    currentTask: testTask,
    tasks: [testTask]
  };
  
  // Test 1: Task Analysis
  console.log('📊 Test 1: Task Analysis');
  try {
    fetch('/api/ai/tasks', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: 'анализ',
        context: testContext
      })
    })
    .then(response => response.json())
    .then(data => {
      console.log('✅ Task Analysis Response:', data.response?.substring(0, 100) + '...');
    })
    .catch(error => {
      console.error('❌ Task Analysis Error:', error);
    });
  } catch (error) {
    console.error('❌ Task Analysis Test Failed:', error);
  }
  
  // Test 2: Subtask Generation
  console.log('🔧 Test 2: Subtask Generation');
  try {
    fetch('/api/ai/subtasks', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        task: testTask,
        additionalInfo: 'Нужно создать API для iOS и Android приложений',
        projectContext: { tasks: [testTask] }
      })
    })
    .then(response => response.json())
    .then(data => {
      console.log('✅ Subtask Generation Response:', {
        success: data.success,
        subtaskCount: data.subtasks?.length || 0,
        hasAnalysis: !!data.analysis
      });
      if (data.subtasks?.length > 0) {
        console.log('📋 Sample Subtask:', data.subtasks[0]);
      }
    })
    .catch(error => {
      console.error('❌ Subtask Generation Error:', error);
    });
  } catch (error) {
    console.error('❌ Subtask Generation Test Failed:', error);
  }
  
  // Test 3: Solution Suggestions
  console.log('💡 Test 3: Solution Suggestions');
  try {
    fetch('/api/ai/tasks', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: 'как решить эту задачу?',
        context: testContext
      })
    })
    .then(response => response.json())
    .then(data => {
      console.log('✅ Solution Suggestions Response:', data.response?.substring(0, 100) + '...');
    })
    .catch(error => {
      console.error('❌ Solution Suggestions Error:', error);
    });
  } catch (error) {
    console.error('❌ Solution Suggestions Test Failed:', error);
  }
  
  // Test 4: Planning Advice
  console.log('📋 Test 4: Planning Advice');
  try {
    fetch('/api/ai/tasks', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: 'составь план выполнения',
        context: testContext
      })
    })
    .then(response => response.json())
    .then(data => {
      console.log('✅ Planning Advice Response:', data.response?.substring(0, 100) + '...');
    })
    .catch(error => {
      console.error('❌ Planning Advice Error:', error);
    });
  } catch (error) {
    console.error('❌ Planning Advice Test Failed:', error);
  }
  
  console.log('🏁 AI Service Tests Completed. Check responses above.');
}

// Test AI service locally (without API calls)
function testAIServiceLocal() {
  console.log('🧪 Testing AI Service Locally...');
  
  // Import the AI service (this would work in a Node.js environment)
  // For browser testing, we'll simulate the functionality
  
  const testTask = {
    id: 'test-1',
    title: 'Создать дизайн мобильного приложения',
    description: 'Разработать UI/UX дизайн для мобильного приложения',
    status: 'todo',
    priority: 'high',
    dueDate: '2025-04-20',
    tags: ['design', 'mobile', 'ui'],
    projectId: 'p1'
  };
  
  // Test task complexity analysis
  console.log('📊 Task Complexity Analysis:');
  const complexity = analyzeTaskComplexity(testTask);
  console.log('Complexity:', complexity);
  
  // Test risk identification
  console.log('⚠️ Risk Identification:');
  const risks = identifyTaskRisks(testTask);
  console.log('Risks:', risks);
  
  // Test next steps suggestion
  console.log('🚀 Next Steps:');
  const nextSteps = suggestNextSteps(testTask);
  console.log('Next Steps:', nextSteps);
  
  console.log('✅ Local AI Service Tests Completed');
}

// Helper functions for local testing
function analyzeTaskComplexity(task) {
  let complexity = 'Средняя';
  let factors = [];
  
  if (task.description && task.description.length > 200) {
    complexity = 'Высокая';
    factors.push('подробное описание');
  }
  
  if (task.priority === 'high') {
    factors.push('высокий приоритет');
  }
  
  return `${complexity}${factors.length > 0 ? ` (${factors.join(', ')})` : ''}`;
}

function identifyTaskRisks(task) {
  const risks = [];
  
  if (!task.dueDate) {
    risks.push('• Отсутствие четкого дедлайна может привести к затягиванию');
  }
  
  if (!task.assignee) {
    risks.push('• Не назначен ответственный исполнитель');
  }
  
  if (task.priority === 'high' && task.status === 'todo') {
    risks.push('• Высокоприоритетная задача еще не начата');
  }
  
  return risks.length > 0 ? risks.join('\n') : '• Критических рисков не выявлено';
}

function suggestNextSteps(task) {
  if (task.status === 'todo') {
    return '1. Назначить ответственного\n2. Установить дедлайн\n3. Разбить на подзадачи\n4. Начать выполнение';
  } else if (task.status === 'in-progress') {
    return '1. Проверить прогресс подзадач\n2. Выявить блокеры\n3. Обновить статус\n4. Скорректировать план при необходимости';
  } else {
    return '1. Провести ретроспективу\n2. Зафиксировать уроки\n3. Подготовить отчет\n4. Архивировать задачу';
  }
}

// Export functions for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testAIService,
    testAIServiceLocal,
    analyzeTaskComplexity,
    identifyTaskRisks,
    suggestNextSteps
  };
}

// Auto-run local tests if in browser
if (typeof window !== 'undefined') {
  console.log('🚀 AI Service Test Suite Ready!');
  console.log('Run testAIService() to test API endpoints');
  console.log('Run testAIServiceLocal() to test local functions');
}
