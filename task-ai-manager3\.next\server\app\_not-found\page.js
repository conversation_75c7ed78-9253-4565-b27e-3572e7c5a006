/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(action-browser)/./lib/email-service.ts":
/*!******************************!*\
  !*** ./lib/email-service.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendReminderEmail: () => (/* binding */ sendReminderEmail)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(action-browser)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! nodemailer */ \"(action-browser)/./node_modules/nodemailer/lib/nodemailer.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"40a3470f6f9b2080776491b136ec9b2d0412ffed56\":\"sendReminderEmail\"} */ \n\n\n// Получение настроек SMTP из базы данных или из переменных окружения\nasync function getEmailSettings() {\n    // В реальном приложении здесь будет запрос к базе данных для получения настроек\n    // Для примера используем переменные окружения\n    return {\n        smtpServer: process.env.SMTP_SERVER || '',\n        smtpPort: process.env.SMTP_PORT || '587',\n        smtpUsername: process.env.SMTP_USERNAME || '',\n        smtpPassword: process.env.SMTP_PASSWORD || '',\n        fromEmail: process.env.FROM_EMAIL || '<EMAIL>'\n    };\n}\n// Создание транспорта для отправки писем\nasync function createTransport() {\n    const settings = await getEmailSettings();\n    // Проверяем, что все необходимые настройки указаны\n    if (!settings.smtpServer || !settings.smtpUsername || !settings.smtpPassword) {\n        throw new Error('SMTP settings are not configured');\n    }\n    return nodemailer__WEBPACK_IMPORTED_MODULE_2__.createTransport({\n        host: settings.smtpServer,\n        port: parseInt(settings.smtpPort, 10),\n        secure: parseInt(settings.smtpPort, 10) === 465,\n        auth: {\n            user: settings.smtpUsername,\n            pass: settings.smtpPassword\n        }\n    });\n}\n// Функция для отправки напоминания по электронной почте\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ sendReminderEmail(reminderData) {\n    try {\n        // Получаем настройки электронной почты\n        const settings = await getEmailSettings();\n        // Проверяем, что все необходимые настройки указаны\n        if (!settings.smtpServer || !settings.smtpUsername || !settings.smtpPassword) {\n            console.error('SMTP settings are not configured');\n            return false;\n        }\n        // Проверяем, что у пользователя есть email\n        if (!reminderData.userEmail) {\n            console.error(`User ${reminderData.userId} does not have an email address`);\n            return false;\n        }\n        // Создаем транспорт\n        const transporter = await createTransport();\n        // Формируем HTML для письма\n        const htmlContent = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #333;\">Напоминание</h2>\n        <p>Здравствуйте, ${reminderData.userName || 'пользователь'}!</p>\n        <p>${reminderData.message}</p>\n        <div style=\"background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0; color: #333;\">${reminderData.itemTitle}</h3>\n          ${reminderData.projectName ? `<p><strong>Проект:</strong> ${reminderData.projectName}</p>` : ''}\n          ${reminderData.dueDate ? `<p><strong>Срок:</strong> ${reminderData.dueDate.toLocaleDateString('ru-RU')}</p>` : ''}\n        </div>\n        <p>С уважением,<br>Команда AI Task Tracker</p>\n      </div>\n    `;\n        // Отправляем письмо\n        const info = await transporter.sendMail({\n            from: `\"AI Task Tracker\" <${settings.fromEmail}>`,\n            to: reminderData.userEmail,\n            subject: reminderData.subject,\n            html: htmlContent\n        });\n        console.log(`Email sent to ${reminderData.userEmail}: ${info.messageId}`);\n        // Записываем информацию об отправленном напоминании в лог\n        await logReminderSent(reminderData);\n        return true;\n    } catch (error) {\n        console.error('Error sending reminder email:', error);\n        return false;\n    }\n}\n// Функция для записи информации об отправленном напоминании в лог\nasync function logReminderSent(reminderData) {\n    try {\n        // В реальном приложении здесь будет запись в базу данных\n        console.log(`Reminder sent to ${reminderData.userEmail} for ${reminderData.itemType} \"${reminderData.itemTitle}\"`);\n    // Пример записи в базу данных (закомментировано, так как модель не существует)\n    /*\n    await prisma.reminderLog.create({\n      data: {\n        userId: reminderData.userId,\n        itemType: reminderData.itemType,\n        itemId: reminderData.itemId,\n        sentAt: new Date(),\n        success: true,\n      },\n    });\n    */ } catch (error) {\n        console.error('Error logging reminder:', error);\n    }\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_3__.ensureServerEntryExports)([\n    sendReminderEmail\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(sendReminderEmail, \"40a3470f6f9b2080776491b136ec9b2d0412ffed56\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./lib/email-service.ts\n");

/***/ }),

/***/ "(action-browser)/./lib/telegram-service.ts":
/*!*********************************!*\
  !*** ./lib/telegram-service.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTelegramBotInfo: () => (/* binding */ getTelegramBotInfo),\n/* harmony export */   sendTelegramMessage: () => (/* binding */ sendTelegramMessage),\n/* harmony export */   sendTelegramReminder: () => (/* binding */ sendTelegramReminder),\n/* harmony export */   validateTelegramChatId: () => (/* binding */ validateTelegramChatId)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(action-browser)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"0004d75c0cbd3b51b50db872a1bb33175142ec7a81\":\"getTelegramBotInfo\",\"4036d186bba33a6111e3a1a311e5529f6815449ecf\":\"validateTelegramChatId\",\"40d1ad41895a6b2d78824c2a72a188ce00547f82db\":\"sendTelegramReminder\",\"40ddd58023aecf11b09e60f5964dbdfe2e36fe9c17\":\"sendTelegramMessage\"} */ \n\n// Получение настроек Telegram Bot из переменных окружения\nfunction getTelegramSettings() {\n    const botToken = process.env.TELEGRAM_BOT_TOKEN;\n    if (!botToken) {\n        throw new Error('TELEGRAM_BOT_TOKEN is not configured');\n    }\n    return {\n        botToken,\n        apiUrl: `https://api.telegram.org/bot${botToken}`\n    };\n}\n// Функция для отправки сообщения через Telegram Bot API\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ sendTelegramMessage(data) {\n    try {\n        const settings = getTelegramSettings();\n        // Проверяем, что все необходимые данные указаны\n        if (!data.chatId || !data.message) {\n            console.error('Telegram chat ID and message are required');\n            return false;\n        }\n        // Формируем тело запроса\n        const requestBody = {\n            chat_id: data.chatId,\n            text: data.message,\n            parse_mode: data.parseMode || 'Markdown',\n            disable_web_page_preview: data.disableWebPagePreview || false,\n            disable_notification: data.disableNotification || false\n        };\n        // Отправляем запрос к Telegram Bot API\n        const response = await fetch(`${settings.apiUrl}/sendMessage`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error('Telegram API error:', errorData);\n            return false;\n        }\n        const responseData = await response.json();\n        console.log(`Telegram message sent to ${data.chatId}: ${responseData.result.message_id}`);\n        // Записываем информацию об отправленном сообщении в лог\n        await logTelegramMessageSent(data);\n        return true;\n    } catch (error) {\n        console.error('Error sending Telegram message:', error);\n        return false;\n    }\n}\n// Функция для отправки напоминания в Telegram\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ sendTelegramReminder(reminderData) {\n    try {\n        // Проверяем, что у пользователя есть Telegram chat ID\n        if (!reminderData.telegramChatId) {\n            console.error(`User ${reminderData.userId} does not have a Telegram chat ID`);\n            return false;\n        }\n        // Формируем сообщение для Telegram\n        const messageText = formatTelegramReminderMessage(reminderData);\n        // Отправляем сообщение\n        const result = await sendTelegramMessage({\n            chatId: reminderData.telegramChatId,\n            message: messageText,\n            parseMode: 'Markdown'\n        });\n        return result;\n    } catch (error) {\n        console.error('Error sending Telegram reminder:', error);\n        return false;\n    }\n}\n// Функция для форматирования сообщения напоминания для Telegram\nfunction formatTelegramReminderMessage(reminderData) {\n    const { userName, subject, message, itemTitle, itemType, dueDate, projectName } = reminderData;\n    let messageText = `🔔 *${subject}*\\n\\n`;\n    messageText += `Привет, ${userName}!\\n\\n`;\n    messageText += `${message}\\n\\n`;\n    // Добавляем информацию о элементе\n    const typeEmoji = getTypeEmoji(itemType);\n    messageText += `${typeEmoji} *${itemTitle}*\\n`;\n    if (projectName) {\n        messageText += `📁 Проект: ${projectName}\\n`;\n    }\n    if (dueDate) {\n        messageText += `📅 Срок: ${dueDate.toLocaleDateString('ru-RU')}\\n`;\n    }\n    messageText += `\\n_AI Task Tracker_`;\n    return messageText;\n}\n// Функция для получения эмодзи по типу элемента\nfunction getTypeEmoji(itemType) {\n    switch(itemType){\n        case 'task':\n            return '✅';\n        case 'subtask':\n            return '📝';\n        case 'project':\n            return '📁';\n        case 'event':\n            return '📅';\n        default:\n            return '📌';\n    }\n}\n// Функция для записи информации об отправленном сообщении в лог\nasync function logTelegramMessageSent(data) {\n    try {\n        // В реальном приложении здесь будет запись в базу данных\n        console.log(`Telegram message sent to ${data.chatId}: ${data.message.substring(0, 50)}...`);\n    // Пример записи в базу данных (закомментировано, так как модель не существует)\n    /*\n    await prisma.telegramLog.create({\n      data: {\n        chatId: data.chatId,\n        message: data.message,\n        sentAt: new Date(),\n        success: true,\n      },\n    });\n    */ } catch (error) {\n        console.error('Error logging Telegram message:', error);\n    }\n}\n// Функция для получения информации о боте\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ getTelegramBotInfo() {\n    try {\n        const settings = getTelegramSettings();\n        const response = await fetch(`${settings.apiUrl}/getMe`);\n        if (!response.ok) {\n            throw new Error(`Failed to get bot info: ${response.statusText}`);\n        }\n        const data = await response.json();\n        return data.result;\n    } catch (error) {\n        console.error('Error getting Telegram bot info:', error);\n        return null;\n    }\n}\n// Функция для проверки валидности Telegram chat ID\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ validateTelegramChatId(chatId) {\n    try {\n        const result = await sendTelegramMessage({\n            chatId,\n            message: '🤖 Тест соединения с AI Task Tracker успешен!',\n            disableNotification: true\n        });\n        return result;\n    } catch (error) {\n        console.error('Error validating Telegram chat ID:', error);\n        return false;\n    }\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_2__.ensureServerEntryExports)([\n    sendTelegramMessage,\n    sendTelegramReminder,\n    getTelegramBotInfo,\n    validateTelegramChatId\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(sendTelegramMessage, \"40ddd58023aecf11b09e60f5964dbdfe2e36fe9c17\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(sendTelegramReminder, \"40d1ad41895a6b2d78824c2a72a188ce00547f82db\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(getTelegramBotInfo, \"0004d75c0cbd3b51b50db872a1bb33175142ec7a81\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(validateTelegramChatId, \"4036d186bba33a6111e3a1a311e5529f6815449ecf\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./lib/telegram-service.ts\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Cemail-service.ts%22%2C%5B%7B%22id%22%3A%2240a3470f6f9b2080776491b136ec9b2d0412ffed56%22%2C%22exportedName%22%3A%22sendReminderEmail%22%7D%5D%5D%2C%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctelegram-service.ts%22%2C%5B%7B%22id%22%3A%220004d75c0cbd3b51b50db872a1bb33175142ec7a81%22%2C%22exportedName%22%3A%22getTelegramBotInfo%22%7D%2C%7B%22id%22%3A%224036d186bba33a6111e3a1a311e5529f6815449ecf%22%2C%22exportedName%22%3A%22validateTelegramChatId%22%7D%2C%7B%22id%22%3A%2240d1ad41895a6b2d78824c2a72a188ce00547f82db%22%2C%22exportedName%22%3A%22sendTelegramReminder%22%7D%2C%7B%22id%22%3A%2240ddd58023aecf11b09e60f5964dbdfe2e36fe9c17%22%2C%22exportedName%22%3A%22sendTelegramMessage%22%7D%5D%5D%5D&__client_imported__=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Cemail-service.ts%22%2C%5B%7B%22id%22%3A%2240a3470f6f9b2080776491b136ec9b2d0412ffed56%22%2C%22exportedName%22%3A%22sendReminderEmail%22%7D%5D%5D%2C%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctelegram-service.ts%22%2C%5B%7B%22id%22%3A%220004d75c0cbd3b51b50db872a1bb33175142ec7a81%22%2C%22exportedName%22%3A%22getTelegramBotInfo%22%7D%2C%7B%22id%22%3A%224036d186bba33a6111e3a1a311e5529f6815449ecf%22%2C%22exportedName%22%3A%22validateTelegramChatId%22%7D%2C%7B%22id%22%3A%2240d1ad41895a6b2d78824c2a72a188ce00547f82db%22%2C%22exportedName%22%3A%22sendTelegramReminder%22%7D%2C%7B%22id%22%3A%2240ddd58023aecf11b09e60f5964dbdfe2e36fe9c17%22%2C%22exportedName%22%3A%22sendTelegramMessage%22%7D%5D%5D%5D&__client_imported__=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"0004d75c0cbd3b51b50db872a1bb33175142ec7a81\": () => (/* reexport safe */ D_Projects_task_ai_manager3_lib_telegram_service_ts__WEBPACK_IMPORTED_MODULE_1__.getTelegramBotInfo),\n/* harmony export */   \"4036d186bba33a6111e3a1a311e5529f6815449ecf\": () => (/* reexport safe */ D_Projects_task_ai_manager3_lib_telegram_service_ts__WEBPACK_IMPORTED_MODULE_1__.validateTelegramChatId),\n/* harmony export */   \"40a3470f6f9b2080776491b136ec9b2d0412ffed56\": () => (/* reexport safe */ D_Projects_task_ai_manager3_lib_email_service_ts__WEBPACK_IMPORTED_MODULE_0__.sendReminderEmail),\n/* harmony export */   \"40d1ad41895a6b2d78824c2a72a188ce00547f82db\": () => (/* reexport safe */ D_Projects_task_ai_manager3_lib_telegram_service_ts__WEBPACK_IMPORTED_MODULE_1__.sendTelegramReminder),\n/* harmony export */   \"40ddd58023aecf11b09e60f5964dbdfe2e36fe9c17\": () => (/* reexport safe */ D_Projects_task_ai_manager3_lib_telegram_service_ts__WEBPACK_IMPORTED_MODULE_1__.sendTelegramMessage)\n/* harmony export */ });\n/* harmony import */ var D_Projects_task_ai_manager3_lib_email_service_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/email-service.ts */ \"(action-browser)/./lib/email-service.ts\");\n/* harmony import */ var D_Projects_task_ai_manager3_lib_telegram_service_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/telegram-service.ts */ \"(action-browser)/./lib/telegram-service.ts\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Cemail-service.ts%22%2C%5B%7B%22id%22%3A%2240a3470f6f9b2080776491b136ec9b2d0412ffed56%22%2C%22exportedName%22%3A%22sendReminderEmail%22%7D%5D%5D%2C%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctelegram-service.ts%22%2C%5B%7B%22id%22%3A%220004d75c0cbd3b51b50db872a1bb33175142ec7a81%22%2C%22exportedName%22%3A%22getTelegramBotInfo%22%7D%2C%7B%22id%22%3A%224036d186bba33a6111e3a1a311e5529f6815449ecf%22%2C%22exportedName%22%3A%22validateTelegramChatId%22%7D%2C%7B%22id%22%3A%2240d1ad41895a6b2d78824c2a72a188ce00547f82db%22%2C%22exportedName%22%3A%22sendTelegramReminder%22%7D%2C%7B%22id%22%3A%2240ddd58023aecf11b09e60f5964dbdfe2e36fe9c17%22%2C%22exportedName%22%3A%22sendTelegramMessage%22%7D%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1de822cf709b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcdGFzay1haS1tYW5hZ2VyM1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFkZTgyMmNmNzA5YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_cyrillic_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\",\"cyrillic\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\",\\\"cyrillic\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_cyrillic_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_cyrillic_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-jetbrains\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-jetbrains\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _lib_translations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/translations */ \"(rsc)/./lib/translations.tsx\");\n/* harmony import */ var _lib_socket_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/socket-context */ \"(rsc)/./lib/socket-context.tsx\");\n/* harmony import */ var _components_reminder_initializer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/reminder-initializer */ \"(rsc)/./components/reminder-initializer.tsx\");\n\n\n\n\n\n\n // Import SocketProvider\n\nconst metadata = {\n    title: \"Minimumist - AI Task Manager\",\n    description: \"Minimalist task management app with AI assistant\",\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_cyrillic_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_7___default().variable)} font-sans`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_socket_context__WEBPACK_IMPORTED_MODULE_4__.SocketProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_translations__WEBPACK_IMPORTED_MODULE_3__.TranslationProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                        attribute: \"class\",\n                        defaultTheme: \"system\",\n                        enableSystem: true,\n                        disableTransitionOnChange: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reminder_initializer__WEBPACK_IMPORTED_MODULE_5__.ReminderInitializer, {}, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/reminder-initializer.tsx":
/*!*********************************************!*\
  !*** ./components/reminder-initializer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReminderInitializer: () => (/* binding */ ReminderInitializer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ReminderInitializer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ReminderInitializer() from the server but ReminderInitializer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\components\\reminder-initializer.tsx",
"ReminderInitializer",
);

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./lib/socket-context.tsx":
/*!********************************!*\
  !*** ./lib/socket-context.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),
/* harmony export */   useSocket: () => (/* binding */ useSocket)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useSocket = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSocket() from the server but useSocket is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\lib\\socket-context.tsx",
"useSocket",
);const SocketProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SocketProvider() from the server but SocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\lib\\socket-context.tsx",
"SocketProvider",
);

/***/ }),

/***/ "(rsc)/./lib/translations.tsx":
/*!******************************!*\
  !*** ./lib/translations.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TranslationProvider: () => (/* binding */ TranslationProvider),
/* harmony export */   useTranslation: () => (/* binding */ useTranslation)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const TranslationProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call TranslationProvider() from the server but TranslationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\lib\\translations.tsx",
"TranslationProvider",
);const useTranslation = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTranslation() from the server but useTranslation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\lib\\translations.tsx",
"useTranslation",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZfbm90LWZvdW5kJTJGcGFnZSZwYWdlPSUyRl9ub3QtZm91bmQlMkZwYWdlJmFwcFBhdGhzPSZwYWdlUGF0aD1ub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZub3QtZm91bmQtZXJyb3IuanMmYXBwRGlyPUQlM0ElNUNQcm9qZWN0cyU1Q3Rhc2stYWktbWFuYWdlcjMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNQcm9qZWN0cyU1Q3Rhc2stYWktbWFuYWdlcjMmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHdCQUF3QiwwTkFBZ0Y7QUFDeEcsc0JBQXNCLDRJQUFvRjtBQUMxRyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLGdPQUFtRjtBQUd2RztBQUdBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsV0FBVyxJQUFJO0FBQ2YsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBub3RGb3VuZDAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUxID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0c1xcXFx0YXNrLWFpLW1hbmFnZXIzXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMiA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTMgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGU0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zc3InXG59O1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgICAgY2hpbGRyZW46IFtcIi9fbm90LWZvdW5kXCIsIHtcbiAgICAgICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICAgICAgcGFnZTogW1xuICAgICAgICAgICAgICAgIG5vdEZvdW5kMCxcbiAgICAgICAgICAgICAgICBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIlxuICAgICAgICAgICAgICBdXG4gICAgICAgICAgICB9XVxuICAgICAgICAgIH0sIHt9XVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlMSwgXCJEOlxcXFxQcm9qZWN0c1xcXFx0YXNrLWFpLW1hbmFnZXIzXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4nZm9yYmlkZGVuJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiXSxcbid1bmF1dGhvcml6ZWQnOiBbbW9kdWxlNCwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9fbm90LWZvdW5kL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL19ub3QtZm91bmRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Creminder-initializer.tsx%22%2C%22ids%22%3A%5B%22ReminderInitializer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Creminder-initializer.tsx%22%2C%22ids%22%3A%5B%22ReminderInitializer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/reminder-initializer.tsx */ \"(rsc)/./components/reminder-initializer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/socket-context.tsx */ \"(rsc)/./lib/socket-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/translations.tsx */ \"(rsc)/./lib/translations.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Creminder-initializer.tsx%22%2C%22ids%22%3A%5B%22ReminderInitializer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/reminder-initializer.tsx":
/*!*********************************************!*\
  !*** ./components/reminder-initializer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReminderInitializer: () => (/* binding */ ReminderInitializer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_reminder_scheduler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/reminder-scheduler */ \"(ssr)/./lib/reminder-scheduler.ts\");\n/* __next_internal_client_entry_do_not_use__ ReminderInitializer auto */ \n\nfunction ReminderInitializer() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ReminderInitializer.useEffect\": ()=>{\n            // Инициализируем планировщик напоминаний при загрузке приложения\n            const timer = setTimeout({\n                \"ReminderInitializer.useEffect.timer\": ()=>{\n                    (0,_lib_reminder_scheduler__WEBPACK_IMPORTED_MODULE_1__.initializeReminderScheduler)();\n                }\n            }[\"ReminderInitializer.useEffect.timer\"], 1000); // Небольшая задержка для полной загрузки приложения\n            return ({\n                \"ReminderInitializer.useEffect\": ()=>clearTimeout(timer)\n            })[\"ReminderInitializer.useEffect\"];\n        }\n    }[\"ReminderInitializer.useEffect\"], []);\n    // Этот компонент не рендерит ничего видимого\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3JlbWluZGVyLWluaXRpYWxpemVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O3lFQUVrQztBQUNxQztBQUVoRSxTQUFTRTtJQUNkRixnREFBU0E7eUNBQUM7WUFDUixpRUFBaUU7WUFDakUsTUFBTUcsUUFBUUM7dURBQVc7b0JBQ3ZCSCxvRkFBMkJBO2dCQUM3QjtzREFBRyxPQUFPLG9EQUFvRDtZQUU5RDtpREFBTyxJQUFNSSxhQUFhRjs7UUFDNUI7d0NBQUcsRUFBRTtJQUVMLDZDQUE2QztJQUM3QyxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcdGFzay1haS1tYW5hZ2VyM1xcY29tcG9uZW50c1xccmVtaW5kZXItaW5pdGlhbGl6ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBpbml0aWFsaXplUmVtaW5kZXJTY2hlZHVsZXIgfSBmcm9tICdAL2xpYi9yZW1pbmRlci1zY2hlZHVsZXInO1xuXG5leHBvcnQgZnVuY3Rpb24gUmVtaW5kZXJJbml0aWFsaXplcigpIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyDQmNC90LjRhtC40LDQu9C40LfQuNGA0YPQtdC8INC/0LvQsNC90LjRgNC+0LLRidC40Log0L3QsNC/0L7QvNC40L3QsNC90LjQuSDQv9GA0Lgg0LfQsNCz0YDRg9C30LrQtSDQv9GA0LjQu9C+0LbQtdC90LjRj1xuICAgIGNvbnN0IHRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBpbml0aWFsaXplUmVtaW5kZXJTY2hlZHVsZXIoKTtcbiAgICB9LCAxMDAwKTsgLy8g0J3QtdCx0L7Qu9GM0YjQsNGPINC30LDQtNC10YDQttC60LAg0LTQu9GPINC/0L7Qu9C90L7QuSDQt9Cw0LPRgNGD0LfQutC4INC/0YDQuNC70L7QttC10L3QuNGPXG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgfSwgW10pO1xuXG4gIC8vINCt0YLQvtGCINC60L7QvNC/0L7QvdC10L3RgiDQvdC1INGA0LXQvdC00LXRgNC40YIg0L3QuNGH0LXQs9C+INCy0LjQtNC40LzQvtCz0L5cbiAgcmV0dXJuIG51bGw7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiaW5pdGlhbGl6ZVJlbWluZGVyU2NoZWR1bGVyIiwiUmVtaW5kZXJJbml0aWFsaXplciIsInRpbWVyIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/reminder-initializer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBSVY7QUFFYixTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcdGFzay1haS1tYW5hZ2VyM1xcY29tcG9uZW50c1xcdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyLFxuICB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyxcbn0gZnJvbSAnbmV4dC10aGVtZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/deadline-reminder-service.ts":
/*!******************************************!*\
  !*** ./lib/deadline-reminder-service.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkAndSendAllReminders: () => (/* binding */ checkAndSendAllReminders),\n/* harmony export */   getDeadlineItemsFromStorage: () => (/* binding */ getDeadlineItemsFromStorage),\n/* harmony export */   getUserReminderSettings: () => (/* binding */ getUserReminderSettings),\n/* harmony export */   sendDeadlineReminder: () => (/* binding */ sendDeadlineReminder),\n/* harmony export */   shouldSendReminder: () => (/* binding */ shouldSendReminder)\n/* harmony export */ });\n/* harmony import */ var _email_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./email-service */ \"(ssr)/./lib/email-service.ts\");\n/* harmony import */ var _telegram_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./telegram-service */ \"(ssr)/./lib/telegram-service.ts\");\n/* __next_internal_client_entry_do_not_use__ getUserReminderSettings,shouldSendReminder,sendDeadlineReminder,getDeadlineItemsFromStorage,checkAndSendAllReminders auto */ \n\n// Функция для получения настроек напоминаний пользователя\nfunction getUserReminderSettings(userId) {\n    if (true) {\n        return {\n            userId,\n            emailReminders: false,\n            telegramReminders: false,\n            emailForReminders: '',\n            telegramUsername: '',\n            reminderTimes: [\n                1440,\n                60,\n                15\n            ]\n        };\n    }\n    return {\n        userId,\n        emailReminders: localStorage.getItem('emailReminders') === 'true',\n        telegramReminders: localStorage.getItem('telegramReminders') === 'true',\n        emailForReminders: localStorage.getItem('emailForReminders') || '',\n        telegramUsername: localStorage.getItem('telegramUsername') || '',\n        reminderTimes: JSON.parse(localStorage.getItem('reminderTimes') || '[1440, 60, 15]')\n    };\n}\n// Функция для проверки, нужно ли отправить напоминание\nfunction shouldSendReminder(item, reminderTimeMinutes) {\n    const now = new Date();\n    const dueDate = new Date(item.dueDate);\n    const timeDifference = dueDate.getTime() - now.getTime();\n    const minutesUntilDue = Math.floor(timeDifference / (1000 * 60));\n    // Проверяем, что элемент не завершен и время напоминания подошло\n    return item.status !== 'done' && minutesUntilDue <= reminderTimeMinutes && minutesUntilDue > reminderTimeMinutes - 5 // Окно в 5 минут для отправки\n    ;\n}\n// Функция для отправки напоминания о дедлайне\nasync function sendDeadlineReminder(item, settings, reminderTimeMinutes) {\n    try {\n        let emailSent = false;\n        let telegramSent = false;\n        const timeUntilDue = getTimeUntilDueText(item.dueDate, reminderTimeMinutes);\n        const urgencyLevel = getUrgencyLevel(reminderTimeMinutes);\n        // Отправляем email-напоминание\n        if (settings.emailReminders && settings.emailForReminders) {\n            const emailData = {\n                userId: settings.userId,\n                userEmail: settings.emailForReminders,\n                userName: item.assigneeName || 'Пользователь',\n                subject: `${urgencyLevel} Напоминание о дедлайне: ${item.title}`,\n                message: `У вас есть ${item.type === 'task' ? 'задача' : item.type === 'subtask' ? 'подзадача' : item.type === 'project' ? 'проект' : 'событие'} \"${item.title}\", которая должна быть выполнена ${timeUntilDue}.`,\n                itemType: item.type,\n                itemId: item.id,\n                itemTitle: item.title,\n                dueDate: new Date(item.dueDate),\n                projectName: item.projectName\n            };\n            emailSent = await (0,_email_service__WEBPACK_IMPORTED_MODULE_0__.sendReminderEmail)(emailData);\n        }\n        // Отправляем Telegram-напоминание\n        if (settings.telegramReminders && settings.telegramUsername) {\n            const telegramData = {\n                userId: settings.userId,\n                telegramChatId: settings.telegramUsername,\n                userName: item.assigneeName || 'Пользователь',\n                subject: `${urgencyLevel} Напоминание о дедлайне`,\n                message: `У вас есть ${item.type === 'task' ? 'задача' : item.type === 'subtask' ? 'подзадача' : item.type === 'project' ? 'проект' : 'событие'} \"${item.title}\", которая должна быть выполнена ${timeUntilDue}.`,\n                itemType: item.type,\n                itemId: item.id,\n                itemTitle: item.title,\n                dueDate: new Date(item.dueDate),\n                projectName: item.projectName\n            };\n            telegramSent = await (0,_telegram_service__WEBPACK_IMPORTED_MODULE_1__.sendTelegramReminder)(telegramData);\n        }\n        // Логируем отправку напоминания\n        console.log(`Deadline reminder sent for ${item.type} \"${item.title}\" (${reminderTimeMinutes} minutes before due)`);\n        return emailSent || telegramSent;\n    } catch (error) {\n        console.error('Error sending deadline reminder:', error);\n        return false;\n    }\n}\n// Функция для получения текста времени до дедлайна\nfunction getTimeUntilDueText(dueDate, reminderTimeMinutes) {\n    if (reminderTimeMinutes >= 1440) {\n        const days = Math.floor(reminderTimeMinutes / 1440);\n        return `через ${days} ${days === 1 ? 'день' : days < 5 ? 'дня' : 'дней'}`;\n    } else if (reminderTimeMinutes >= 60) {\n        const hours = Math.floor(reminderTimeMinutes / 60);\n        return `через ${hours} ${hours === 1 ? 'час' : hours < 5 ? 'часа' : 'часов'}`;\n    } else {\n        return `через ${reminderTimeMinutes} ${reminderTimeMinutes === 1 ? 'минуту' : reminderTimeMinutes < 5 ? 'минуты' : 'минут'}`;\n    }\n}\n// Функция для получения уровня срочности\nfunction getUrgencyLevel(reminderTimeMinutes) {\n    if (reminderTimeMinutes <= 15) {\n        return '🚨 СРОЧНО!';\n    } else if (reminderTimeMinutes <= 60) {\n        return '⚠️ ВАЖНО!';\n    } else if (reminderTimeMinutes <= 1440) {\n        return '📅';\n    } else {\n        return '📋';\n    }\n}\n// Функция для получения всех элементов с дедлайнами из localStorage\nfunction getDeadlineItemsFromStorage() {\n    if (true) {\n        return [];\n    }\n    const items = [];\n    try {\n        // Получаем проекты\n        const projectsData = localStorage.getItem('projects');\n        if (projectsData) {\n            const projects = JSON.parse(projectsData);\n            projects.forEach((project)=>{\n                if (project.dueDate) {\n                    items.push({\n                        id: project.id,\n                        title: project.name,\n                        description: project.description,\n                        dueDate: new Date(project.dueDate),\n                        type: 'project',\n                        projectId: project.id,\n                        projectName: project.name,\n                        status: project.status || 'todo',\n                        priority: project.priority\n                    });\n                }\n            });\n        }\n        // Получаем задачи\n        const tasksData = localStorage.getItem('tasksData');\n        if (tasksData) {\n            const tasks = JSON.parse(tasksData);\n            Object.keys(tasks).forEach((projectId)=>{\n                const projectTasks = tasks[projectId];\n                Object.keys(projectTasks).forEach((status)=>{\n                    projectTasks[status].forEach((task)=>{\n                        if (task.dueDate) {\n                            items.push({\n                                id: task.id,\n                                title: task.title,\n                                description: task.description,\n                                dueDate: new Date(task.dueDate),\n                                type: 'task',\n                                projectId: projectId,\n                                projectName: task.projectName,\n                                assigneeId: task.assignee,\n                                assigneeName: task.assigneeName,\n                                assigneeEmail: task.assigneeEmail,\n                                status: status,\n                                priority: task.priority\n                            });\n                            // Добавляем подзадачи\n                            if (task.subtasks) {\n                                task.subtasks.forEach((subtask)=>{\n                                    if (subtask.dueDate) {\n                                        items.push({\n                                            id: subtask.id,\n                                            title: subtask.title,\n                                            description: subtask.description,\n                                            dueDate: new Date(subtask.dueDate),\n                                            type: 'subtask',\n                                            projectId: projectId,\n                                            projectName: task.projectName,\n                                            assigneeId: subtask.assignee,\n                                            assigneeName: subtask.assigneeName,\n                                            assigneeEmail: subtask.assigneeEmail,\n                                            status: subtask.completed ? 'done' : 'todo',\n                                            priority: subtask.priority\n                                        });\n                                    }\n                                });\n                            }\n                        }\n                    });\n                });\n            });\n        }\n        // Получаем события календаря\n        const eventsData = localStorage.getItem('calendarEvents');\n        if (eventsData) {\n            const events = JSON.parse(eventsData);\n            events.forEach((event)=>{\n                items.push({\n                    id: event.id,\n                    title: event.title,\n                    description: event.description,\n                    dueDate: new Date(event.end || event.start),\n                    type: 'event',\n                    projectId: event.projectId,\n                    projectName: event.projectName,\n                    status: 'todo',\n                    priority: event.priority\n                });\n            });\n        }\n    } catch (error) {\n        console.error('Error getting deadline items from storage:', error);\n    }\n    return items;\n}\n// Функция для проверки и отправки всех напоминаний\nasync function checkAndSendAllReminders() {\n    try {\n        const items = getDeadlineItemsFromStorage();\n        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');\n        if (!currentUser.id) {\n            console.log('No current user found, skipping reminder check');\n            return;\n        }\n        const settings = getUserReminderSettings(currentUser.id);\n        if (!settings.emailReminders && !settings.telegramReminders) {\n            console.log('Reminders are disabled for user', currentUser.id);\n            return;\n        }\n        console.log(`Checking ${items.length} items for reminders...`);\n        for (const item of items){\n            for (const reminderTime of settings.reminderTimes){\n                if (shouldSendReminder(item, reminderTime)) {\n                    await sendDeadlineReminder(item, settings, reminderTime);\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Error checking and sending reminders:', error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/deadline-reminder-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/email-service.ts":
/*!******************************!*\
  !*** ./lib/email-service.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendReminderEmail: () => (/* binding */ sendReminderEmail)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"40a3470f6f9b2080776491b136ec9b2d0412ffed56\":\"sendReminderEmail\"} */ \nvar sendReminderEmail = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40a3470f6f9b2080776491b136ec9b2d0412ffed56\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"sendReminderEmail\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvZW1haWwtc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0lBOERzQkEsa0NBQUFBLDZGQUFBQSwrQ0FBQUEsOEVBQUFBLFVBQUFBLG9GQUFBQSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFx0YXNrLWFpLW1hbmFnZXIzXFxsaWJcXGVtYWlsLXNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc2VydmVyXCI7XG5cbmltcG9ydCBub2RlbWFpbGVyIGZyb20gJ25vZGVtYWlsZXInO1xuLy8gaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnLi9wcmlzbWEnOyAvLyDQl9Cw0LrQvtC80LzQtdC90YLQuNGA0L7QstCw0L3QviDQtNC+INC90LDRgdGC0YDQvtC50LrQuCDQkdCUXG5cbi8vINCY0L3RgtC10YDRhNC10LnRgSDQtNC70Y8g0L3QsNGB0YLRgNC+0LXQuiBTTVRQXG5pbnRlcmZhY2UgRW1haWxTZXR0aW5ncyB7XG4gIHNtdHBTZXJ2ZXI6IHN0cmluZztcbiAgc210cFBvcnQ6IHN0cmluZztcbiAgc210cFVzZXJuYW1lOiBzdHJpbmc7XG4gIHNtdHBQYXNzd29yZDogc3RyaW5nO1xuICBmcm9tRW1haWw6IHN0cmluZztcbn1cblxuLy8g0JjQvdGC0LXRgNGE0LXQudGBINC00LvRjyDQtNCw0L3QvdGL0YUg0L3QsNC/0L7QvNC40L3QsNC90LjRj1xuZXhwb3J0IGludGVyZmFjZSBSZW1pbmRlckRhdGEge1xuICB1c2VySWQ6IHN0cmluZztcbiAgdXNlckVtYWlsOiBzdHJpbmc7XG4gIHVzZXJOYW1lOiBzdHJpbmc7XG4gIHN1YmplY3Q6IHN0cmluZztcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBpdGVtVHlwZTogJ2V2ZW50JyB8ICd0YXNrJyB8ICdzdWJ0YXNrJyB8ICdwcm9qZWN0JztcbiAgaXRlbUlkOiBzdHJpbmc7XG4gIGl0ZW1UaXRsZTogc3RyaW5nO1xuICBkdWVEYXRlPzogRGF0ZTtcbiAgcHJvamVjdE5hbWU/OiBzdHJpbmc7XG59XG5cbi8vINCf0L7Qu9GD0YfQtdC90LjQtSDQvdCw0YHRgtGA0L7QtdC6IFNNVFAg0LjQtyDQsdCw0LfRiyDQtNCw0L3QvdGL0YUg0LjQu9C4INC40Lcg0L/QtdGA0LXQvNC10L3QvdGL0YUg0L7QutGA0YPQttC10L3QuNGPXG5hc3luYyBmdW5jdGlvbiBnZXRFbWFpbFNldHRpbmdzKCk6IFByb21pc2U8RW1haWxTZXR0aW5ncz4ge1xuICAvLyDQkiDRgNC10LDQu9GM0L3QvtC8INC/0YDQuNC70L7QttC10L3QuNC4INC30LTQtdGB0Ywg0LHRg9C00LXRgiDQt9Cw0L/RgNC+0YEg0Log0LHQsNC30LUg0LTQsNC90L3Ri9GFINC00LvRjyDQv9C+0LvRg9GH0LXQvdC40Y8g0L3QsNGB0YLRgNC+0LXQulxuICAvLyDQlNC70Y8g0L/RgNC40LzQtdGA0LAg0LjRgdC/0L7Qu9GM0LfRg9C10Lwg0L/QtdGA0LXQvNC10L3QvdGL0LUg0L7QutGA0YPQttC10L3QuNGPXG4gIHJldHVybiB7XG4gICAgc210cFNlcnZlcjogcHJvY2Vzcy5lbnYuU01UUF9TRVJWRVIgfHwgJycsXG4gICAgc210cFBvcnQ6IHByb2Nlc3MuZW52LlNNVFBfUE9SVCB8fCAnNTg3JyxcbiAgICBzbXRwVXNlcm5hbWU6IHByb2Nlc3MuZW52LlNNVFBfVVNFUk5BTUUgfHwgJycsXG4gICAgc210cFBhc3N3b3JkOiBwcm9jZXNzLmVudi5TTVRQX1BBU1NXT1JEIHx8ICcnLFxuICAgIGZyb21FbWFpbDogcHJvY2Vzcy5lbnYuRlJPTV9FTUFJTCB8fCAnbm9yZXBseUBhaXRhc2t0cmFja2VyLmNvbScsXG4gIH07XG59XG5cbi8vINCh0L7Qt9C00LDQvdC40LUg0YLRgNCw0L3RgdC/0L7RgNGC0LAg0LTQu9GPINC+0YLQv9GA0LDQstC60Lgg0L/QuNGB0LXQvFxuYXN5bmMgZnVuY3Rpb24gY3JlYXRlVHJhbnNwb3J0KCkge1xuICBjb25zdCBzZXR0aW5ncyA9IGF3YWl0IGdldEVtYWlsU2V0dGluZ3MoKTtcblxuICAvLyDQn9GA0L7QstC10YDRj9C10LwsINGH0YLQviDQstGB0LUg0L3QtdC+0LHRhdC+0LTQuNC80YvQtSDQvdCw0YHRgtGA0L7QudC60Lgg0YPQutCw0LfQsNC90YtcbiAgaWYgKCFzZXR0aW5ncy5zbXRwU2VydmVyIHx8ICFzZXR0aW5ncy5zbXRwVXNlcm5hbWUgfHwgIXNldHRpbmdzLnNtdHBQYXNzd29yZCkge1xuICAgIHRocm93IG5ldyBFcnJvcignU01UUCBzZXR0aW5ncyBhcmUgbm90IGNvbmZpZ3VyZWQnKTtcbiAgfVxuXG4gIHJldHVybiBub2RlbWFpbGVyLmNyZWF0ZVRyYW5zcG9ydCh7XG4gICAgaG9zdDogc2V0dGluZ3Muc210cFNlcnZlcixcbiAgICBwb3J0OiBwYXJzZUludChzZXR0aW5ncy5zbXRwUG9ydCwgMTApLFxuICAgIHNlY3VyZTogcGFyc2VJbnQoc2V0dGluZ3Muc210cFBvcnQsIDEwKSA9PT0gNDY1LCAvLyB0cnVlINC00LvRjyDQv9C+0YDRgtCwIDQ2NSwgZmFsc2Ug0LTQu9GPINC00YDRg9Cz0LjRhSDQv9C+0YDRgtC+0LJcbiAgICBhdXRoOiB7XG4gICAgICB1c2VyOiBzZXR0aW5ncy5zbXRwVXNlcm5hbWUsXG4gICAgICBwYXNzOiBzZXR0aW5ncy5zbXRwUGFzc3dvcmQsXG4gICAgfSxcbiAgfSk7XG59XG5cbi8vINCk0YPQvdC60YbQuNGPINC00LvRjyDQvtGC0L/RgNCw0LLQutC4INC90LDQv9C+0LzQuNC90LDQvdC40Y8g0L/QviDRjdC70LXQutGC0YDQvtC90L3QvtC5INC/0L7Rh9GC0LVcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZW5kUmVtaW5kZXJFbWFpbChyZW1pbmRlckRhdGE6IFJlbWluZGVyRGF0YSk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICB0cnkge1xuICAgIC8vINCf0L7Qu9GD0YfQsNC10Lwg0L3QsNGB0YLRgNC+0LnQutC4INGN0LvQtdC60YLRgNC+0L3QvdC+0Lkg0L/QvtGH0YLRi1xuICAgIGNvbnN0IHNldHRpbmdzID0gYXdhaXQgZ2V0RW1haWxTZXR0aW5ncygpO1xuXG4gICAgLy8g0J/RgNC+0LLQtdGA0Y/QtdC8LCDRh9GC0L4g0LLRgdC1INC90LXQvtCx0YXQvtC00LjQvNGL0LUg0L3QsNGB0YLRgNC+0LnQutC4INGD0LrQsNC30LDQvdGLXG4gICAgaWYgKCFzZXR0aW5ncy5zbXRwU2VydmVyIHx8ICFzZXR0aW5ncy5zbXRwVXNlcm5hbWUgfHwgIXNldHRpbmdzLnNtdHBQYXNzd29yZCkge1xuICAgICAgY29uc29sZS5lcnJvcignU01UUCBzZXR0aW5ncyBhcmUgbm90IGNvbmZpZ3VyZWQnKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICAvLyDQn9GA0L7QstC10YDRj9C10LwsINGH0YLQviDRgyDQv9C+0LvRjNC30L7QstCw0YLQtdC70Y8g0LXRgdGC0YwgZW1haWxcbiAgICBpZiAoIXJlbWluZGVyRGF0YS51c2VyRW1haWwpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYFVzZXIgJHtyZW1pbmRlckRhdGEudXNlcklkfSBkb2VzIG5vdCBoYXZlIGFuIGVtYWlsIGFkZHJlc3NgKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICAvLyDQodC+0LfQtNCw0LXQvCDRgtGA0LDQvdGB0L/QvtGA0YJcbiAgICBjb25zdCB0cmFuc3BvcnRlciA9IGF3YWl0IGNyZWF0ZVRyYW5zcG9ydCgpO1xuXG4gICAgLy8g0KTQvtGA0LzQuNGA0YPQtdC8IEhUTUwg0LTQu9GPINC/0LjRgdGM0LzQsFxuICAgIGNvbnN0IGh0bWxDb250ZW50ID0gYFxuICAgICAgPGRpdiBzdHlsZT1cImZvbnQtZmFtaWx5OiBBcmlhbCwgc2Fucy1zZXJpZjsgbWF4LXdpZHRoOiA2MDBweDsgbWFyZ2luOiAwIGF1dG87XCI+XG4gICAgICAgIDxoMiBzdHlsZT1cImNvbG9yOiAjMzMzO1wiPtCd0LDQv9C+0LzQuNC90LDQvdC40LU8L2gyPlxuICAgICAgICA8cD7Ql9C00YDQsNCy0YHRgtCy0YPQudGC0LUsICR7cmVtaW5kZXJEYXRhLnVzZXJOYW1lIHx8ICfQv9C+0LvRjNC30L7QstCw0YLQtdC70YwnfSE8L3A+XG4gICAgICAgIDxwPiR7cmVtaW5kZXJEYXRhLm1lc3NhZ2V9PC9wPlxuICAgICAgICA8ZGl2IHN0eWxlPVwiYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsgcGFkZGluZzogMTVweDsgYm9yZGVyLXJhZGl1czogNXB4OyBtYXJnaW46IDIwcHggMDtcIj5cbiAgICAgICAgICA8aDMgc3R5bGU9XCJtYXJnaW4tdG9wOiAwOyBjb2xvcjogIzMzMztcIj4ke3JlbWluZGVyRGF0YS5pdGVtVGl0bGV9PC9oMz5cbiAgICAgICAgICAke3JlbWluZGVyRGF0YS5wcm9qZWN0TmFtZSA/IGA8cD48c3Ryb25nPtCf0YDQvtC10LrRgjo8L3N0cm9uZz4gJHtyZW1pbmRlckRhdGEucHJvamVjdE5hbWV9PC9wPmAgOiAnJ31cbiAgICAgICAgICAke3JlbWluZGVyRGF0YS5kdWVEYXRlID8gYDxwPjxzdHJvbmc+0KHRgNC+0Lo6PC9zdHJvbmc+ICR7cmVtaW5kZXJEYXRhLmR1ZURhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdydS1SVScpfTwvcD5gIDogJyd9XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8cD7QoSDRg9Cy0LDQttC10L3QuNC10LwsPGJyPtCa0L7QvNCw0L3QtNCwIEFJIFRhc2sgVHJhY2tlcjwvcD5cbiAgICAgIDwvZGl2PlxuICAgIGA7XG5cbiAgICAvLyDQntGC0L/RgNCw0LLQu9GP0LXQvCDQv9C40YHRjNC80L5cbiAgICBjb25zdCBpbmZvID0gYXdhaXQgdHJhbnNwb3J0ZXIuc2VuZE1haWwoe1xuICAgICAgZnJvbTogYFwiQUkgVGFzayBUcmFja2VyXCIgPCR7c2V0dGluZ3MuZnJvbUVtYWlsfT5gLFxuICAgICAgdG86IHJlbWluZGVyRGF0YS51c2VyRW1haWwsXG4gICAgICBzdWJqZWN0OiByZW1pbmRlckRhdGEuc3ViamVjdCxcbiAgICAgIGh0bWw6IGh0bWxDb250ZW50LFxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coYEVtYWlsIHNlbnQgdG8gJHtyZW1pbmRlckRhdGEudXNlckVtYWlsfTogJHtpbmZvLm1lc3NhZ2VJZH1gKTtcblxuICAgIC8vINCX0LDQv9C40YHRi9Cy0LDQtdC8INC40L3RhNC+0YDQvNCw0YbQuNGOINC+0LEg0L7RgtC/0YDQsNCy0LvQtdC90L3QvtC8INC90LDQv9C+0LzQuNC90LDQvdC40Lgg0LIg0LvQvtCzXG4gICAgYXdhaXQgbG9nUmVtaW5kZXJTZW50KHJlbWluZGVyRGF0YSk7XG5cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZW5kaW5nIHJlbWluZGVyIGVtYWlsOicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn1cblxuLy8g0KTRg9C90LrRhtC40Y8g0LTQu9GPINC30LDQv9C40YHQuCDQuNC90YTQvtGA0LzQsNGG0LjQuCDQvtCxINC+0YLQv9GA0LDQstC70LXQvdC90L7QvCDQvdCw0L/QvtC80LjQvdCw0L3QuNC4INCyINC70L7Qs1xuYXN5bmMgZnVuY3Rpb24gbG9nUmVtaW5kZXJTZW50KHJlbWluZGVyRGF0YTogUmVtaW5kZXJEYXRhKTogUHJvbWlzZTx2b2lkPiB7XG4gIHRyeSB7XG4gICAgLy8g0JIg0YDQtdCw0LvRjNC90L7QvCDQv9GA0LjQu9C+0LbQtdC90LjQuCDQt9C00LXRgdGMINCx0YPQtNC10YIg0LfQsNC/0LjRgdGMINCyINCx0LDQt9GDINC00LDQvdC90YvRhVxuICAgIGNvbnNvbGUubG9nKGBSZW1pbmRlciBzZW50IHRvICR7cmVtaW5kZXJEYXRhLnVzZXJFbWFpbH0gZm9yICR7cmVtaW5kZXJEYXRhLml0ZW1UeXBlfSBcIiR7cmVtaW5kZXJEYXRhLml0ZW1UaXRsZX1cImApO1xuXG4gICAgLy8g0J/RgNC40LzQtdGAINC30LDQv9C40YHQuCDQsiDQsdCw0LfRgyDQtNCw0L3QvdGL0YUgKNC30LDQutC+0LzQvNC10L3RgtC40YDQvtCy0LDQvdC+LCDRgtCw0Log0LrQsNC6INC80L7QtNC10LvRjCDQvdC1INGB0YPRidC10YHRgtCy0YPQtdGCKVxuICAgIC8qXG4gICAgYXdhaXQgcHJpc21hLnJlbWluZGVyTG9nLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIHVzZXJJZDogcmVtaW5kZXJEYXRhLnVzZXJJZCxcbiAgICAgICAgaXRlbVR5cGU6IHJlbWluZGVyRGF0YS5pdGVtVHlwZSxcbiAgICAgICAgaXRlbUlkOiByZW1pbmRlckRhdGEuaXRlbUlkLFxuICAgICAgICBzZW50QXQ6IG5ldyBEYXRlKCksXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICB9LFxuICAgIH0pO1xuICAgICovXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9nZ2luZyByZW1pbmRlcjonLCBlcnJvcik7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJzZW5kUmVtaW5kZXJFbWFpbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/email-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/enhanced-reminder-service.ts":
/*!******************************************!*\
  !*** ./lib/enhanced-reminder-service.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupOldData: () => (/* binding */ cleanupOldData),\n/* harmony export */   createRemindersForItem: () => (/* binding */ createRemindersForItem),\n/* harmony export */   getDefaultUserPreferences: () => (/* binding */ getDefaultUserPreferences),\n/* harmony export */   getNotificationHistory: () => (/* binding */ getNotificationHistory),\n/* harmony export */   getUserReminderPreferences: () => (/* binding */ getUserReminderPreferences),\n/* harmony export */   processReminderQueue: () => (/* binding */ processReminderQueue),\n/* harmony export */   saveUserReminderPreferences: () => (/* binding */ saveUserReminderPreferences)\n/* harmony export */ });\n/* harmony import */ var _email_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./email-service */ \"(ssr)/./lib/email-service.ts\");\n/* harmony import */ var _telegram_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./telegram-service */ \"(ssr)/./lib/telegram-service.ts\");\n\n\n// In-memory storage for development (replace with database in production)\nlet reminderQueue = [];\nlet notificationHistory = [];\nlet userPreferences = new Map();\n// Default user preferences\nfunction getDefaultUserPreferences(userId) {\n    return {\n        userId,\n        emailReminders: false,\n        telegramReminders: false,\n        emailForReminders: '',\n        telegramChatId: '',\n        reminderTimes: [\n            1440,\n            60,\n            15\n        ],\n        timezone: 'UTC',\n        taskReminders: true,\n        projectReminders: true,\n        calendarReminders: true,\n        overdueReminders: true,\n        reminderFrequency: 'once',\n        quietHours: {\n            enabled: false,\n            start: '22:00',\n            end: '08:00'\n        }\n    };\n}\n// Get user reminder preferences\nfunction getUserReminderPreferences(userId) {\n    if (false) {}\n    // Server-side or fallback: use in-memory storage or defaults\n    return userPreferences.get(userId) || getDefaultUserPreferences(userId);\n}\n// Save user reminder preferences\nfunction saveUserReminderPreferences(preferences) {\n    if (false) {}\n    // Server-side: save to in-memory storage (replace with database in production)\n    userPreferences.set(preferences.userId, preferences);\n}\n// Create multiple reminders based on user preferences\nasync function createRemindersForItem(userId, itemType, itemId, dueDate, priority = 'medium') {\n    const preferences = getUserReminderPreferences(userId);\n    const reminders = [];\n    // Check if reminders are enabled for this item type\n    const isEnabled = itemType === 'task' && preferences.taskReminders || itemType === 'project' && preferences.projectReminders || itemType === 'event' && preferences.calendarReminders || itemType === 'subtask' && preferences.taskReminders;\n    if (!isEnabled || !preferences.emailReminders && !preferences.telegramReminders) {\n        return reminders;\n    }\n    // Create reminders for each configured time\n    for (const minutesBefore of preferences.reminderTimes){\n        const reminderTime = new Date(dueDate.getTime() - minutesBefore * 60 * 1000);\n        // Skip if reminder time is in the past\n        if (reminderTime <= new Date()) {\n            continue;\n        }\n        // Adjust for quiet hours if enabled\n        const adjustedTime = adjustForQuietHours(reminderTime, preferences);\n        const reminder = {\n            id: `reminder-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n            userId,\n            itemType,\n            itemId,\n            reminderTime: adjustedTime,\n            sent: false,\n            retryCount: 0,\n            maxRetries: 3,\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            reminderType: 'deadline',\n            priority\n        };\n        reminders.push(reminder);\n        // Add to queue\n        addToReminderQueue(reminder);\n    }\n    return reminders;\n}\n// Adjust reminder time for quiet hours\nfunction adjustForQuietHours(reminderTime, preferences) {\n    if (!preferences.quietHours.enabled) {\n        return reminderTime;\n    }\n    const time = reminderTime.toTimeString().substr(0, 5); // \"HH:MM\"\n    const startTime = preferences.quietHours.start;\n    const endTime = preferences.quietHours.end;\n    // Simple check if time falls within quiet hours\n    if (startTime > endTime) {\n        // Quiet hours span midnight (e.g., 22:00 to 08:00)\n        if (time >= startTime || time <= endTime) {\n            // Move to end of quiet hours\n            const adjusted = new Date(reminderTime);\n            const [endHour, endMinute] = endTime.split(':').map(Number);\n            adjusted.setHours(endHour, endMinute, 0, 0);\n            if (adjusted <= reminderTime) {\n                adjusted.setDate(adjusted.getDate() + 1);\n            }\n            return adjusted;\n        }\n    } else {\n        // Quiet hours within same day\n        if (time >= startTime && time <= endTime) {\n            const adjusted = new Date(reminderTime);\n            const [endHour, endMinute] = endTime.split(':').map(Number);\n            adjusted.setHours(endHour, endMinute, 0, 0);\n            return adjusted;\n        }\n    }\n    return reminderTime;\n}\n// Add reminder to queue\nfunction addToReminderQueue(reminder) {\n    const queueItem = {\n        id: `queue-${reminder.id}`,\n        reminder,\n        scheduledTime: reminder.reminderTime,\n        status: 'pending',\n        attempts: 0\n    };\n    reminderQueue.push(queueItem);\n    console.log(`Added reminder ${reminder.id} to queue for ${reminder.reminderTime.toISOString()}`);\n}\n// Process reminder queue\nasync function processReminderQueue() {\n    const now = new Date();\n    const pendingReminders = reminderQueue.filter((item)=>item.status === 'pending' && item.scheduledTime <= now);\n    console.log(`Processing ${pendingReminders.length} pending reminders`);\n    for (const queueItem of pendingReminders){\n        queueItem.status = 'processing';\n        queueItem.attempts++;\n        queueItem.lastAttempt = now;\n        try {\n            const success = await sendEnhancedReminder(queueItem.reminder);\n            if (success) {\n                queueItem.status = 'sent';\n                queueItem.reminder.sent = true;\n                queueItem.reminder.sentAt = now;\n                queueItem.reminder.updatedAt = now;\n            } else {\n                throw new Error('Failed to send reminder');\n            }\n        } catch (error) {\n            console.error(`Error sending reminder ${queueItem.reminder.id}:`, error);\n            queueItem.error = error instanceof Error ? error.message : 'Unknown error';\n            queueItem.reminder.error = queueItem.error;\n            queueItem.reminder.retryCount++;\n            queueItem.reminder.updatedAt = now;\n            if (queueItem.reminder.retryCount >= queueItem.reminder.maxRetries) {\n                queueItem.status = 'failed';\n                console.error(`Reminder ${queueItem.reminder.id} failed after ${queueItem.reminder.maxRetries} attempts`);\n            } else {\n                // Schedule retry\n                queueItem.status = 'pending';\n                queueItem.nextRetry = new Date(now.getTime() + queueItem.reminder.retryCount * 5 * 60 * 1000); // Exponential backoff\n                queueItem.scheduledTime = queueItem.nextRetry;\n                console.log(`Scheduled retry for reminder ${queueItem.reminder.id} at ${queueItem.nextRetry.toISOString()}`);\n            }\n        }\n    }\n}\n// Send enhanced reminder with both email and Telegram support\nasync function sendEnhancedReminder(reminder) {\n    const preferences = getUserReminderPreferences(reminder.userId);\n    let emailSent = false;\n    let telegramSent = false;\n    // Get item details\n    const itemDetails = await getItemDetails(reminder.itemType, reminder.itemId);\n    if (!itemDetails) {\n        throw new Error(`Item ${reminder.itemId} not found`);\n    }\n    // Send email reminder\n    if (preferences.emailReminders && preferences.emailForReminders) {\n        try {\n            const emailData = createEmailReminderData(reminder, itemDetails, preferences);\n            emailSent = await (0,_email_service__WEBPACK_IMPORTED_MODULE_0__.sendReminderEmail)(emailData);\n            // Log to history\n            addToNotificationHistory({\n                id: `email-${Date.now()}`,\n                userId: reminder.userId,\n                itemType: reminder.itemType,\n                itemId: reminder.itemId,\n                notificationType: 'email',\n                status: emailSent ? 'sent' : 'failed',\n                sentAt: new Date(),\n                error: emailSent ? undefined : 'Failed to send email'\n            });\n        } catch (error) {\n            console.error('Error sending email reminder:', error);\n        }\n    }\n    // Send Telegram reminder\n    if (preferences.telegramReminders && preferences.telegramChatId) {\n        try {\n            const telegramData = createTelegramReminderData(reminder, itemDetails, preferences);\n            telegramSent = await (0,_telegram_service__WEBPACK_IMPORTED_MODULE_1__.sendTelegramReminder)(telegramData);\n            // Log to history\n            addToNotificationHistory({\n                id: `telegram-${Date.now()}`,\n                userId: reminder.userId,\n                itemType: reminder.itemType,\n                itemId: reminder.itemId,\n                notificationType: 'telegram',\n                status: telegramSent ? 'sent' : 'failed',\n                sentAt: new Date(),\n                error: telegramSent ? undefined : 'Failed to send Telegram message'\n            });\n        } catch (error) {\n            console.error('Error sending Telegram reminder:', error);\n        }\n    }\n    return emailSent || telegramSent;\n}\n// Get item details based on type and ID\nasync function getItemDetails(itemType, itemId) {\n    // In a real application, this would fetch from database\n    // For now, return mock data or fetch from localStorage\n    if (false) {}\n    // Server-side fallback\n    return {\n        id: itemId,\n        title: `${itemType} ${itemId}`,\n        description: `Description for ${itemType} ${itemId}`\n    };\n}\n// Create email reminder data\nfunction createEmailReminderData(reminder, itemDetails, preferences) {\n    const urgencyMap = {\n        'low': '📅',\n        'medium': '⚠️',\n        'high': '🚨'\n    };\n    const urgencyPrefix = urgencyMap[reminder.priority];\n    return {\n        userId: reminder.userId,\n        userEmail: preferences.emailForReminders,\n        userName: 'User',\n        subject: `${urgencyPrefix} Reminder: ${itemDetails.title}`,\n        message: `You have a ${reminder.itemType} \"${itemDetails.title}\" that requires your attention.`,\n        itemType: reminder.itemType,\n        itemId: reminder.itemId,\n        itemTitle: itemDetails.title,\n        dueDate: new Date(itemDetails.dueDate || Date.now()),\n        projectName: itemDetails.projectName\n    };\n}\n// Create Telegram reminder data\nfunction createTelegramReminderData(reminder, itemDetails, preferences) {\n    const urgencyMap = {\n        'low': '📅',\n        'medium': '⚠️',\n        'high': '🚨'\n    };\n    const urgencyPrefix = urgencyMap[reminder.priority];\n    return {\n        userId: reminder.userId,\n        telegramChatId: preferences.telegramChatId,\n        userName: 'User',\n        subject: `${urgencyPrefix} Reminder`,\n        message: `You have a ${reminder.itemType} \"${itemDetails.title}\" that requires your attention.`,\n        itemType: reminder.itemType,\n        itemId: reminder.itemId,\n        itemTitle: itemDetails.title,\n        dueDate: new Date(itemDetails.dueDate || Date.now()),\n        projectName: itemDetails.projectName\n    };\n}\n// Add to notification history\nfunction addToNotificationHistory(entry) {\n    notificationHistory.push(entry);\n    // Keep only last 1000 entries\n    if (notificationHistory.length > 1000) {\n        notificationHistory = notificationHistory.slice(-1000);\n    }\n}\n// Get notification history for user\nfunction getNotificationHistory(userId, limit = 50) {\n    return notificationHistory.filter((entry)=>entry.userId === userId).sort((a, b)=>b.sentAt.getTime() - a.sentAt.getTime()).slice(0, limit);\n}\n// Clean up old reminders and history\nfunction cleanupOldData() {\n    const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);\n    // Remove old completed/failed reminders from queue\n    reminderQueue = reminderQueue.filter((item)=>item.status === 'pending' || item.lastAttempt && item.lastAttempt > oneWeekAgo);\n    // Remove old notification history\n    notificationHistory = notificationHistory.filter((entry)=>entry.sentAt > oneWeekAgo);\n    console.log(`Cleaned up old reminder data. Queue size: ${reminderQueue.length}, History size: ${notificationHistory.length}`);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/enhanced-reminder-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/reminder-scheduler.ts":
/*!***********************************!*\
  !*** ./lib/reminder-scheduler.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getReminderCheckInterval: () => (/* binding */ getReminderCheckInterval),\n/* harmony export */   getReminderSchedulerStats: () => (/* binding */ getReminderSchedulerStats),\n/* harmony export */   getSavedReminderCheckInterval: () => (/* binding */ getSavedReminderCheckInterval),\n/* harmony export */   initializeReminderScheduler: () => (/* binding */ initializeReminderScheduler),\n/* harmony export */   isReminderSchedulerRunning: () => (/* binding */ isReminderSchedulerRunning),\n/* harmony export */   manualReminderCheck: () => (/* binding */ manualReminderCheck),\n/* harmony export */   saveReminderCheckInterval: () => (/* binding */ saveReminderCheckInterval),\n/* harmony export */   setReminderCheckInterval: () => (/* binding */ setReminderCheckInterval),\n/* harmony export */   startReminderScheduler: () => (/* binding */ startReminderScheduler),\n/* harmony export */   stopReminderScheduler: () => (/* binding */ stopReminderScheduler),\n/* harmony export */   updateReminderScheduler: () => (/* binding */ updateReminderScheduler)\n/* harmony export */ });\n/* harmony import */ var _deadline_reminder_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deadline-reminder-service */ \"(ssr)/./lib/deadline-reminder-service.ts\");\n/* harmony import */ var _enhanced_reminder_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enhanced-reminder-service */ \"(ssr)/./lib/enhanced-reminder-service.ts\");\n/* __next_internal_client_entry_do_not_use__ startReminderScheduler,stopReminderScheduler,isReminderSchedulerRunning,manualReminderCheck,getReminderCheckInterval,setReminderCheckInterval,initializeReminderScheduler,saveReminderCheckInterval,getSavedReminderCheckInterval,updateReminderScheduler,getReminderSchedulerStats auto */ \n\n// Global enhanced scheduler\nlet scheduler = {\n    queueProcessorId: null,\n    cleanupId: null,\n    overdueCheckId: null,\n    syncId: null,\n    isRunning: false,\n    queueInterval: 60 * 1000,\n    cleanupInterval: 60 * 60 * 1000,\n    overdueInterval: 30 * 60 * 1000,\n    syncInterval: 6 * 60 * 60 * 1000\n};\n// Enhanced function to start reminder scheduler\nfunction startReminderScheduler(intervalMinutes = 1) {\n    // Stop previous scheduler if running\n    stopReminderScheduler();\n    console.log('Starting enhanced reminder scheduler...');\n    // Start queue processor (every minute)\n    scheduler.queueProcessorId = setInterval(async ()=>{\n        try {\n            await (0,_enhanced_reminder_service__WEBPACK_IMPORTED_MODULE_1__.processReminderQueue)();\n        } catch (error) {\n            console.error('Error processing reminder queue:', error);\n        }\n    }, scheduler.queueInterval);\n    // Start cleanup process (every hour)\n    scheduler.cleanupId = setInterval(async ()=>{\n        try {\n            (0,_enhanced_reminder_service__WEBPACK_IMPORTED_MODULE_1__.cleanupOldData)();\n        } catch (error) {\n            console.error('Error cleaning up old data:', error);\n        }\n    }, scheduler.cleanupInterval);\n    // Start overdue checker (every 30 minutes)\n    scheduler.overdueCheckId = setInterval(async ()=>{\n        try {\n            await checkOverdueItems();\n        } catch (error) {\n            console.error('Error checking overdue items:', error);\n        }\n    }, scheduler.overdueInterval);\n    // Start reminder sync (every 6 hours)\n    scheduler.syncId = setInterval(async ()=>{\n        try {\n            await syncAllReminders();\n        } catch (error) {\n            console.error('Error syncing reminders:', error);\n        }\n    }, scheduler.syncInterval);\n    // Run initial checks\n    (0,_enhanced_reminder_service__WEBPACK_IMPORTED_MODULE_1__.processReminderQueue)();\n    (0,_deadline_reminder_service__WEBPACK_IMPORTED_MODULE_0__.checkAndSendAllReminders)();\n    scheduler.isRunning = true;\n    console.log('Enhanced reminder scheduler started successfully');\n}\n// Enhanced function to stop reminder scheduler\nfunction stopReminderScheduler() {\n    console.log('Stopping enhanced reminder scheduler...');\n    if (scheduler.queueProcessorId) {\n        clearInterval(scheduler.queueProcessorId);\n        scheduler.queueProcessorId = null;\n    }\n    if (scheduler.cleanupId) {\n        clearInterval(scheduler.cleanupId);\n        scheduler.cleanupId = null;\n    }\n    if (scheduler.overdueCheckId) {\n        clearInterval(scheduler.overdueCheckId);\n        scheduler.overdueCheckId = null;\n    }\n    if (scheduler.syncId) {\n        clearInterval(scheduler.syncId);\n        scheduler.syncId = null;\n    }\n    scheduler.isRunning = false;\n    console.log('Enhanced reminder scheduler stopped');\n}\n// Check for overdue items and send notifications\nasync function checkOverdueItems() {\n    console.log('Checking for overdue items...');\n    try {\n        const items = await getAllItemsWithDueDates();\n        const now = new Date();\n        for (const item of items){\n            const dueDate = new Date(item.dueDate);\n            if (dueDate < now && !item.completed) {\n                const userId = item.userId || item.assignee?.id;\n                if (userId) {\n                    const preferences = (0,_enhanced_reminder_service__WEBPACK_IMPORTED_MODULE_1__.getUserReminderPreferences)(userId);\n                    if (preferences.overdueReminders) {\n                        await (0,_enhanced_reminder_service__WEBPACK_IMPORTED_MODULE_1__.createRemindersForItem)(userId, item.type, item.id, now, 'high');\n                    }\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Error checking overdue items:', error);\n    }\n}\n// Sync reminders for all items\nasync function syncAllReminders() {\n    console.log('Syncing reminders for all items...');\n    try {\n        const items = await getAllItemsWithDueDates();\n        for (const item of items){\n            if (!item.completed && item.dueDate) {\n                const userId = item.userId || item.assignee?.id;\n                if (userId) {\n                    await (0,_enhanced_reminder_service__WEBPACK_IMPORTED_MODULE_1__.createRemindersForItem)(userId, item.type, item.id, new Date(item.dueDate), item.priority || 'medium');\n                }\n            }\n        }\n        console.log(`Synced reminders for ${items.length} items`);\n    } catch (error) {\n        console.error('Error syncing reminders:', error);\n    }\n}\n// Get all items with due dates from storage\nasync function getAllItemsWithDueDates() {\n    const items = [];\n    if (false) {}\n    return items;\n}\n// Function to check scheduler status\nfunction isReminderSchedulerRunning() {\n    return scheduler.isRunning;\n}\n// Manual reminder check function\nasync function manualReminderCheck() {\n    console.log('Running manual reminder check...');\n    try {\n        await (0,_enhanced_reminder_service__WEBPACK_IMPORTED_MODULE_1__.processReminderQueue)();\n        await checkOverdueItems();\n        await (0,_deadline_reminder_service__WEBPACK_IMPORTED_MODULE_0__.checkAndSendAllReminders)();\n        console.log('Manual reminder check completed successfully');\n    } catch (error) {\n        console.error('Error in manual reminder check:', error);\n        throw error;\n    }\n}\n// Функция для получения интервала проверки\nfunction getReminderCheckInterval() {\n    return scheduler.checkInterval / (60 * 1000); // возвращаем в минутах\n}\n// Функция для изменения интервала проверки\nfunction setReminderCheckInterval(intervalMinutes) {\n    if (scheduler.isRunning) {\n        startReminderScheduler(intervalMinutes);\n    } else {\n        scheduler.checkInterval = intervalMinutes * 60 * 1000;\n    }\n}\n// Функция для инициализации планировщика при загрузке приложения\nfunction initializeReminderScheduler() {\n    if (true) {\n        return; // Не запускаем на сервере\n    }\n    // Проверяем, включены ли напоминания\n    const emailReminders = localStorage.getItem('emailReminders') === 'true';\n    const telegramReminders = localStorage.getItem('telegramReminders') === 'true';\n    if (emailReminders || telegramReminders) {\n        // Получаем интервал проверки из настроек (по умолчанию 5 минут)\n        const savedInterval = localStorage.getItem('reminderCheckInterval');\n        const intervalMinutes = savedInterval ? parseInt(savedInterval) : 5;\n        startReminderScheduler(intervalMinutes);\n        console.log('Reminder scheduler initialized and started');\n    } else {\n        console.log('Reminders are disabled, scheduler not started');\n    }\n}\n// Функция для сохранения интервала проверки в localStorage\nfunction saveReminderCheckInterval(intervalMinutes) {\n    if (false) {}\n}\n// Функция для получения сохраненного интервала проверки\nfunction getSavedReminderCheckInterval() {\n    if (true) {\n        return 5; // По умолчанию 5 минут\n    }\n    const saved = localStorage.getItem('reminderCheckInterval');\n    return saved ? parseInt(saved) : 5;\n}\n// Функция для обновления планировщика при изменении настроек\nfunction updateReminderScheduler() {\n    if (true) {\n        return;\n    }\n    const emailReminders = localStorage.getItem('emailReminders') === 'true';\n    const telegramReminders = localStorage.getItem('telegramReminders') === 'true';\n    if (emailReminders || telegramReminders) {\n        if (!scheduler.isRunning) {\n            const intervalMinutes = getSavedReminderCheckInterval();\n            startReminderScheduler(intervalMinutes);\n        }\n    } else {\n        if (scheduler.isRunning) {\n            stopReminderScheduler();\n        }\n    }\n}\n// Функция для получения статистики планировщика\nfunction getReminderSchedulerStats() {\n    const stats = {\n        isRunning: scheduler.isRunning,\n        intervalMinutes: scheduler.checkInterval / (60 * 1000)\n    };\n    // Если планировщик запущен, вычисляем время до следующей проверки\n    // Это приблизительная оценка, так как мы не отслеживаем точное время последней проверки\n    if (scheduler.isRunning) {\n        return {\n            ...stats,\n            nextCheckIn: Math.floor(scheduler.checkInterval / 1000)\n        };\n    }\n    return stats;\n}\n// Автоматическая инициализация при загрузке модуля (только в браузере)\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/reminder-scheduler.ts\n");

/***/ }),

/***/ "(ssr)/./lib/socket-context.tsx":
/*!********************************!*\
  !*** ./lib/socket-context.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* __next_internal_client_entry_do_not_use__ useSocket,SocketProvider auto */ \n\n\n// Определяем URL нашего сервера чата\n// Используем переменную окружения или значение по умолчанию\nconst SOCKET_URL = process.env.NEXT_PUBLIC_CHAT_SERVER_URL || 'http://localhost:3003';\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    socket: null,\n    isConnected: false\n});\n// Хук для удобного доступа к контексту\nconst useSocket = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n};\nconst SocketProvider = ({ children })=>{\n    console.log('--- SocketProvider component rendering ---'); // Add this log\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocketProvider.useEffect\": ()=>{\n            console.log('SocketProvider useEffect running. Creating socket instance...');\n            let socketInstance = null;\n            // Определяем обработчики вне try...catch, чтобы они были доступны в cleanup\n            const onConnect = {\n                \"SocketProvider.useEffect.onConnect\": ()=>{\n                    // Используем socketInstance из замыкания\n                    if (socketInstance) {\n                        console.log('Socket connected:', socketInstance.id);\n                        setIsConnected(true);\n                    }\n                }\n            }[\"SocketProvider.useEffect.onConnect\"];\n            const onDisconnect = {\n                \"SocketProvider.useEffect.onDisconnect\": (reason)=>{\n                    console.log('Socket disconnected:', reason);\n                    setIsConnected(false);\n                // Можно добавить логику обработки отключения, например, попытку переподключения\n                }\n            }[\"SocketProvider.useEffect.onDisconnect\"];\n            const onConnectError = {\n                \"SocketProvider.useEffect.onConnectError\": (error)=>{\n                    console.error('Socket connection error:', error);\n                    setIsConnected(false);\n                }\n            }[\"SocketProvider.useEffect.onConnectError\"];\n            try {\n                console.log('Attempting to create socket instance...');\n                // Создаем экземпляр сокета при монтировании компонента\n                // Опция `autoConnect: false` предотвращает автоматическое подключение\n                // Мы можем подключиться вручную позже, например, после аутентификации пользователя\n                socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(SOCKET_URL, {\n                    autoConnect: true,\n                    reconnectionAttempts: 5,\n                    transports: [\n                        'websocket'\n                    ]\n                });\n                setSocket(socketInstance);\n                console.log('Socket instance created:', socketInstance ? 'Yes' : 'No');\n                // Добавляем обработчики, если сокет успешно создан\n                socketInstance.on('connect', onConnect);\n                socketInstance.on('disconnect', onDisconnect);\n                socketInstance.on('connect_error', onConnectError);\n            } catch (error) {\n                console.error('Error creating socket instance:', error);\n                setIsConnected(false); // Убедимся, что статус disconnected\n            }\n            // Очистка при размонтировании компонента\n            return ({\n                \"SocketProvider.useEffect\": ()=>{\n                    if (socketInstance) {\n                        console.log('Disconnecting socket...');\n                        socketInstance.off('connect', onConnect);\n                        socketInstance.off('disconnect', onDisconnect);\n                        socketInstance.off('connect_error', onConnectError);\n                        socketInstance.disconnect();\n                        setSocket(null);\n                        setIsConnected(false);\n                    }\n                }\n            })[\"SocketProvider.useEffect\"];\n        }\n    }[\"SocketProvider.useEffect\"], []); // Пустой массив зависимостей гарантирует выполнение только один раз\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: {\n            socket,\n            isConnected\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\lib\\\\socket-context.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvc29ja2V0LWNvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXlGO0FBQzNDO0FBRTlDLHFDQUFxQztBQUNyQyw0REFBNEQ7QUFDNUQsTUFBTU0sYUFBYUMsUUFBUUMsR0FBRyxDQUFDQywyQkFBMkIsSUFBSTtBQU85RCxNQUFNQyw4QkFBZ0JULG9EQUFhQSxDQUFxQjtJQUN0RFUsUUFBUTtJQUNSQyxhQUFhO0FBQ2Y7QUFFQSx1Q0FBdUM7QUFDaEMsTUFBTUMsWUFBWTtJQUN2QixPQUFPWCxpREFBVUEsQ0FBQ1E7QUFDcEIsRUFBRTtBQU1LLE1BQU1JLGlCQUFnRCxDQUFDLEVBQUVDLFFBQVEsRUFBRTtJQUN4RUMsUUFBUUMsR0FBRyxDQUFDLCtDQUErQyxlQUFlO0lBQzFFLE1BQU0sQ0FBQ04sUUFBUU8sVUFBVSxHQUFHZCwrQ0FBUUEsQ0FBZ0I7SUFDcEQsTUFBTSxDQUFDUSxhQUFhTyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFDO0lBRS9DRCxnREFBU0E7b0NBQUM7WUFDUmEsUUFBUUMsR0FBRyxDQUFDO1lBQ1osSUFBSUcsaUJBQWdDO1lBRXBDLDRFQUE0RTtZQUM1RSxNQUFNQztzREFBWTtvQkFDaEIseUNBQXlDO29CQUN6QyxJQUFJRCxnQkFBZ0I7d0JBQ2xCSixRQUFRQyxHQUFHLENBQUMscUJBQXFCRyxlQUFlRSxFQUFFO3dCQUNsREgsZUFBZTtvQkFDakI7Z0JBQ0Y7O1lBRUEsTUFBTUk7eURBQWUsQ0FBQ0M7b0JBQ3BCUixRQUFRQyxHQUFHLENBQUMsd0JBQXdCTztvQkFDcENMLGVBQWU7Z0JBQ2YsZ0ZBQWdGO2dCQUNsRjs7WUFFQSxNQUFNTTsyREFBaUIsQ0FBQ0M7b0JBQ3RCVixRQUFRVSxLQUFLLENBQUMsNEJBQTRCQTtvQkFDMUNQLGVBQWU7Z0JBQ2pCOztZQUVBLElBQUk7Z0JBQ0ZILFFBQVFDLEdBQUcsQ0FBQztnQkFDWix1REFBdUQ7Z0JBQ3ZELHNFQUFzRTtnQkFDdEUsbUZBQW1GO2dCQUNuRkcsaUJBQWlCZixvREFBRUEsQ0FBQ0MsWUFBWTtvQkFDOUJxQixhQUFhO29CQUNiQyxzQkFBc0I7b0JBQ3RCQyxZQUFZO3dCQUFDO3FCQUFZO2dCQUczQjtnQkFFQVgsVUFBVUU7Z0JBQ1ZKLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJHLGlCQUFpQixRQUFRO2dCQUVqRSxtREFBbUQ7Z0JBQ25EQSxlQUFlVSxFQUFFLENBQUMsV0FBV1Q7Z0JBQzdCRCxlQUFlVSxFQUFFLENBQUMsY0FBY1A7Z0JBQ2hDSCxlQUFlVSxFQUFFLENBQUMsaUJBQWlCTDtZQUVyQyxFQUFFLE9BQU9DLE9BQU87Z0JBQ2RWLFFBQVFVLEtBQUssQ0FBQyxtQ0FBbUNBO2dCQUNqRFAsZUFBZSxRQUFRLG9DQUFvQztZQUM3RDtZQUVBLHlDQUF5QztZQUN6Qzs0Q0FBTztvQkFDTCxJQUFJQyxnQkFBZ0I7d0JBQ2xCSixRQUFRQyxHQUFHLENBQUM7d0JBQ1pHLGVBQWVXLEdBQUcsQ0FBQyxXQUFXVjt3QkFDOUJELGVBQWVXLEdBQUcsQ0FBQyxjQUFjUjt3QkFDakNILGVBQWVXLEdBQUcsQ0FBQyxpQkFBaUJOO3dCQUNwQ0wsZUFBZVksVUFBVTt3QkFDekJkLFVBQVU7d0JBQ1ZDLGVBQWU7b0JBQ2pCO2dCQUNGOztRQUNGO21DQUFHLEVBQUUsR0FBRyxvRUFBb0U7SUFFNUUscUJBQ0UsOERBQUNULGNBQWN1QixRQUFRO1FBQUNDLE9BQU87WUFBRXZCO1lBQVFDO1FBQVk7a0JBQ2xERzs7Ozs7O0FBR1AsRUFBRSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFx0YXNrLWFpLW1hbmFnZXIzXFxsaWJcXHNvY2tldC1jb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjsgLy8g0JrQvtC90YLQtdC60YHRgiDQsdGD0LTQtdGCINC40YHQv9C+0LvRjNC30L7QstCw0YLRjNGB0Y8g0LIg0LrQu9C40LXQvdGC0YHQutC40YUg0LrQvtC80L/QvtC90LXQvdGC0LDRhVxyXG5cclxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgaW8sIFNvY2tldCB9IGZyb20gJ3NvY2tldC5pby1jbGllbnQnO1xyXG5cclxuLy8g0J7Qv9GA0LXQtNC10LvRj9C10LwgVVJMINC90LDRiNC10LPQviDRgdC10YDQstC10YDQsCDRh9Cw0YLQsFxyXG4vLyDQmNGB0L/QvtC70YzQt9GD0LXQvCDQv9C10YDQtdC80LXQvdC90YPRjiDQvtC60YDRg9C20LXQvdC40Y8g0LjQu9C4INC30L3QsNGH0LXQvdC40LUg0L/QviDRg9C80L7Qu9GH0LDQvdC40Y5cclxuY29uc3QgU09DS0VUX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NIQVRfU0VSVkVSX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAzJztcclxuXHJcbmludGVyZmFjZSBTb2NrZXRDb250ZXh0UHJvcHMge1xyXG4gIHNvY2tldDogU29ja2V0IHwgbnVsbDtcclxuICBpc0Nvbm5lY3RlZDogYm9vbGVhbjtcclxufVxyXG5cclxuY29uc3QgU29ja2V0Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8U29ja2V0Q29udGV4dFByb3BzPih7XHJcbiAgc29ja2V0OiBudWxsLFxyXG4gIGlzQ29ubmVjdGVkOiBmYWxzZSxcclxufSk7XHJcblxyXG4vLyDQpdGD0Log0LTQu9GPINGD0LTQvtCx0L3QvtCz0L4g0LTQvtGB0YLRg9C/0LAg0Log0LrQvtC90YLQtdC60YHRgtGDXHJcbmV4cG9ydCBjb25zdCB1c2VTb2NrZXQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIHVzZUNvbnRleHQoU29ja2V0Q29udGV4dCk7XHJcbn07XHJcblxyXG5pbnRlcmZhY2UgU29ja2V0UHJvdmlkZXJQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IFNvY2tldFByb3ZpZGVyOiBSZWFjdC5GQzxTb2NrZXRQcm92aWRlclByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcclxuICBjb25zb2xlLmxvZygnLS0tIFNvY2tldFByb3ZpZGVyIGNvbXBvbmVudCByZW5kZXJpbmcgLS0tJyk7IC8vIEFkZCB0aGlzIGxvZ1xyXG4gIGNvbnN0IFtzb2NrZXQsIHNldFNvY2tldF0gPSB1c2VTdGF0ZTxTb2NrZXQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbaXNDb25uZWN0ZWQsIHNldElzQ29ubmVjdGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCdTb2NrZXRQcm92aWRlciB1c2VFZmZlY3QgcnVubmluZy4gQ3JlYXRpbmcgc29ja2V0IGluc3RhbmNlLi4uJyk7XHJcbiAgICBsZXQgc29ja2V0SW5zdGFuY2U6IFNvY2tldCB8IG51bGwgPSBudWxsO1xyXG5cclxuICAgIC8vINCe0L/RgNC10LTQtdC70Y/QtdC8INC+0LHRgNCw0LHQvtGC0YfQuNC60Lgg0LLQvdC1IHRyeS4uLmNhdGNoLCDRh9GC0L7QsdGLINC+0L3QuCDQsdGL0LvQuCDQtNC+0YHRgtGD0L/QvdGLINCyIGNsZWFudXBcclxuICAgIGNvbnN0IG9uQ29ubmVjdCA9ICgpID0+IHtcclxuICAgICAgLy8g0JjRgdC/0L7Qu9GM0LfRg9C10Lwgc29ja2V0SW5zdGFuY2Ug0LjQtyDQt9Cw0LzRi9C60LDQvdC40Y9cclxuICAgICAgaWYgKHNvY2tldEluc3RhbmNlKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1NvY2tldCBjb25uZWN0ZWQ6Jywgc29ja2V0SW5zdGFuY2UuaWQpO1xyXG4gICAgICAgIHNldElzQ29ubmVjdGVkKHRydWUpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IG9uRGlzY29ubmVjdCA9IChyZWFzb246IFNvY2tldC5EaXNjb25uZWN0UmVhc29uKSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdTb2NrZXQgZGlzY29ubmVjdGVkOicsIHJlYXNvbik7XHJcbiAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKTtcclxuICAgICAgLy8g0JzQvtC20L3QviDQtNC+0LHQsNCy0LjRgtGMINC70L7Qs9C40LrRgyDQvtCx0YDQsNCx0L7RgtC60Lgg0L7RgtC60LvRjtGH0LXQvdC40Y8sINC90LDQv9GA0LjQvNC10YAsINC/0L7Qv9GL0YLQutGDINC/0LXRgNC10L/QvtC00LrQu9GO0YfQtdC90LjRj1xyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBvbkNvbm5lY3RFcnJvciA9IChlcnJvcjogRXJyb3IpID0+IHtcclxuICAgICAgY29uc29sZS5lcnJvcignU29ja2V0IGNvbm5lY3Rpb24gZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICBzZXRJc0Nvbm5lY3RlZChmYWxzZSk7XHJcbiAgICB9O1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdBdHRlbXB0aW5nIHRvIGNyZWF0ZSBzb2NrZXQgaW5zdGFuY2UuLi4nKTtcclxuICAgICAgLy8g0KHQvtC30LTQsNC10Lwg0Y3QutC30LXQvNC/0LvRj9GAINGB0L7QutC10YLQsCDQv9GA0Lgg0LzQvtC90YLQuNGA0L7QstCw0L3QuNC4INC60L7QvNC/0L7QvdC10L3RgtCwXHJcbiAgICAgIC8vINCe0L/RhtC40Y8gYGF1dG9Db25uZWN0OiBmYWxzZWAg0L/RgNC10LTQvtGC0LLRgNCw0YnQsNC10YIg0LDQstGC0L7QvNCw0YLQuNGH0LXRgdC60L7QtSDQv9C+0LTQutC70Y7Rh9C10L3QuNC1XHJcbiAgICAgIC8vINCc0Ysg0LzQvtC20LXQvCDQv9C+0LTQutC70Y7Rh9C40YLRjNGB0Y8g0LLRgNGD0YfQvdGD0Y4g0L/QvtC30LbQtSwg0L3QsNC/0YDQuNC80LXRgCwg0L/QvtGB0LvQtSDQsNGD0YLQtdC90YLQuNGE0LjQutCw0YbQuNC4INC/0L7Qu9GM0LfQvtCy0LDRgtC10LvRj1xyXG4gICAgICBzb2NrZXRJbnN0YW5jZSA9IGlvKFNPQ0tFVF9VUkwsIHtcclxuICAgICAgICBhdXRvQ29ubmVjdDogdHJ1ZSwgLy8g0J/QvtC60LAg0YPRgdGC0LDQvdC+0LLQuNC8IHRydWUg0LTQu9GPINC/0YDQvtGB0YLQvtGC0YtcclxuICAgICAgICByZWNvbm5lY3Rpb25BdHRlbXB0czogNSwgLy8g0J/QvtC/0YvRgtC60Lgg0L/QtdGA0LXQv9C+0LTQutC70Y7Rh9C10L3QuNGPXHJcbiAgICAgICAgdHJhbnNwb3J0czogWyd3ZWJzb2NrZXQnXSwgLy8g0J/RgNC40L3Rg9C00LjRgtC10LvRjNC90L4g0LjRgdC/0L7Qu9GM0LfQvtCy0LDRgtGMINGC0L7Qu9GM0LrQviBXZWJTb2NrZXRcclxuICAgICAgICAvLyDQnNC+0LbQvdC+INC00L7QsdCw0LLQuNGC0Ywg0L/QsNGA0LDQvNC10YLRgNGLINCw0YPRgtC10L3RgtC40YTQuNC60LDRhtC40LgsINC10YHQu9C4INC90YPQttC90L5cclxuICAgICAgICAvLyBxdWVyeTogeyB0b2tlbjogJ3VzZXItYXV0aC10b2tlbicgfVxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHNldFNvY2tldChzb2NrZXRJbnN0YW5jZSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdTb2NrZXQgaW5zdGFuY2UgY3JlYXRlZDonLCBzb2NrZXRJbnN0YW5jZSA/ICdZZXMnIDogJ05vJyk7XHJcblxyXG4gICAgICAvLyDQlNC+0LHQsNCy0LvRj9C10Lwg0L7QsdGA0LDQsdC+0YLRh9C40LrQuCwg0LXRgdC70Lgg0YHQvtC60LXRgiDRg9GB0L/QtdGI0L3QviDRgdC+0LfQtNCw0L1cclxuICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ2Nvbm5lY3QnLCBvbkNvbm5lY3QpO1xyXG4gICAgICBzb2NrZXRJbnN0YW5jZS5vbignZGlzY29ubmVjdCcsIG9uRGlzY29ubmVjdCk7XHJcbiAgICAgIHNvY2tldEluc3RhbmNlLm9uKCdjb25uZWN0X2Vycm9yJywgb25Db25uZWN0RXJyb3IpO1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHNvY2tldCBpbnN0YW5jZTonLCBlcnJvcik7XHJcbiAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKTsgLy8g0KPQsdC10LTQuNC80YHRjywg0YfRgtC+INGB0YLQsNGC0YPRgSBkaXNjb25uZWN0ZWRcclxuICAgIH1cclxuXHJcbiAgICAvLyDQntGH0LjRgdGC0LrQsCDQv9GA0Lgg0YDQsNC30LzQvtC90YLQuNGA0L7QstCw0L3QuNC4INC60L7QvNC/0L7QvdC10L3RgtCwXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpZiAoc29ja2V0SW5zdGFuY2UpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnRGlzY29ubmVjdGluZyBzb2NrZXQuLi4nKTtcclxuICAgICAgICBzb2NrZXRJbnN0YW5jZS5vZmYoJ2Nvbm5lY3QnLCBvbkNvbm5lY3QpO1xyXG4gICAgICAgIHNvY2tldEluc3RhbmNlLm9mZignZGlzY29ubmVjdCcsIG9uRGlzY29ubmVjdCk7XHJcbiAgICAgICAgc29ja2V0SW5zdGFuY2Uub2ZmKCdjb25uZWN0X2Vycm9yJywgb25Db25uZWN0RXJyb3IpO1xyXG4gICAgICAgIHNvY2tldEluc3RhbmNlLmRpc2Nvbm5lY3QoKTtcclxuICAgICAgICBzZXRTb2NrZXQobnVsbCk7XHJcbiAgICAgICAgc2V0SXNDb25uZWN0ZWQoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0sIFtdKTsgLy8g0J/Rg9GB0YLQvtC5INC80LDRgdGB0LjQsiDQt9Cw0LLQuNGB0LjQvNC+0YHRgtC10Lkg0LPQsNGA0LDQvdGC0LjRgNGD0LXRgiDQstGL0L/QvtC70L3QtdC90LjQtSDRgtC+0LvRjNC60L4g0L7QtNC40L0g0YDQsNC3XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8U29ja2V0Q29udGV4dC5Qcm92aWRlciB2YWx1ZT17eyBzb2NrZXQsIGlzQ29ubmVjdGVkIH19PlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L1NvY2tldENvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsImlvIiwiU09DS0VUX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19DSEFUX1NFUlZFUl9VUkwiLCJTb2NrZXRDb250ZXh0Iiwic29ja2V0IiwiaXNDb25uZWN0ZWQiLCJ1c2VTb2NrZXQiLCJTb2NrZXRQcm92aWRlciIsImNoaWxkcmVuIiwiY29uc29sZSIsImxvZyIsInNldFNvY2tldCIsInNldElzQ29ubmVjdGVkIiwic29ja2V0SW5zdGFuY2UiLCJvbkNvbm5lY3QiLCJpZCIsIm9uRGlzY29ubmVjdCIsInJlYXNvbiIsIm9uQ29ubmVjdEVycm9yIiwiZXJyb3IiLCJhdXRvQ29ubmVjdCIsInJlY29ubmVjdGlvbkF0dGVtcHRzIiwidHJhbnNwb3J0cyIsIm9uIiwib2ZmIiwiZGlzY29ubmVjdCIsIlByb3ZpZGVyIiwidmFsdWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/socket-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/telegram-service.ts":
/*!*********************************!*\
  !*** ./lib/telegram-service.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTelegramBotInfo: () => (/* binding */ getTelegramBotInfo),\n/* harmony export */   sendTelegramMessage: () => (/* binding */ sendTelegramMessage),\n/* harmony export */   sendTelegramReminder: () => (/* binding */ sendTelegramReminder),\n/* harmony export */   validateTelegramChatId: () => (/* binding */ validateTelegramChatId)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"0004d75c0cbd3b51b50db872a1bb33175142ec7a81\":\"getTelegramBotInfo\",\"4036d186bba33a6111e3a1a311e5529f6815449ecf\":\"validateTelegramChatId\",\"40d1ad41895a6b2d78824c2a72a188ce00547f82db\":\"sendTelegramReminder\",\"40ddd58023aecf11b09e60f5964dbdfe2e36fe9c17\":\"sendTelegramMessage\"} */ \nvar sendTelegramMessage = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40ddd58023aecf11b09e60f5964dbdfe2e36fe9c17\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"sendTelegramMessage\");\nvar sendTelegramReminder = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"40d1ad41895a6b2d78824c2a72a188ce00547f82db\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"sendTelegramReminder\");\nvar getTelegramBotInfo = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"0004d75c0cbd3b51b50db872a1bb33175142ec7a81\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getTelegramBotInfo\");\nvar validateTelegramChatId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4036d186bba33a6111e3a1a311e5529f6815449ecf\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"validateTelegramChatId\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdGVsZWdyYW0tc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0lBeUNzQkEsb0NBQUFBLDZGQUFBQSwrQ0FBQUEsOEVBQUFBLFVBQUFBLG9GQUFBQTtJQWdEQUMscUNBQUFBLDZGQUFBQSwrQ0FBQUEsOEVBQUFBLFVBQUFBLG9GQUFBQTtJQXlGQUMsbUNBQUFBLDZGQUFBQSwrQ0FBQUEsOEVBQUFBLFVBQUFBLG9GQUFBQTtJQW1CQUMsdUNBQUFBLDZGQUFBQSwrQ0FBQUEsOEVBQUFBLFVBQUFBLG9GQUFBQSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFx0YXNrLWFpLW1hbmFnZXIzXFxsaWJcXHRlbGVncmFtLXNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc2VydmVyXCI7XG5cbi8vINCY0L3RgtC10YDRhNC10LnRgSDQtNC70Y8g0LTQsNC90L3Ri9GFIFRlbGVncmFtLdGD0LLQtdC00L7QvNC70LXQvdC40Y9cbmV4cG9ydCBpbnRlcmZhY2UgVGVsZWdyYW1Ob3RpZmljYXRpb25EYXRhIHtcbiAgY2hhdElkOiBzdHJpbmc7IC8vIFRlbGVncmFtIGNoYXQgSUQg0LjQu9C4IHVzZXJuYW1lXG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgc3ViamVjdD86IHN0cmluZztcbiAgcGFyc2VNb2RlPzogJ01hcmtkb3duJyB8ICdIVE1MJztcbiAgZGlzYWJsZVdlYlBhZ2VQcmV2aWV3PzogYm9vbGVhbjtcbiAgZGlzYWJsZU5vdGlmaWNhdGlvbj86IGJvb2xlYW47XG59XG5cbi8vINCY0L3RgtC10YDRhNC10LnRgSDQtNC70Y8g0LTQsNC90L3Ri9GFINC90LDQv9C+0LzQuNC90LDQvdC40Y8g0LIgVGVsZWdyYW1cbmV4cG9ydCBpbnRlcmZhY2UgVGVsZWdyYW1SZW1pbmRlckRhdGEge1xuICB1c2VySWQ6IHN0cmluZztcbiAgdGVsZWdyYW1DaGF0SWQ6IHN0cmluZztcbiAgdXNlck5hbWU6IHN0cmluZztcbiAgc3ViamVjdDogc3RyaW5nO1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIGl0ZW1UeXBlOiAnZXZlbnQnIHwgJ3Rhc2snIHwgJ3N1YnRhc2snIHwgJ3Byb2plY3QnO1xuICBpdGVtSWQ6IHN0cmluZztcbiAgaXRlbVRpdGxlOiBzdHJpbmc7XG4gIGR1ZURhdGU/OiBEYXRlO1xuICBwcm9qZWN0TmFtZT86IHN0cmluZztcbn1cblxuLy8g0J/QvtC70YPRh9C10L3QuNC1INC90LDRgdGC0YDQvtC10LogVGVsZWdyYW0gQm90INC40Lcg0L/QtdGA0LXQvNC10L3QvdGL0YUg0L7QutGA0YPQttC10L3QuNGPXG5mdW5jdGlvbiBnZXRUZWxlZ3JhbVNldHRpbmdzKCkge1xuICBjb25zdCBib3RUb2tlbiA9IHByb2Nlc3MuZW52LlRFTEVHUkFNX0JPVF9UT0tFTjtcbiAgXG4gIGlmICghYm90VG9rZW4pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1RFTEVHUkFNX0JPVF9UT0tFTiBpcyBub3QgY29uZmlndXJlZCcpO1xuICB9XG4gIFxuICByZXR1cm4ge1xuICAgIGJvdFRva2VuLFxuICAgIGFwaVVybDogYGh0dHBzOi8vYXBpLnRlbGVncmFtLm9yZy9ib3Qke2JvdFRva2VufWAsXG4gIH07XG59XG5cbi8vINCk0YPQvdC60YbQuNGPINC00LvRjyDQvtGC0L/RgNCw0LLQutC4INGB0L7QvtCx0YnQtdC90LjRjyDRh9C10YDQtdC3IFRlbGVncmFtIEJvdCBBUElcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZW5kVGVsZWdyYW1NZXNzYWdlKGRhdGE6IFRlbGVncmFtTm90aWZpY2F0aW9uRGF0YSk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHNldHRpbmdzID0gZ2V0VGVsZWdyYW1TZXR0aW5ncygpO1xuICAgIFxuICAgIC8vINCf0YDQvtCy0LXRgNGP0LXQvCwg0YfRgtC+INCy0YHQtSDQvdC10L7QsdGF0L7QtNC40LzRi9C1INC00LDQvdC90YvQtSDRg9C60LDQt9Cw0L3Ri1xuICAgIGlmICghZGF0YS5jaGF0SWQgfHwgIWRhdGEubWVzc2FnZSkge1xuICAgICAgY29uc29sZS5lcnJvcignVGVsZWdyYW0gY2hhdCBJRCBhbmQgbWVzc2FnZSBhcmUgcmVxdWlyZWQnKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgXG4gICAgLy8g0KTQvtGA0LzQuNGA0YPQtdC8INGC0LXQu9C+INC30LDQv9GA0L7RgdCwXG4gICAgY29uc3QgcmVxdWVzdEJvZHkgPSB7XG4gICAgICBjaGF0X2lkOiBkYXRhLmNoYXRJZCxcbiAgICAgIHRleHQ6IGRhdGEubWVzc2FnZSxcbiAgICAgIHBhcnNlX21vZGU6IGRhdGEucGFyc2VNb2RlIHx8ICdNYXJrZG93bicsXG4gICAgICBkaXNhYmxlX3dlYl9wYWdlX3ByZXZpZXc6IGRhdGEuZGlzYWJsZVdlYlBhZ2VQcmV2aWV3IHx8IGZhbHNlLFxuICAgICAgZGlzYWJsZV9ub3RpZmljYXRpb246IGRhdGEuZGlzYWJsZU5vdGlmaWNhdGlvbiB8fCBmYWxzZSxcbiAgICB9O1xuICAgIFxuICAgIC8vINCe0YLQv9GA0LDQstC70Y/QtdC8INC30LDQv9GA0L7RgSDQuiBUZWxlZ3JhbSBCb3QgQVBJXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtzZXR0aW5ncy5hcGlVcmx9L3NlbmRNZXNzYWdlYCwge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICB9LFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocmVxdWVzdEJvZHkpLFxuICAgIH0pO1xuICAgIFxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1RlbGVncmFtIEFQSSBlcnJvcjonLCBlcnJvckRhdGEpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCByZXNwb25zZURhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgY29uc29sZS5sb2coYFRlbGVncmFtIG1lc3NhZ2Ugc2VudCB0byAke2RhdGEuY2hhdElkfTogJHtyZXNwb25zZURhdGEucmVzdWx0Lm1lc3NhZ2VfaWR9YCk7XG4gICAgXG4gICAgLy8g0JfQsNC/0LjRgdGL0LLQsNC10Lwg0LjQvdGE0L7RgNC80LDRhtC40Y4g0L7QsSDQvtGC0L/RgNCw0LLQu9C10L3QvdC+0Lwg0YHQvtC+0LHRidC10L3QuNC4INCyINC70L7Qs1xuICAgIGF3YWl0IGxvZ1RlbGVncmFtTWVzc2FnZVNlbnQoZGF0YSk7XG4gICAgXG4gICAgcmV0dXJuIHRydWU7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VuZGluZyBUZWxlZ3JhbSBtZXNzYWdlOicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn1cblxuLy8g0KTRg9C90LrRhtC40Y8g0LTQu9GPINC+0YLQv9GA0LDQstC60Lgg0L3QsNC/0L7QvNC40L3QsNC90LjRjyDQsiBUZWxlZ3JhbVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNlbmRUZWxlZ3JhbVJlbWluZGVyKHJlbWluZGVyRGF0YTogVGVsZWdyYW1SZW1pbmRlckRhdGEpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgdHJ5IHtcbiAgICAvLyDQn9GA0L7QstC10YDRj9C10LwsINGH0YLQviDRgyDQv9C+0LvRjNC30L7QstCw0YLQtdC70Y8g0LXRgdGC0YwgVGVsZWdyYW0gY2hhdCBJRFxuICAgIGlmICghcmVtaW5kZXJEYXRhLnRlbGVncmFtQ2hhdElkKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBVc2VyICR7cmVtaW5kZXJEYXRhLnVzZXJJZH0gZG9lcyBub3QgaGF2ZSBhIFRlbGVncmFtIGNoYXQgSURgKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgXG4gICAgLy8g0KTQvtGA0LzQuNGA0YPQtdC8INGB0L7QvtCx0YnQtdC90LjQtSDQtNC70Y8gVGVsZWdyYW1cbiAgICBjb25zdCBtZXNzYWdlVGV4dCA9IGZvcm1hdFRlbGVncmFtUmVtaW5kZXJNZXNzYWdlKHJlbWluZGVyRGF0YSk7XG4gICAgXG4gICAgLy8g0J7RgtC/0YDQsNCy0LvRj9C10Lwg0YHQvtC+0LHRidC10L3QuNC1XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2VuZFRlbGVncmFtTWVzc2FnZSh7XG4gICAgICBjaGF0SWQ6IHJlbWluZGVyRGF0YS50ZWxlZ3JhbUNoYXRJZCxcbiAgICAgIG1lc3NhZ2U6IG1lc3NhZ2VUZXh0LFxuICAgICAgcGFyc2VNb2RlOiAnTWFya2Rvd24nLFxuICAgIH0pO1xuICAgIFxuICAgIHJldHVybiByZXN1bHQ7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VuZGluZyBUZWxlZ3JhbSByZW1pbmRlcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59XG5cbi8vINCk0YPQvdC60YbQuNGPINC00LvRjyDRhNC+0YDQvNCw0YLQuNGA0L7QstCw0L3QuNGPINGB0L7QvtCx0YnQtdC90LjRjyDQvdCw0L/QvtC80LjQvdCw0L3QuNGPINC00LvRjyBUZWxlZ3JhbVxuZnVuY3Rpb24gZm9ybWF0VGVsZWdyYW1SZW1pbmRlck1lc3NhZ2UocmVtaW5kZXJEYXRhOiBUZWxlZ3JhbVJlbWluZGVyRGF0YSk6IHN0cmluZyB7XG4gIGNvbnN0IHsgdXNlck5hbWUsIHN1YmplY3QsIG1lc3NhZ2UsIGl0ZW1UaXRsZSwgaXRlbVR5cGUsIGR1ZURhdGUsIHByb2plY3ROYW1lIH0gPSByZW1pbmRlckRhdGE7XG4gIFxuICBsZXQgbWVzc2FnZVRleHQgPSBg8J+UlCAqJHtzdWJqZWN0fSpcXG5cXG5gO1xuICBtZXNzYWdlVGV4dCArPSBg0J/RgNC40LLQtdGCLCAke3VzZXJOYW1lfSFcXG5cXG5gO1xuICBtZXNzYWdlVGV4dCArPSBgJHttZXNzYWdlfVxcblxcbmA7XG4gIFxuICAvLyDQlNC+0LHQsNCy0LvRj9C10Lwg0LjQvdGE0L7RgNC80LDRhtC40Y4g0L4g0Y3Qu9C10LzQtdC90YLQtVxuICBjb25zdCB0eXBlRW1vamkgPSBnZXRUeXBlRW1vamkoaXRlbVR5cGUpO1xuICBtZXNzYWdlVGV4dCArPSBgJHt0eXBlRW1vaml9ICoke2l0ZW1UaXRsZX0qXFxuYDtcbiAgXG4gIGlmIChwcm9qZWN0TmFtZSkge1xuICAgIG1lc3NhZ2VUZXh0ICs9IGDwn5OBINCf0YDQvtC10LrRgjogJHtwcm9qZWN0TmFtZX1cXG5gO1xuICB9XG4gIFxuICBpZiAoZHVlRGF0ZSkge1xuICAgIG1lc3NhZ2VUZXh0ICs9IGDwn5OFINCh0YDQvtC6OiAke2R1ZURhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdydS1SVScpfVxcbmA7XG4gIH1cbiAgXG4gIG1lc3NhZ2VUZXh0ICs9IGBcXG5fQUkgVGFzayBUcmFja2VyX2A7XG4gIFxuICByZXR1cm4gbWVzc2FnZVRleHQ7XG59XG5cbi8vINCk0YPQvdC60YbQuNGPINC00LvRjyDQv9C+0LvRg9GH0LXQvdC40Y8g0Y3QvNC+0LTQt9C4INC/0L4g0YLQuNC/0YMg0Y3Qu9C10LzQtdC90YLQsFxuZnVuY3Rpb24gZ2V0VHlwZUVtb2ppKGl0ZW1UeXBlOiBzdHJpbmcpOiBzdHJpbmcge1xuICBzd2l0Y2ggKGl0ZW1UeXBlKSB7XG4gICAgY2FzZSAndGFzayc6XG4gICAgICByZXR1cm4gJ+KchSc7XG4gICAgY2FzZSAnc3VidGFzayc6XG4gICAgICByZXR1cm4gJ/Cfk50nO1xuICAgIGNhc2UgJ3Byb2plY3QnOlxuICAgICAgcmV0dXJuICfwn5OBJztcbiAgICBjYXNlICdldmVudCc6XG4gICAgICByZXR1cm4gJ/Cfk4UnO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gJ/Cfk4wnO1xuICB9XG59XG5cbi8vINCk0YPQvdC60YbQuNGPINC00LvRjyDQt9Cw0L/QuNGB0Lgg0LjQvdGE0L7RgNC80LDRhtC40Lgg0L7QsSDQvtGC0L/RgNCw0LLQu9C10L3QvdC+0Lwg0YHQvtC+0LHRidC10L3QuNC4INCyINC70L7Qs1xuYXN5bmMgZnVuY3Rpb24gbG9nVGVsZWdyYW1NZXNzYWdlU2VudChkYXRhOiBUZWxlZ3JhbU5vdGlmaWNhdGlvbkRhdGEpOiBQcm9taXNlPHZvaWQ+IHtcbiAgdHJ5IHtcbiAgICAvLyDQkiDRgNC10LDQu9GM0L3QvtC8INC/0YDQuNC70L7QttC10L3QuNC4INC30LTQtdGB0Ywg0LHRg9C00LXRgiDQt9Cw0L/QuNGB0Ywg0LIg0LHQsNC30YMg0LTQsNC90L3Ri9GFXG4gICAgY29uc29sZS5sb2coYFRlbGVncmFtIG1lc3NhZ2Ugc2VudCB0byAke2RhdGEuY2hhdElkfTogJHtkYXRhLm1lc3NhZ2Uuc3Vic3RyaW5nKDAsIDUwKX0uLi5gKTtcbiAgICBcbiAgICAvLyDQn9GA0LjQvNC10YAg0LfQsNC/0LjRgdC4INCyINCx0LDQt9GDINC00LDQvdC90YvRhSAo0LfQsNC60L7QvNC80LXQvdGC0LjRgNC+0LLQsNC90L4sINGC0LDQuiDQutCw0Log0LzQvtC00LXQu9GMINC90LUg0YHRg9GJ0LXRgdGC0LLRg9C10YIpXG4gICAgLypcbiAgICBhd2FpdCBwcmlzbWEudGVsZWdyYW1Mb2cuY3JlYXRlKHtcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgY2hhdElkOiBkYXRhLmNoYXRJZCxcbiAgICAgICAgbWVzc2FnZTogZGF0YS5tZXNzYWdlLFxuICAgICAgICBzZW50QXQ6IG5ldyBEYXRlKCksXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICB9LFxuICAgIH0pO1xuICAgICovXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9nZ2luZyBUZWxlZ3JhbSBtZXNzYWdlOicsIGVycm9yKTtcbiAgfVxufVxuXG4vLyDQpNGD0L3QutGG0LjRjyDQtNC70Y8g0L/QvtC70YPRh9C10L3QuNGPINC40L3RhNC+0YDQvNCw0YbQuNC4INC+INCx0L7RgtC1XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VGVsZWdyYW1Cb3RJbmZvKCk6IFByb21pc2U8YW55PiB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2V0dGluZ3MgPSBnZXRUZWxlZ3JhbVNldHRpbmdzKCk7XG4gICAgXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtzZXR0aW5ncy5hcGlVcmx9L2dldE1lYCk7XG4gICAgXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZ2V0IGJvdCBpbmZvOiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgfVxuICAgIFxuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgcmV0dXJuIGRhdGEucmVzdWx0O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgVGVsZWdyYW0gYm90IGluZm86JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbi8vINCk0YPQvdC60YbQuNGPINC00LvRjyDQv9GA0L7QstC10YDQutC4INCy0LDQu9C40LTQvdC+0YHRgtC4IFRlbGVncmFtIGNoYXQgSURcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB2YWxpZGF0ZVRlbGVncmFtQ2hhdElkKGNoYXRJZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2VuZFRlbGVncmFtTWVzc2FnZSh7XG4gICAgICBjaGF0SWQsXG4gICAgICBtZXNzYWdlOiAn8J+kliDQotC10YHRgiDRgdC+0LXQtNC40L3QtdC90LjRjyDRgSBBSSBUYXNrIFRyYWNrZXIg0YPRgdC/0LXRiNC10L0hJyxcbiAgICAgIGRpc2FibGVOb3RpZmljYXRpb246IHRydWUsXG4gICAgfSk7XG4gICAgXG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciB2YWxpZGF0aW5nIFRlbGVncmFtIGNoYXQgSUQ6JywgZXJyb3IpO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbInNlbmRUZWxlZ3JhbU1lc3NhZ2UiLCJzZW5kVGVsZWdyYW1SZW1pbmRlciIsImdldFRlbGVncmFtQm90SW5mbyIsInZhbGlkYXRlVGVsZWdyYW1DaGF0SWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/telegram-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/translations.tsx":
/*!******************************!*\
  !*** ./lib/translations.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TranslationProvider: () => (/* binding */ TranslationProvider),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ TranslationProvider,useTranslation auto */ \n\n// Define the available languages\nconst languages = {\n    en: {\n        // General\n        dashboard: \"Dashboard\",\n        projects: \"Projects\",\n        analytics: \"Analytics\",\n        settings: \"Settings\",\n        tasks: \"Tasks\",\n        documents: \"Documents\",\n        save: \"Save\",\n        cancel: \"Cancel\",\n        calendar: \"Calendar\",\n        // Tasks\n        toDo: \"To Do\",\n        inProgress: \"In Progress\",\n        done: \"Done\",\n        addTask: \"Add Task\",\n        deleteTask: \"Delete Task\",\n        editTask: \"Edit Task\",\n        taskTitle: \"Task Title\",\n        taskTitlePlaceholder: \"Enter task title\",\n        taskDescription: \"Task Description\",\n        taskDescriptionPlaceholder: \"Enter task description\",\n        taskPriority: \"Priority\",\n        high: \"High\",\n        medium: \"Medium\",\n        low: \"Low\",\n        dueDate: \"Due Date\",\n        selectPriority: \"Select priority\",\n        taskAge: \"Task Age\",\n        daysLeft: \" days left\",\n        daysOverdue: \" days overdue\",\n        taskUpdated: \"Task Updated\",\n        taskUpdatedSuccess: \"Task has been successfully updated\",\n        taskDeleted: \"Task Deleted\",\n        taskDeletedSuccess: \"Task has been successfully deleted\",\n        taskCreated: \"Task Created\",\n        taskCreatedSuccess: \"Task has been successfully created\",\n        deleteTaskConfirmation: \"Are you sure you want to delete task {title}?\",\n        delete: \"Delete\",\n        close: \"Close\",\n        editTaskDescription: \"Edit the task details\",\n        viewMode: \"View mode\",\n        kanban: \"Kanban\",\n        list: \"List\",\n        searchTasks: \"Search tasks\",\n        priority: \"Priority\",\n        allPriorities: \"All priorities\",\n        assignee: \"Assignee\",\n        allAssignees: \"All assignees\",\n        noTasksFound: \"No tasks found\",\n        add: \"Add\",\n        commaSeparatedTags: \"Comma separated tags\",\n        // Subtasks\n        manageSubtasks: \"Manage Subtasks\",\n        manageSubtasksDescription: \"Add or manage subtasks for this task\",\n        subtasks: \"Subtasks\",\n        newSubtask: \"New Subtask\",\n        newSubtaskPlaceholder: \"Enter subtask name\",\n        extractSubtasks: \"Extract as Subtasks\",\n        noSubtasksFound: \"No Subtasks Found\",\n        noSubtasksFoundDescription: \"Could not extract subtasks from AI response\",\n        subtasksAdded: \"Subtasks Added\",\n        subtasksAddedFromAI: \"Added {count} subtasks from AI suggestion\",\n        generateSubtasksWithAI: \"Generate Subtasks with AI\",\n        // AI Assistant\n        aiAssist: \"AI Assistant\",\n        aiAssistDescription: \"Get AI help with this task\",\n        aiPrompt: \"Ask AI\",\n        aiPromptPlaceholder: \"e.g. Help me break down this task\",\n        aiPromptExample1: \"Break down this task\",\n        aiPromptExample2: \"Suggest a priority\",\n        aiPromptExample3: \"Recommend next steps\",\n        aiResponse: \"AI Response\",\n        generateResponse: \"Generate\",\n        thinking: \"Thinking...\",\n        currentTask: \"Current Task\",\n        // Projects\n        projectProgress: \"Project Progress\",\n        createProject: \"Create Project\",\n        editProject: \"Edit Project\",\n        deleteProject: \"Delete Project\",\n        projectName: \"Project Name\",\n        projectDescription: \"Project Description\",\n        selectProject: \"Select Project\",\n        newProject: \"New Project\",\n        projectCreated: \"Project Created\",\n        projectCreatedDescription: \"Project has been created successfully\",\n        projectUpdated: \"Project Updated\",\n        projectUpdatedDescription: \"Project has been updated successfully\",\n        projectDeleted: \"Project Deleted\",\n        projectDeletedDescription: \"Project has been deleted successfully\",\n        deleteProjectConfirmation: \"Are you sure you want to delete project {name}?\",\n        projectCompletion: \"Project Completion\",\n        aiProjectAssistant: \"AI Project Assistant\",\n        askAI: \"Ask AI\",\n        applyAISuggestions: \"Apply AI Suggestions\",\n        aiSuggestionsApplied: \"AI Suggestions Applied\",\n        projectUpdatedWithAI: \"Project has been updated with AI suggestions\",\n        privateProject: \"Private Project\",\n        privateProjectDescription: \"Private projects are only visible to you and people you share them with\",\n        private: \"Private\",\n        shared: \"Shared\",\n        shareProject: \"Share Project\",\n        currentParticipants: \"Current Participants\",\n        addParticipants: \"Add Participants\",\n        searchTeamMembers: \"Search team members\",\n        noResultsFound: \"No results found\",\n        selectedParticipants: \"Selected Participants\",\n        share: \"Share\",\n        projectShared: \"Project Shared\",\n        projectSharedDescription: \"Project has been shared with {count} participants\",\n        participantRemoved: \"Participant Removed\",\n        participantRemovedDescription: \"Participant has been removed from the project\",\n        totalProjects: \"Total Projects\",\n        completedProjects: \"Completed Projects\",\n        inProgressProjects: \"In Progress\",\n        averageCompletion: \"Average Completion\",\n        noProjects: \"No Projects\",\n        createFirstProject: \"Create your first project to get started with task management\",\n        participants: \"Participants\",\n        noParticipants: \"No participants\",\n        actions: \"Actions\",\n        completion: \"Completion\",\n        createdAt: \"Created At\",\n        // Documents\n        projectDocuments: \"Project Documents\",\n        uploadDocument: \"Upload Document\",\n        addDocumentLink: \"Add Document Link\",\n        viewDocuments: \"View Documents\",\n        noDocuments: \"No documents for this project\",\n        documentName: \"Document Name\",\n        documentType: \"Document Type\",\n        documentURL: \"Document URL\",\n        addDocument: \"Add Document\",\n        addDocumentDescription: \"Add a document link to the current project\",\n        documentUploaded: \"Document Uploaded\",\n        documentUploadedSuccess: \"Document {name} has been uploaded successfully\",\n        documentAdded: \"Document Added\",\n        documentAddedSuccess: \"Document {name} has been added successfully\",\n        editDocument: \"Edit Document\",\n        deleteDocument: \"Delete Document\",\n        deleteDocumentConfirmation: \"Are you sure you want to delete document {name}?\",\n        documentDeleted: \"Document Deleted\",\n        documentDeletedSuccessfully: \"Document has been deleted successfully\",\n        documentUpdated: \"Document Updated\",\n        documentUpdatedSuccessfully: \"Document {name} has been updated successfully\",\n        folders: \"Folders\",\n        files: \"Files\",\n        newFolder: \"New Folder\",\n        uploadFiles: \"Upload Files\",\n        uploadFilesDescription: \"Upload files to the current project\",\n        dropFilesHere: \"Drop files here\",\n        or: \"or\",\n        browseFiles: \"Browse Files\",\n        selectedFiles: \"Selected Files\",\n        uploading: \"Uploading...\",\n        upload: \"Upload\",\n        uploadComplete: \"Upload Complete\",\n        filesUploadedSuccessfully: \"{count} files uploaded successfully\",\n        createdBy: \"Created by\",\n        lastModified: \"Last modified\",\n        size: \"Size\",\n        modified: \"Modified\",\n        folder: \"Folder\",\n        noFolder: \"No folder\",\n        tags: \"Tags\",\n        content: \"Content\",\n        aiDocumentAssistant: \"AI Document Assistant\",\n        aiDocumentPromptPlaceholder: \"e.g. Improve this document, Summarize, Suggest tags\",\n        aiDocumentPromptSuggestion1: \"Improve this document\",\n        aiDocumentPromptSuggestion2: \"Summarize content\",\n        aiDocumentPromptSuggestion3: \"Suggest tags\",\n        currentDocument: \"Current Document\",\n        documentUpdatedWithAI: \"Document has been updated with AI suggestions\",\n        documentAIUpdated: \"Document {name} has been updated with AI suggestions\",\n        openMenu: \"Open Menu\",\n        view: \"View\",\n        edit: \"Edit\",\n        download: \"Download\",\n        totalDocuments: \"Total Documents\",\n        totalSize: \"Total Size\",\n        latestUpdate: \"Latest Update\",\n        documentTypes: \"Document Types\",\n        groupByType: \"Group by Type\",\n        version: \"Version\",\n        status: \"Status\",\n        description: \"Description\",\n        // Admin\n        adminDashboard: \"Admin Dashboard\",\n        userManagement: \"User Management\",\n        blockUser: \"Block User\",\n        unblockUser: \"Unblock User\",\n        deleteUser: \"Delete User\",\n        confirmDelete: \"Are you sure you want to delete this user?\",\n        confirmBlock: \"Are you sure you want to block this user?\",\n        confirmUnblock: \"Are you sure you want to unblock this user?\",\n        username: \"Username\",\n        email: \"Email\",\n        active: \"Active\",\n        blocked: \"Blocked\",\n        // Settings\n        settingsSaved: \"Settings Saved\",\n        generalSettingsSaved: \"General settings have been saved successfully\",\n        aiSettingsSaved: \"AI settings have been saved successfully\",\n        emailSettingsSaved: \"Email settings have been saved successfully\",\n        aiModelApiKey: \"AI Model API Key\",\n        enterApiKeyPlaceholder: \"Enter your API key\",\n        // Admin Settings specific keys\n        generalSettings: \"General Settings\",\n        aiSettings: \"AI Settings\",\n        emailSettings: \"Email Settings\",\n        generalSettingsDescription: \"Configure general site settings\",\n        aiSettingsDescription: \"Configure AI assistant settings\",\n        emailSettingsDescription: \"Configure email notification settings\",\n        siteName: \"Site Name\",\n        siteDescription: \"Site Description\",\n        allowRegistration: \"Allow User Registration\",\n        requireEmailVerification: \"Require Email Verification\",\n        maxProjectsPerUser: \"Max Projects Per User\",\n        maxTasksPerProject: \"Max Tasks Per Project\",\n        saveSettings: \"Save Settings\",\n        enableAI: \"Enable AI Assistant\",\n        aiModel: \"AI Model\",\n        aiTemperature: \"AI Temperature\",\n        maxTokens: \"Max Tokens\",\n        customPrompt: \"Custom System Prompt\",\n        aiWarning: \"AI Usage Warning\",\n        aiWarningDescription: \"Inform users about potential AI inaccuracies or costs\",\n        enableEmailNotifications: \"Enable Email Notifications\",\n        smtpServer: \"SMTP Server\",\n        smtpPort: \"SMTP Port\",\n        smtpUsername: \"SMTP Username\",\n        smtpPassword: \"SMTP Password\",\n        fromEmail: \"From Email Address\",\n        systemSettings: \"System Settings\",\n        // Admin Dashboard specific keys\n        adminDashboardDescription: \"Overview of system statistics and management\",\n        totalUsers: \"Total Users\",\n        activeUsers: \"Active Users\",\n        totalTasks: \"Total Tasks\",\n        completedTasks: \"Completed Tasks\",\n        completionRate: \"Completion Rate\",\n        taskCompletionRate: \"Task Completion Rate\",\n        // Dashboard specific keys\n        logoutSuccessful: \"Logout Successful\",\n        youHaveBeenLoggedOut: \"You have been successfully logged out.\",\n        // Analytics specific keys\n        taskDistribution: \"Task Distribution\",\n        projectStatistics: \"Project Statistics\",\n        tasksByStatus: \"Tasks by Status\",\n        tasksByStatusDescription: \"Distribution of tasks across different statuses\",\n        taskCompletion: \"Task Completion Over Time\",\n        taskCompletionDescription: \"Trend of task completion\",\n        completed: \"Completed\",\n        projectsByStatus: \"Projects by Status\",\n        projectsByStatusDescription: \"Distribution of projects across different statuses\",\n        onHold: \"On Hold\",\n        monthlyProjects: \"Monthly Project Creation\",\n        monthlyProjectsDescription: \"Trend of new projects created each month\",\n        // User Analytics specific keys\n        userParticipation: \"User Participation\",\n        userActivity: \"User Activity\",\n        projectCollaboration: \"Project Collaboration\",\n        topUsersByProjects: \"Top Users by Projects\",\n        topUsersByProjectsDescription: \"Users with the most projects\",\n        projectOwnershipDistribution: \"Project Ownership Distribution\",\n        projectOwnershipDistributionDescription: \"Who owns the most projects\",\n        topUsersByTasks: \"Top Users by Tasks\",\n        topUsersByTasksDescription: \"Users with the most tasks\",\n        taskCompletionByUser: \"Task Completion by User\",\n        taskCompletionByUserDescription: \"Who completes the most tasks\",\n        projectsByTeamSize: \"Projects by Team Size\",\n        projectsByTeamSizeDescription: \"Distribution of projects by number of participants\",\n        collaborationDistribution: \"Collaboration Distribution\",\n        collaborationDistributionDescription: \"How projects are distributed by team size\",\n        totalProjects: \"Total Projects\",\n        ownedProjects: \"Owned Projects\",\n        participatedProjects: \"Participated Projects\",\n        completedTasks: \"Completed Tasks\",\n        inProgressTasks: \"In Progress Tasks\",\n        todoTasks: \"To Do Tasks\",\n        singleUserProjects: \"Single User Projects\",\n        multiUserProjects: \"{count} User Projects\",\n        // Sidebar specific keys\n        aiAssistant: \"AI Assistant\",\n        aiTaskTracker: \"AI Task Tracker\",\n        // Project timeline keys\n        projectAge: \"Project Age\",\n        daysLeft: \"Days Left\",\n        days: \"days\",\n        daysOverdue: \"days overdue\",\n        noDueDate: \"No due date\",\n        noChanges: \"No Changes\",\n        allParticipantsAlreadyAdded: \"All selected participants are already in the project\",\n        // AI Assistant\n        error: \"Error\",\n        aiRequestFailed: \"Failed to process AI request. Please try again.\",\n        // Other\n        all: \"All\",\n        spreadsheets: \"Spreadsheets\",\n        useMainMenu: \"Use the sidebar menu for navigation\",\n        pdfs: \"PDFs\",\n        other: \"Other\",\n        noDocumentsFound: \"No documents found\",\n        noDocumentsMatchingSearch: \"No documents matching search '{query}'\",\n        noDocumentsInProject: \"No documents in this project\",\n        noProjectSelected: \"No project selected\",\n        selectProjectToViewDocuments: \"Select a project to view its documents\",\n        selectProjectToManageDocuments: \"Select a project to manage its documents\",\n        documentsForProject: \"Documents for project: {name}\",\n        searchDocuments: \"Search documents\",\n        // Language Toggle\n        toggleLanguage: \"Toggle language\",\n        // Delete Confirmation Dialog\n        confirmDelete: \"Confirm Delete\",\n        confirmDeleteDescription: \"Are you sure you want to delete this item? This action cannot be undone.\",\n        confirmDeleteEvent: \"Are you sure you want to delete this event? This action cannot be undone.\",\n        // Event Dialog\n        eventTitle: \"Event Title\",\n        eventTitlePlaceholder: \"Enter event title\",\n        eventType: \"Event Type\",\n        selectEventType: \"Select type\",\n        eventTask: \"Task\",\n        eventSubtask: \"Subtask\",\n        eventMeeting: \"Meeting\",\n        eventReminder: \"Reminder\",\n        eventPersonal: \"Personal\",\n        selectEventPriority: \"Select priority\",\n        eventStatus: \"Status\",\n        selectEventStatus: \"Select status\",\n        eventActive: \"Active\",\n        eventCompleted: \"Completed\",\n        eventProject: \"Project\",\n        selectEventProject: \"Select project\",\n        noProject: \"No project\",\n        eventDescription: \"Event Description\",\n        eventDescriptionPlaceholder: \"Enter event description\",\n        startDate: \"Start Date and Time\",\n        endDate: \"End Date and Time\",\n        // Admin Panel\n        adminPanel: \"Admin Panel\",\n        userManagement: \"User Management\",\n        systemSettings: \"System Settings\",\n        reminderStatus: \"Reminder Status\",\n        // User Management\n        searchUsers: \"Search users\",\n        addUser: \"Add User\",\n        addNewUser: \"Add New User\",\n        addNewUserDescription: \"Create a new user account\",\n        editUser: \"Edit User\",\n        editUserDescription: \"Edit user information\",\n        deleteUser: \"Delete User\",\n        blockUser: \"Block User\",\n        unblockUser: \"Unblock User\",\n        confirmBlock: \"Are you sure you want to block this user?\",\n        confirmUnblock: \"Are you sure you want to unblock this user?\",\n        selectRole: \"Select role\",\n        selectStatus: \"Select status\",\n        active: \"Active\",\n        blocked: \"Blocked\",\n        admin: \"Admin\",\n        user: \"User\",\n        noUsersFound: \"No users found\",\n        actions: \"Actions\",\n        // Notification Preferences\n        emailNotifications: \"Email Notifications\",\n        emailNotificationsDescription: \"Configure email reminder settings and preferences\",\n        enableEmailReminders: \"Enable Email Reminders\",\n        emailRemindersDescription: \"Receive task and project reminders via email\",\n        emailAddressForReminders: \"Email Address for Reminders\",\n        emailPlaceholder: \"<EMAIL>\",\n        telegramNotifications: \"Telegram Notifications\",\n        telegramNotificationsDescription: \"Set up Telegram bot integration for instant notifications\",\n        enableTelegramReminders: \"Enable Telegram Reminders\",\n        telegramRemindersDescription: \"Receive notifications through Telegram bot\",\n        telegramChatId: \"Telegram Chat ID\",\n        telegramChatIdPlaceholder: \"123456789 or @username\",\n        test: \"Test\",\n        testSuccessful: \"Test Successful\",\n        testSuccessfulDescription: \"Test message sent to Telegram successfully!\",\n        testFailed: \"Test Failed\",\n        testFailedDescription: \"Failed to send test message. Please check your chat ID and try again.\",\n        missingChatId: \"Missing Chat ID\",\n        missingChatIdDescription: \"Please enter your Telegram chat ID first.\",\n        invalidTime: \"Invalid Time\",\n        invalidTimeDescription: \"Please enter a valid positive number.\",\n        duplicateTime: \"Duplicate Time\",\n        duplicateTimeDescription: \"This reminder time already exists.\",\n        settingsSaved: \"Settings Saved\",\n        settingsSavedDescription: \"Your notification preferences have been updated successfully.\",\n        errorSaving: \"Error\",\n        errorSavingDescription: \"Failed to save notification preferences. Please try again.\",\n        reminderTiming: \"Reminder Timing\",\n        reminderTimingDescription: \"Configure when you want to receive reminders before deadlines\",\n        currentReminderTimes: \"Current Reminder Times\",\n        noReminderTimes: \"No reminder times configured\",\n        beforeDeadline: \"before\",\n        minutes: \"Minutes\",\n        hours: \"Hours\",\n        days: \"Days\",\n        notificationTypes: \"Notification Types\",\n        notificationTypesDescription: \"Choose which types of items you want to receive reminders for\",\n        taskReminders: \"Task Reminders\",\n        projectReminders: \"Project Reminders\",\n        calendarReminders: \"Calendar Reminders\",\n        overdueNotifications: \"Overdue Notifications\",\n        quietHours: \"Quiet Hours\",\n        quietHoursDescription: \"Set times when you don't want to receive notifications\",\n        enableQuietHours: \"Enable Quiet Hours\",\n        quietHoursInfo: \"Notifications will be delayed until quiet hours end\",\n        startTime: \"Start Time\",\n        endTime: \"End Time\",\n        saveChanges: \"Save Changes\",\n        saved: \"Saved\",\n        // Telegram Setup Instructions\n        telegramSetupTitle: \"How to get your Chat ID:\",\n        telegramSetupStep1: \"1. Start a chat with @userinfobot on Telegram\",\n        telegramSetupStep2: \"2. Send any message to get your Chat ID\",\n        telegramSetupStep3: \"3. Copy the number and paste it above\",\n        // Reminder Status\n        reminderScheduler: \"Reminder Scheduler\",\n        schedulerStatus: \"Scheduler Status\",\n        running: \"Running\",\n        stopped: \"Stopped\",\n        checkInterval: \"Check Interval\",\n        minutesShort: \"min\",\n        lastCheck: \"Last Check\",\n        never: \"Never\",\n        startScheduler: \"Start Scheduler\",\n        stopScheduler: \"Stop Scheduler\",\n        manualCheck: \"Manual Check\",\n        updateInterval: \"Update Interval\",\n        enabled: \"Enabled\",\n        disabled: \"Disabled\",\n        users: \"Users\",\n        // Test Suite\n        reminderSystemTestSuite: \"Reminder System Test Suite\",\n        testSystemFunctionality: \"Test the reminder and notification system functionality\",\n        runAllTests: \"Run All Tests\",\n        runningTests: \"Running Tests...\",\n        reset: \"Reset\",\n        testsReset: \"Tests Reset\",\n        testsResetDescription: \"Test results have been cleared\",\n        // Reminder Status Messages\n        schedulerStarted: \"Scheduler Started\",\n        schedulerStartedDescription: \"Reminders will be checked every {minutes} minutes\",\n        schedulerStopped: \"Scheduler Stopped\",\n        schedulerStoppedDescription: \"Automatic reminder checking is disabled\",\n        checkCompleted: \"Check Completed\",\n        checkCompletedDescription: \"Reminders checked and sent\",\n        checkFailedDescription: \"Failed to check reminders\",\n        intervalUpdated: \"Interval Updated\",\n        intervalUpdatedDescription: \"Check interval changed to {minutes} minutes\",\n        apiWorking: \"API Working\",\n        apiWorkingDescription: \"Test request to reminder API completed successfully\",\n        apiError: \"API Error\",\n        connectionError: \"Connection Error\",\n        connectionErrorDescription: \"Failed to connect to reminder API\",\n        unknownError: \"Unknown error\",\n        // Settings Page\n        theme: \"Theme\",\n        themeDescription: \"Choose your preferred color theme\",\n        lightDefault: \"Light (Default)\",\n        language: \"Language\",\n        languageDescription: \"Select your preferred language\",\n        english: \"English\",\n        // Header\n        myAccount: \"My Account\",\n        profile: \"Profile\",\n        headerSettings: \"Settings\",\n        adminPanel: \"Admin Panel\",\n        adminRole: \"Administrator\",\n        userRole: \"User\",\n        logout: \"Logout\",\n        notifications: \"Notifications\",\n        clearAll: \"Clear All\",\n        noNotifications: \"No notifications\",\n        role: \"Role\",\n        name: \"Name\",\n        editProfile: \"Edit Profile\",\n        appearance: \"Appearance\",\n        theme: \"Theme\",\n        language: \"Language\",\n        light: \"Light\",\n        dark: \"Dark\",\n        marketingEmails: \"Marketing Emails\",\n        taskNotifications: \"Task Notifications\",\n        systemNotifications: \"System Notifications\",\n        projectNotifications: \"Project Notifications\",\n        reminderSettings: \"Reminder Settings\",\n        emailReminders: \"Email Reminders\",\n        telegramReminders: \"Telegram Reminders\",\n        reminders: \"Reminders\",\n        emailRemindersDescription: \"Receive reminders via email\",\n        telegramRemindersDescription: \"Receive reminders via Telegram\",\n        emailForReminders: \"Email for reminders\",\n        telegramUsername: \"Telegram username\",\n        emailFromProfileUsed: \"Email from your profile is used\",\n        reminderSettingsSaved: \"Reminder settings have been updated\",\n        saveChanges: \"Save Changes\",\n        settingsUpdatedSuccess: \"Settings have been updated successfully\",\n        // Project Management Specific\n        validationError: \"Validation Error\",\n        projectNameRequired: \"Project name is required.\",\n        selectProjectToUseQuickActions: \"Select a project to use quick actions.\",\n        error: \"Error\",\n        cannotNavigateWithoutProject: \"Cannot navigate to tasks without a selected project.\",\n        createNewProject: \"Create New Project\",\n        projectNamePlaceholder: \"Enter project name\",\n        projectDescriptionPlaceholder: \"Enter project description\",\n        selectAssignee: \"Select assignee\",\n        unassigned: \"Unassigned\",\n        selectDate: \"Select date\",\n        create: \"Create\",\n        viewTasks: \"View Tasks\",\n        discussProject: \"Discuss Project\",\n        discussTask: \"Discuss Task\",\n        projectDetails: \"Project Details\",\n        // status: \"Status\", // Removed duplicate - already exists under // Documents\n        created: \"Created\",\n        notSet: \"Not Set\",\n        privacy: \"Privacy\",\n        public: \"Public\",\n        todo: \"To Do\",\n        quickActions: \"Quick Actions\",\n        viewCalendar: \"View Calendar\",\n        team: \"Team\",\n        teamMembers: \"Team Members\",\n        addMember: \"Add Member\",\n        improveWithAI: \"Improve with AI\",\n        currentDescription: \"Current Description\",\n        noDescriptionYet: \"No description added yet.\",\n        generating: \"Generating...\",\n        generateImprovedDescription: \"Generate Improved Description\",\n        aiSuggestion: \"AI Suggestion\",\n        aiSuggestionApplied: \"AI Suggestion Applied\",\n        descriptionUpdatedWithAI: \"Description updated with AI suggestion.\",\n        applyAISuggestion: \"Apply AI Suggestion\",\n        aiProjectPromptPlaceholder: \"e.g. Ask about project improvements, task suggestions, etc.\" // Added missing key\n    },\n    ru: {\n        // General\n        dashboard: \"Панель управления\",\n        projects: \"Проекты\",\n        analytics: \"Аналитика\",\n        settings: \"Настройки\",\n        tasks: \"Задачи\",\n        documents: \"Документы\",\n        save: \"Сохранить\",\n        cancel: \"Отмена\",\n        calendar: \"Календарь\",\n        // Tasks\n        toDo: \"К выполнению\",\n        inProgress: \"В процессе\",\n        done: \"Выполнено\",\n        addTask: \"Добавить задачу\",\n        deleteTask: \"Удалить задачу\",\n        editTask: \"Редактировать задачу\",\n        taskTitle: \"Название задачи\",\n        taskTitlePlaceholder: \"Введите название задачи\",\n        taskDescription: \"Описание задачи\",\n        taskDescriptionPlaceholder: \"Введите описание задачи\",\n        taskPriority: \"Приоритет\",\n        high: \"Высокий\",\n        medium: \"Средний\",\n        low: \"Низкий\",\n        dueDate: \"Срок выполнения\",\n        selectPriority: \"Выберите приоритет\",\n        taskAge: \"Возраст задачи\",\n        daysLeft: \" дней осталось\",\n        daysOverdue: \" дней просрочено\",\n        taskUpdated: \"Задача обновлена\",\n        taskUpdatedSuccess: \"Задача успешно обновлена\",\n        taskDeleted: \"Задача удалена\",\n        taskDeletedSuccess: \"Задача успешно удалена\",\n        taskCreated: \"Задача создана\",\n        taskCreatedSuccess: \"Задача успешно создана\",\n        deleteTaskConfirmation: \"Вы уверены, что хотите удалить задачу {title}?\",\n        delete: \"Удалить\",\n        close: \"Закрыть\",\n        editTaskDescription: \"Редактирование деталей задачи\",\n        viewMode: \"Режим просмотра\",\n        kanban: \"Канбан\",\n        list: \"Список\",\n        searchTasks: \"Поиск задач\",\n        priority: \"Приоритет\",\n        allPriorities: \"Все приоритеты\",\n        assignee: \"Исполнитель\",\n        allAssignees: \"Все исполнители\",\n        noTasksFound: \"Задачи не найдены\",\n        add: \"Добавить\",\n        commaSeparatedTags: \"Теги, разделенные запятыми\",\n        // Subtasks\n        manageSubtasks: \"Управление подзадачами\",\n        manageSubtasksDescription: \"Добавление или управление подзадачами\",\n        subtasks: \"Подзадачи\",\n        newSubtask: \"Новая подзадача\",\n        newSubtaskPlaceholder: \"Введите название подзадачи\",\n        extractSubtasks: \"Извлечь как подзадачи\",\n        noSubtasksFound: \"Подзадачи не найдены\",\n        noSubtasksFoundDescription: \"Не удалось извлечь подзадачи из ответа ИИ\",\n        subtasksAdded: \"Подзадачи добавлены\",\n        subtasksAddedFromAI: \"Добавлено {count} подзадач из предложения ИИ\",\n        generateSubtasksWithAI: \"Создать подзадачи с помощью ИИ\",\n        // AI Assistant\n        aiAssist: \"ИИ-помощник\",\n        aiAssistDescription: \"Получить помощь ИИ для этой задачи\",\n        aiPrompt: \"Спросить ИИ\",\n        aiPromptPlaceholder: \"например: Помоги разбить эту задачу на подзадачи\",\n        aiPromptExample1: \"Разбей эту задачу\",\n        aiPromptExample2: \"Предложи приоритет\",\n        aiPromptExample3: \"Рекомендуй следующие шаги\",\n        aiResponse: \"Ответ ИИ\",\n        generateResponse: \"Сгенерировать\",\n        thinking: \"Думаю...\",\n        currentTask: \"Текущая задача\",\n        // Projects\n        projectProgress: \"Прогресс проекта\",\n        createProject: \"Создать проект\",\n        editProject: \"Редактировать проект\",\n        deleteProject: \"Удалить проект\",\n        projectName: \"Название проекта\",\n        projectDescription: \"Описание проекта\",\n        selectProject: \"Выбрать проект\",\n        newProject: \"Новый проект\",\n        projectCreated: \"Проект создан\",\n        projectCreatedDescription: \"Проект успешно создан\",\n        projectUpdated: \"Проект обновлен\",\n        projectUpdatedDescription: \"Проект успешно обновлен\",\n        projectDeleted: \"Проект удален\",\n        projectDeletedDescription: \"Проект успешно удален\",\n        deleteProjectConfirmation: \"Вы уверены, что хотите удалить проект {name}?\",\n        projectCompletion: \"Завершенность проекта\",\n        aiProjectAssistant: \"ИИ-помощник проекта\",\n        askAI: \"Спросить ИИ\",\n        applyAISuggestions: \"Применить предложения ИИ\",\n        aiSuggestionsApplied: \"Предложения ИИ применены\",\n        projectUpdatedWithAI: \"Проект обновлен с учетом предложений ИИ\",\n        privateProject: \"Приватный проект\",\n        privateProjectDescription: \"Приватные проекты видны только вам и людям, с которыми вы ими делитесь\",\n        private: \"Приватный\",\n        shared: \"Общедоступный\",\n        shareProject: \"Поделиться проектом\",\n        currentParticipants: \"Текущие участники\",\n        addParticipants: \"Добавить участников\",\n        searchTeamMembers: \"Поиск участников команды\",\n        noResultsFound: \"Результаты не найдены\",\n        selectedParticipants: \"Выбранные участники\",\n        share: \"Поделиться\",\n        projectShared: \"Проект доступен для совместной работы\",\n        projectSharedDescription: \"Проект доступен для {count} участников\",\n        participantRemoved: \"Участник удален\",\n        participantRemovedDescription: \"Участник удален из проекта\",\n        totalProjects: \"Всего проектов\",\n        completedProjects: \"Завершенных проектов\",\n        inProgressProjects: \"В процессе\",\n        averageCompletion: \"Средняя завершенность\",\n        noProjects: \"Нет проектов\",\n        createFirstProject: \"Создайте свой первый проект, чтобы начать управление задачами\",\n        participants: \"Участники\",\n        noParticipants: \"Нет участников\",\n        actions: \"Действия\",\n        completion: \"Завершенность\",\n        createdAt: \"Создан\",\n        // Documents\n        projectDocuments: \"Документы проекта\",\n        uploadDocument: \"Загрузить документ\",\n        addDocumentLink: \"Добавить ссылку на документ\",\n        viewDocuments: \"Просмотр документов\",\n        noDocuments: \"Нет документов для этого проекта\",\n        documentName: \"Название документа\",\n        documentType: \"Тип документа\",\n        documentURL: \"URL документа\",\n        addDocument: \"Добавить документ\",\n        addDocumentDescription: \"Добавить ссылку на документ к текущему проекту\",\n        documentUploaded: \"Документ загружен\",\n        documentUploadedSuccess: \"Документ {name} успешно загружен\",\n        documentAdded: \"Документ добавлен\",\n        documentAddedSuccess: \"Документ {name} успешно добавлен\",\n        editDocument: \"Редактировать документ\",\n        deleteDocument: \"Удалить документ\",\n        deleteDocumentConfirmation: \"Вы уверены, что хотите удалить документ {name}?\",\n        documentDeleted: \"Документ удален\",\n        documentDeletedSuccessfully: \"Документ успешно удален\",\n        documentUpdated: \"Документ обновлен\",\n        documentUpdatedSuccessfully: \"Документ {name} успешно обновлен\",\n        folders: \"Папки\",\n        files: \"Файлы\",\n        newFolder: \"Новая папка\",\n        uploadFiles: \"Загрузить файлы\",\n        uploadFilesDescription: \"Загрузка файлов в текущий проект\",\n        dropFilesHere: \"Перетащите файлы сюда\",\n        or: \"или\",\n        browseFiles: \"Выбрать файлы\",\n        selectedFiles: \"Выбранные файлы\",\n        uploading: \"Загрузка...\",\n        upload: \"Загрузить\",\n        uploadComplete: \"Загрузка завершена\",\n        filesUploadedSuccessfully: \"{count} файлов успешно загружено\",\n        createdBy: \"Создал\",\n        lastModified: \"Последнее изменение\",\n        size: \"Размер\",\n        modified: \"Изменен\",\n        folder: \"Папка\",\n        noFolder: \"Без папки\",\n        tags: \"Теги\",\n        content: \"Содержимое\",\n        aiDocumentAssistant: \"ИИ-помощник для документов\",\n        aiDocumentPromptPlaceholder: \"например: Улучши этот документ, Сделай резюме, Предложи теги\",\n        aiDocumentPromptSuggestion1: \"Улучшить документ\",\n        aiDocumentPromptSuggestion2: \"Сделать резюме\",\n        aiDocumentPromptSuggestion3: \"Предложить теги\",\n        currentDocument: \"Текущий документ\",\n        documentUpdatedWithAI: \"Документ обновлен с учетом предложений ИИ\",\n        documentAIUpdated: \"Документ {name} обновлен с учетом предложений ИИ\",\n        openMenu: \"Открыть меню\",\n        view: \"Просмотр\",\n        edit: \"Редактировать\",\n        download: \"Скачать\",\n        totalDocuments: \"Всего документов\",\n        totalSize: \"Общий размер\",\n        latestUpdate: \"Последнее обновление\",\n        documentTypes: \"Типы документов\",\n        groupByType: \"Группировать по типу\",\n        version: \"Версия\",\n        status: \"Статус\",\n        description: \"Описание\",\n        // Admin\n        adminDashboard: \"Панель администратора\",\n        userManagement: \"Управление пользователями\",\n        blockUser: \"Заблокировать пользователя\",\n        unblockUser: \"Разблокировать пользователя\",\n        deleteUser: \"Удалить пользователя\",\n        confirmDelete: \"Вы уверены, что хотите удалить этого пользователя?\",\n        confirmBlock: \"Вы уверены, что хотите заблокировать этого пользователя?\",\n        confirmUnblock: \"Вы уверены, что хотите разблокировать этого пользователя?\",\n        username: \"Имя пользователя\",\n        email: \"Email\",\n        active: \"Активен\",\n        blocked: \"Заблокирован\",\n        // Settings\n        settingsSaved: \"Настройки сохранены\",\n        generalSettingsSaved: \"Общие настройки успешно сохранены\",\n        aiSettingsSaved: \"Настройки ИИ успешно сохранены\",\n        emailSettingsSaved: \"Настройки электронной почты успешно сохранены\",\n        aiModelApiKey: \"API-ключ модели ИИ\",\n        enterApiKeyPlaceholder: \"Введите ваш API-ключ\",\n        // Admin Settings specific keys\n        generalSettings: \"Общие настройки\",\n        aiSettings: \"Настройки ИИ\",\n        emailSettings: \"Настройки Email\",\n        generalSettingsDescription: \"Настройка общих параметров сайта\",\n        aiSettingsDescription: \"Настройка параметров ИИ-помощника\",\n        emailSettingsDescription: \"Настройка параметров уведомлений по email\",\n        siteName: \"Название сайта\",\n        siteDescription: \"Описание сайта\",\n        allowRegistration: \"Разрешить регистрацию пользователей\",\n        requireEmailVerification: \"Требовать подтверждение Email\",\n        maxProjectsPerUser: \"Макс. проектов на пользователя\",\n        maxTasksPerProject: \"Макс. задач на проект\",\n        saveSettings: \"Сохранить настройки\",\n        enableAI: \"Включить ИИ-помощника\",\n        aiModel: \"Модель ИИ\",\n        aiTemperature: \"Температура ИИ\",\n        maxTokens: \"Макс. токенов\",\n        customPrompt: \"Пользовательский системный промпт\",\n        aiWarning: \"Предупреждение об использовании ИИ\",\n        aiWarningDescription: \"Информировать пользователей о возможных неточностях ИИ или затратах\",\n        enableEmailNotifications: \"Включить уведомления по Email\",\n        smtpServer: \"SMTP Сервер\",\n        smtpPort: \"SMTP Порт\",\n        smtpUsername: \"SMTP Имя пользователя\",\n        smtpPassword: \"SMTP Пароль\",\n        fromEmail: \"Email отправителя\",\n        systemSettings: \"Системные настройки\",\n        // Admin Dashboard specific keys\n        adminDashboardDescription: \"Обзор статистики и управления системой\",\n        totalUsers: \"Всего пользователей\",\n        activeUsers: \"Активных пользователей\",\n        totalTasks: \"Всего задач\",\n        completedTasks: \"Завершенных задач\",\n        completionRate: \"Уровень завершения\",\n        taskCompletionRate: \"Уровень завершения задач\",\n        // Dashboard specific keys\n        logoutSuccessful: \"Выход выполнен успешно\",\n        youHaveBeenLoggedOut: \"Вы успешно вышли из системы.\",\n        // Analytics specific keys\n        taskDistribution: \"Распределение задач\",\n        projectStatistics: \"Статистика проекта\",\n        tasksByStatus: \"Задачи по статусу\",\n        tasksByStatusDescription: \"Распределение задач по различным статусам\",\n        taskCompletion: \"Завершение задач со временем\",\n        taskCompletionDescription: \"Тренд завершения задач\",\n        completed: \"Завершено\",\n        projectsByStatus: \"Проекты по статусу\",\n        projectsByStatusDescription: \"Распределение проектов по различным статусам\",\n        onHold: \"На удержании\",\n        monthlyProjects: \"Создание проектов по месяцам\",\n        monthlyProjectsDescription: \"Тренд создания новых проектов каждый месяц\",\n        // User Analytics specific keys\n        userParticipation: \"Участие пользователей\",\n        userActivity: \"Активность пользователей\",\n        projectCollaboration: \"Совместная работа\",\n        topUsersByProjects: \"Топ пользователей по проектам\",\n        topUsersByProjectsDescription: \"Пользователи с наибольшим количеством проектов\",\n        projectOwnershipDistribution: \"Распределение владения проектами\",\n        projectOwnershipDistributionDescription: \"Кто владеет большинством проектов\",\n        topUsersByTasks: \"Топ пользователей по задачам\",\n        topUsersByTasksDescription: \"Пользователи с наибольшим количеством задач\",\n        taskCompletionByUser: \"Выполнение задач по пользователям\",\n        taskCompletionByUserDescription: \"Кто выполняет больше всего задач\",\n        projectsByTeamSize: \"Проекты по размеру команды\",\n        projectsByTeamSizeDescription: \"Распределение проектов по количеству участников\",\n        collaborationDistribution: \"Распределение совместной работы\",\n        collaborationDistributionDescription: \"Как распределены проекты по количеству участников\",\n        totalProjects: \"Всего проектов\",\n        ownedProjects: \"Владеет проектами\",\n        participatedProjects: \"Участвует в проектах\",\n        completedTasks: \"Завершенные задачи\",\n        inProgressTasks: \"Задачи в процессе\",\n        todoTasks: \"Задачи в очереди\",\n        singleUserProjects: \"Проекты с 1 участником\",\n        multiUserProjects: \"Проекты с {count} участниками\",\n        // Sidebar specific keys\n        aiAssistant: \"ИИ-помощник\",\n        aiTaskTracker: \"ИИ Трекер Задач\",\n        // Project timeline keys\n        projectAge: \"Возраст проекта\",\n        daysLeft: \"Осталось дней\",\n        days: \"дней\",\n        daysOverdue: \"дней просрочено\",\n        noDueDate: \"Нет срока\",\n        noChanges: \"Без изменений\",\n        allParticipantsAlreadyAdded: \"Все выбранные участники уже добавлены в проект\",\n        // AI Assistant\n        error: \"Ошибка\",\n        aiRequestFailed: \"Не удалось обработать запрос к ИИ. Пожалуйста, попробуйте еще раз.\",\n        // Other\n        all: \"Все\",\n        spreadsheets: \"Таблицы\",\n        useMainMenu: \"Используйте боковое меню для навигации\",\n        pdfs: \"PDF-документы\",\n        other: \"Другое\",\n        noDocumentsFound: \"Документы не найдены\",\n        noDocumentsMatchingSearch: \"Нет документов, соответствующих запросу '{query}'\",\n        noDocumentsInProject: \"В этом проекте нет документов\",\n        noProjectSelected: \"Проект не выбран\",\n        selectProjectToViewDocuments: \"Выберите проект для просмотра документов\",\n        selectProjectToManageDocuments: \"Выберите проект для управления документами\",\n        documentsForProject: \"Документы проекта: {name}\",\n        searchDocuments: \"Поиск документов\",\n        // Language Toggle\n        toggleLanguage: \"Переключить язык\",\n        // Delete Confirmation Dialog\n        confirmDelete: \"Подтверждение удаления\",\n        confirmDeleteDescription: \"Вы уверены, что хотите удалить этот элемент? Это действие нельзя отменить.\",\n        confirmDeleteEvent: \"Вы уверены, что хотите удалить это событие? Это действие нельзя отменить.\",\n        // Event Dialog\n        eventTitle: \"Название события\",\n        eventTitlePlaceholder: \"Введите название события\",\n        eventType: \"Тип события\",\n        selectEventType: \"Выберите тип\",\n        eventTask: \"Задача\",\n        eventSubtask: \"Подзадача\",\n        eventMeeting: \"Встреча\",\n        eventReminder: \"Напоминание\",\n        eventPersonal: \"Личное\",\n        selectEventPriority: \"Выберите приоритет\",\n        eventStatus: \"Статус\",\n        selectEventStatus: \"Выберите статус\",\n        eventActive: \"Активно\",\n        eventCompleted: \"Завершено\",\n        eventProject: \"Проект\",\n        selectEventProject: \"Выберите проект\",\n        noProject: \"Без проекта\",\n        eventDescription: \"Описание события\",\n        eventDescriptionPlaceholder: \"Введите описание события\",\n        startDate: \"Дата и время начала\",\n        endDate: \"Дата и время окончания\",\n        // Admin Panel\n        adminPanel: \"Панель администратора\",\n        userManagement: \"Управление пользователями\",\n        systemSettings: \"Системные настройки\",\n        reminderStatus: \"Статус напоминаний\",\n        // User Management\n        searchUsers: \"Поиск пользователей\",\n        addUser: \"Добавить пользователя\",\n        addNewUser: \"Добавить нового пользователя\",\n        addNewUserDescription: \"Создать новую учетную запись пользователя\",\n        editUser: \"Редактировать пользователя\",\n        editUserDescription: \"Редактировать информацию о пользователе\",\n        deleteUser: \"Удалить пользователя\",\n        blockUser: \"Заблокировать пользователя\",\n        unblockUser: \"Разблокировать пользователя\",\n        confirmBlock: \"Вы уверены, что хотите заблокировать этого пользователя?\",\n        confirmUnblock: \"Вы уверены, что хотите разблокировать этого пользователя?\",\n        selectRole: \"Выберите роль\",\n        selectStatus: \"Выберите статус\",\n        active: \"Активный\",\n        blocked: \"Заблокирован\",\n        admin: \"Администратор\",\n        user: \"Пользователь\",\n        noUsersFound: \"Пользователи не найдены\",\n        actions: \"Действия\",\n        // Notification Preferences\n        emailNotifications: \"Email уведомления\",\n        emailNotificationsDescription: \"Настройка параметров email напоминаний\",\n        enableEmailReminders: \"Включить Email напоминания\",\n        emailRemindersDescription: \"Получать напоминания о задачах и проектах по email\",\n        emailAddressForReminders: \"Email адрес для напоминаний\",\n        emailPlaceholder: \"<EMAIL>\",\n        telegramNotifications: \"Telegram уведомления\",\n        telegramNotificationsDescription: \"Настройка интеграции с Telegram ботом для мгновенных уведомлений\",\n        enableTelegramReminders: \"Включить Telegram напоминания\",\n        telegramRemindersDescription: \"Получать уведомления через Telegram бота\",\n        telegramChatId: \"Telegram Chat ID\",\n        telegramChatIdPlaceholder: \"123456789 или @username\",\n        test: \"Тест\",\n        testSuccessful: \"Тест успешен\",\n        testSuccessfulDescription: \"Тестовое сообщение успешно отправлено в Telegram!\",\n        testFailed: \"Тест не удался\",\n        testFailedDescription: \"Не удалось отправить тестовое сообщение. Проверьте ваш chat ID и попробуйте снова.\",\n        missingChatId: \"Отсутствует Chat ID\",\n        missingChatIdDescription: \"Пожалуйста, сначала введите ваш Telegram chat ID.\",\n        invalidTime: \"Неверное время\",\n        invalidTimeDescription: \"Пожалуйста, введите корректное положительное число.\",\n        duplicateTime: \"Дублирующееся время\",\n        duplicateTimeDescription: \"Это время напоминания уже существует.\",\n        settingsSaved: \"Настройки сохранены\",\n        settingsSavedDescription: \"Ваши настройки уведомлений успешно обновлены.\",\n        errorSaving: \"Ошибка\",\n        errorSavingDescription: \"Не удалось сохранить настройки уведомлений. Попробуйте снова.\",\n        reminderTiming: \"Время напоминаний\",\n        reminderTimingDescription: \"Настройте, когда вы хотите получать напоминания до дедлайнов\",\n        currentReminderTimes: \"Текущие времена напоминаний\",\n        noReminderTimes: \"Времена напоминаний не настроены\",\n        beforeDeadline: \"до дедлайна\",\n        minutes: \"Минуты\",\n        hours: \"Часы\",\n        days: \"Дни\",\n        notificationTypes: \"Типы уведомлений\",\n        notificationTypesDescription: \"Выберите, для каких элементов вы хотите получать напоминания\",\n        taskReminders: \"Напоминания о задачах\",\n        projectReminders: \"Напоминания о проектах\",\n        calendarReminders: \"Напоминания календаря\",\n        overdueNotifications: \"Уведомления о просрочке\",\n        quietHours: \"Тихие часы\",\n        quietHoursDescription: \"Установите время, когда вы не хотите получать уведомления\",\n        enableQuietHours: \"Включить тихие часы\",\n        quietHoursInfo: \"Уведомления будут отложены до окончания тихих часов\",\n        startTime: \"Время начала\",\n        endTime: \"Время окончания\",\n        saveChanges: \"Сохранить изменения\",\n        saved: \"Сохранено\",\n        // Telegram Setup Instructions\n        telegramSetupTitle: \"Как получить ваш Chat ID:\",\n        telegramSetupStep1: \"1. Начните чат с @userinfobot в Telegram\",\n        telegramSetupStep2: \"2. Отправьте любое сообщение, чтобы получить ваш Chat ID\",\n        telegramSetupStep3: \"3. Скопируйте номер и вставьте его выше\",\n        // Reminder Status\n        reminderScheduler: \"Планировщик напоминаний\",\n        schedulerStatus: \"Статус планировщика\",\n        running: \"Работает\",\n        stopped: \"Остановлен\",\n        checkInterval: \"Интервал проверки\",\n        minutesShort: \"мин\",\n        lastCheck: \"Последняя проверка\",\n        never: \"Никогда\",\n        startScheduler: \"Запустить планировщик\",\n        stopScheduler: \"Остановить планировщик\",\n        manualCheck: \"Ручная проверка\",\n        updateInterval: \"Обновить интервал\",\n        enabled: \"Включено\",\n        disabled: \"Отключено\",\n        users: \"Пользователи\",\n        // Test Suite\n        reminderSystemTestSuite: \"Набор тестов системы напоминаний\",\n        testSystemFunctionality: \"Тестирование функциональности системы напоминаний и уведомлений\",\n        runAllTests: \"Запустить все тесты\",\n        runningTests: \"Выполнение тестов...\",\n        reset: \"Сброс\",\n        testsReset: \"Тесты сброшены\",\n        testsResetDescription: \"Результаты тестов очищены\",\n        // Reminder Status Messages\n        schedulerStarted: \"Планировщик запущен\",\n        schedulerStartedDescription: \"Напоминания будут проверяться каждые {minutes} минут\",\n        schedulerStopped: \"Планировщик остановлен\",\n        schedulerStoppedDescription: \"Автоматическая проверка напоминаний отключена\",\n        checkCompleted: \"Проверка завершена\",\n        checkCompletedDescription: \"Напоминания проверены и отправлены\",\n        checkFailedDescription: \"Не удалось выполнить проверку напоминаний\",\n        intervalUpdated: \"Интервал обновлен\",\n        intervalUpdatedDescription: \"Интервал проверки изменен на {minutes} минут\",\n        apiWorking: \"API работает\",\n        apiWorkingDescription: \"Тестовый запрос к API напоминаний выполнен успешно\",\n        apiError: \"Ошибка API\",\n        connectionError: \"Ошибка соединения\",\n        connectionErrorDescription: \"Не удалось подключиться к API напоминаний\",\n        unknownError: \"Неизвестная ошибка\",\n        // Settings Page\n        theme: \"Тема\",\n        themeDescription: \"Выберите предпочитаемую цветовую тему\",\n        lightDefault: \"Светлая (по умолчанию)\",\n        language: \"Язык\",\n        languageDescription: \"Выберите предпочитаемый язык\",\n        english: \"Английский\",\n        // Header\n        myAccount: \"Мой аккаунт\",\n        profile: \"Профиль\",\n        headerSettings: \"Настройки\",\n        adminPanel: \"Панель администратора\",\n        adminRole: \"Администратор\",\n        userRole: \"Пользователь\",\n        logout: \"Выйти\",\n        notifications: \"Уведомления\",\n        clearAll: \"Очистить все\",\n        noNotifications: \"Нет уведомлений\",\n        role: \"Роль\",\n        name: \"Имя\",\n        editProfile: \"Редактировать профиль\",\n        appearance: \"Внешний вид\",\n        theme: \"Тема\",\n        language: \"Язык\",\n        light: \"Светлая\",\n        dark: \"Темная\",\n        marketingEmails: \"Маркетинговые рассылки\",\n        taskNotifications: \"Уведомления о задачах\",\n        systemNotifications: \"Системные уведомления\",\n        projectNotifications: \"Уведомления о проектах\",\n        reminderSettings: \"Настройки напоминаний\",\n        emailReminders: \"Напоминания на почту\",\n        telegramReminders: \"Напоминания в Telegram\",\n        reminders: \"Напоминания\",\n        emailRemindersDescription: \"Получать напоминания на электронную почту\",\n        telegramRemindersDescription: \"Получать напоминания в Telegram\",\n        emailForReminders: \"Email для напоминаний\",\n        telegramUsername: \"Имя пользователя в Telegram\",\n        emailFromProfileUsed: \"Используется email из вашего профиля\",\n        reminderSettingsSaved: \"Настройки напоминаний успешно обновлены\",\n        saveChanges: \"Сохранить изменения\",\n        settingsUpdatedSuccess: \"Настройки успешно обновлены\",\n        // Project Management Specific\n        validationError: \"Ошибка валидации\",\n        projectNameRequired: \"Название проекта обязательно.\",\n        selectProjectToUseQuickActions: \"Выберите проект для использования быстрых действий.\",\n        error: \"Ошибка\",\n        cannotNavigateWithoutProject: \"Невозможно перейти к задачам без выбранного проекта.\",\n        createNewProject: \"Создать новый проект\",\n        projectNamePlaceholder: \"Введите название проекта\",\n        projectDescriptionPlaceholder: \"Введите описание проекта\",\n        selectAssignee: \"Выберите исполнителя\",\n        unassigned: \"Не назначен\",\n        selectDate: \"Выберите дату\",\n        create: \"Создать\",\n        viewTasks: \"Просмотр задач\",\n        discussProject: \"Обсудить проект\",\n        discussTask: \"Обсудить задачу\",\n        projectDetails: \"Детали проекта\",\n        // status: \"Статус\", // Removed duplicate - already exists under // Documents\n        created: \"Создан\",\n        notSet: \"Не указан\",\n        privacy: \"Приватность\",\n        public: \"Публичный\",\n        todo: \"К выполнению\",\n        quickActions: \"Быстрые действия\",\n        viewCalendar: \"Просмотр календаря\",\n        team: \"Команда\",\n        teamMembers: \"Участники команды\",\n        addMember: \"Добавить участника\",\n        improveWithAI: \"Улучшить с помощью ИИ\",\n        currentDescription: \"Текущее описание\",\n        noDescriptionYet: \"Описание еще не добавлено.\",\n        generating: \"Генерация...\",\n        generateImprovedDescription: \"Сгенерировать улучшенное описание\",\n        aiSuggestion: \"Предложение ИИ\",\n        aiSuggestionApplied: \"Предложение ИИ применено\",\n        descriptionUpdatedWithAI: \"Описание обновлено с помощью предложения ИИ.\",\n        applyAISuggestion: \"Применить предложение ИИ\",\n        aiProjectPromptPlaceholder: \"например: Спроси об улучшениях проекта, предложениях задач и т.д.\" // Added missing key\n    }\n};\n// Create the context\n// Adjust the context definition to match the provider's t function signature\nconst TranslationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    t: (key, params = {})=>key,\n    locale: \"en\",\n    setLocale: ()=>{}\n});\nfunction TranslationProvider({ children }) {\n    const [locale, setLocale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\");\n    // Translation function\n    // Use TranslationKey for the key parameter\n    const t = (key, params = {})=>{\n        const currentLocale = locale; // Cast locale\n        // Now TS knows 'key' is a valid key for languages[currentLocale]\n        const translation = languages[currentLocale]?.[key] || key;\n        // Replace parameters in the translation\n        if (params && Object.keys(params).length > 0) {\n            return Object.keys(params).reduce((acc, paramKey)=>{\n                return acc.replace(`{${paramKey}}`, params[paramKey]);\n            }, translation);\n        }\n        return translation;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TranslationContext.Provider, {\n        value: {\n            t,\n            locale,\n            setLocale\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\lib\\\\translations.tsx\",\n        lineNumber: 1177,\n        columnNumber: 10\n    }, this);\n}\n// Custom hook to use the translation context\nfunction useTranslation() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TranslationContext);\n    if (context === undefined) {\n        throw new Error(\"useTranslation must be used within a TranslationProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/translations.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Creminder-initializer.tsx%22%2C%22ids%22%3A%5B%22ReminderInitializer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Creminder-initializer.tsx%22%2C%22ids%22%3A%5B%22ReminderInitializer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/reminder-initializer.tsx */ \"(ssr)/./components/reminder-initializer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/socket-context.tsx */ \"(ssr)/./lib/socket-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/translations.tsx */ \"(ssr)/./lib/translations.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Creminder-initializer.tsx%22%2C%22ids%22%3A%5B%22ReminderInitializer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/nodemailer","vendor-chunks/@opentelemetry","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/debug","vendor-chunks/socket.io-parser","vendor-chunks/engine.io-parser","vendor-chunks/next-themes","vendor-chunks/@socket.io","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();