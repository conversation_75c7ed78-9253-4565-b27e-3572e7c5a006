import { type NextRequest, NextResponse } from "next/server"

// In a real application, this would connect to a database
// For now, we'll use an in-memory store
let tasks = {
  todo: [
    {
      id: "t1",
      title: "Design landing page",
      description: "Create wireframes and mockups",
      dueDate: "2023-12-15",
      priority: "high",
    },
    {
      id: "t2",
      title: "Setup database",
      description: "Configure PostgreSQL with Prisma",
      dueDate: "2023-12-10",
      priority: "medium",
    },
  ],
  inProgress: [
    {
      id: "t3",
      title: "Implement authentication",
      description: "Add login and registration",
      dueDate: "2023-12-20",
      priority: "high",
    },
  ],
  done: [
    {
      id: "t4",
      title: "Project setup",
      description: "Initialize Next.js project",
      dueDate: "2023-12-01",
      priority: "low",
    },
  ],
}

export async function GET() {
  return NextResponse.json(tasks)
}

export async function POST(req: NextRequest) {
  try {
    const { task, column } = await req.json()

    if (!task || !column) {
      return NextResponse.json({ error: "Task and column are required" }, { status: 400 })
    }

    // Generate a unique ID
    const id = `t${Date.now()}`
    const newTask = { id, ...task }

    // Add the task to the specified column
    tasks = {
      ...tasks,
      [column]: [...tasks[column], newTask],
    }

    return NextResponse.json({ id, success: true })
  } catch (error) {
    console.error("Error creating task:", error)
    return NextResponse.json({ error: "Failed to create task" }, { status: 500 })
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { id, task, fromColumn, toColumn } = await req.json()

    if (!id || !task) {
      return NextResponse.json({ error: "Task ID and updated task are required" }, { status: 400 })
    }

    // If moving between columns
    if (fromColumn && toColumn && fromColumn !== toColumn) {
      // Remove from source column
      tasks = {
        ...tasks,
        [fromColumn]: tasks[fromColumn].filter((t) => t.id !== id),
      }

      // Add to destination column
      tasks = {
        ...tasks,
        [toColumn]: [...tasks[toColumn], { id, ...task }],
      }
    } else {
      // Just update in place
      const column = fromColumn || Object.keys(tasks).find((col) => tasks[col].some((t) => t.id === id))

      if (!column) {
        return NextResponse.json({ error: "Task not found" }, { status: 404 })
      }

      tasks = {
        ...tasks,
        [column]: tasks[column].map((t) => (t.id === id ? { id, ...task } : t)),
      }
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error updating task:", error)
    return NextResponse.json({ error: "Failed to update task" }, { status: 500 })
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const id = searchParams.get("id")
    const column = searchParams.get("column")

    if (!id || !column) {
      return NextResponse.json({ error: "Task ID and column are required" }, { status: 400 })
    }

    // Remove the task
    tasks = {
      ...tasks,
      [column]: tasks[column].filter((t) => t.id !== id),
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting task:", error)
    return NextResponse.json({ error: "Failed to delete task" }, { status: 500 })
  }
}

