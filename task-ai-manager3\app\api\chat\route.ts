import { type NextRequest, NextResponse } from "next/server"
import { streamText } from "ai"
import { openai } from "@ai-sdk/openai"

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: NextRequest) {
  try {
    const { messages } = await req.json()

    // Detect language of the last user message
    const lastUserMessage = [...messages].reverse().find((m) => m.role === "user")
    const isRussian = lastUserMessage && /[а-яА-Я]/.test(lastUserMessage.content)

    // Set system message based on detected language
    const systemMessage = isRussian
      ? `Ты - ИИ-ассистент по управлению задачами, который действует как продукт-менеджер.
         Твоя задача - помогать пользователю в планировании, организации и выполнении задач.
         Ты должен быть проактивным, предлагать решения и разбивать большие цели на конкретные задачи.
         Отвечай на русском языке, используй профессиональную терминологию из области управления проектами.`
      : `You are an AI task management assistant that acts as a product manager.
         Your job is to help the user plan, organize, and complete tasks.
         You should be proactive, suggest solutions, and break down big goals into specific tasks.
         Respond in English, using professional terminology from the project management field.`

    const result = streamText({
      model: openai("gpt-4o"),
      system: systemMessage,
      messages,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error("Error in chat route:", error)
    return NextResponse.json({ error: "Failed to generate a response" }, { status: 500 })
  }
}

