import { type NextRequest, NextResponse } from "next/server"
import { streamText } from "ai"
import { getAIConfig, getSystemPrompt, createOpenAIClientSync } from '@/lib/ai-config'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: NextRequest) {
  try {
    const { messages } = await req.json()

    // Get AI configuration
    const config = await getAIConfig()

    // Check if AI is enabled
    if (!config.enabled) {
      return NextResponse.json({ error: "AI is currently disabled" }, { status: 503 })
    }

    // Detect language of the last user message
    const lastUserMessage = [...messages].reverse().find((m) => m.role === "user")
    const isRussian = lastUserMessage && /[а-яА-Я]/.test(lastUserMessage.content)

    // Get system message based on configuration and language
    const systemMessage = await getSystemPrompt(isRussian)

    // Create OpenAI client with current configuration
    const openai = createOpenAIClientSync()

    const result = streamText({
      model: openai,
      system: systemMessage,
      messages,
      temperature: config.temperature,
      maxTokens: config.maxTokens,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error("Error in chat route:", error)
    return NextResponse.json({ error: "Failed to generate a response" }, { status: 500 })
  }
}

