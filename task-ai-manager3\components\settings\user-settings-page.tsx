"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  User, 
  Bell, 
  History, 
  Palette, 
  Shield, 
  Settings,
  Camera
} from "lucide-react"
import { useTranslation } from "@/lib/translations"
import { useToast } from "@/components/ui/use-toast"
import { NotificationPreferences } from "./notification-preferences"
import { NotificationHistoryComponent } from "./notification-history"

interface UserSettingsPageProps {
  user: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  onUpdateProfile?: (user: any) => void
}

export function UserSettingsPage({ user, onUpdateProfile }: UserSettingsPageProps) {
  const { t } = useTranslation()
  const { toast } = useToast()
  
  // Profile state
  const [profileData, setProfileData] = useState({
    name: user.name || '',
    email: user.email || '',
    avatar: user.avatar || ''
  })
  
  const [hasProfileChanges, setHasProfileChanges] = useState(false)
  
  // Update profile data
  const updateProfileData = (updates: Partial<typeof profileData>) => {
    setProfileData(prev => ({ ...prev, ...updates }))
    setHasProfileChanges(true)
  }
  
  // Save profile changes
  const handleSaveProfile = () => {
    try {
      const updatedUser = {
        ...user,
        ...profileData
      }
      
      if (onUpdateProfile) {
        onUpdateProfile(updatedUser)
      }
      
      // Update localStorage
      localStorage.setItem('user', JSON.stringify(updatedUser))
      
      // Dispatch update event
      const event = new CustomEvent('updateUser', {
        detail: { user: updatedUser }
      })
      window.dispatchEvent(event)
      
      setHasProfileChanges(false)
      
      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully.",
      })
    } catch (error) {
      console.error('Error updating profile:', error)
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive"
      })
    }
  }
  
  // Handle avatar upload (placeholder)
  const handleAvatarUpload = () => {
    toast({
      title: "Avatar Upload",
      description: "Avatar upload functionality will be implemented soon.",
    })
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center gap-4 mb-6">
        <Settings className="h-8 w-8" />
        <div>
          <h1 className="text-3xl font-bold">User Settings</h1>
          <p className="text-muted-foreground">
            Manage your account settings and preferences
          </p>
        </div>
      </div>
      
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            History
          </TabsTrigger>
          <TabsTrigger value="appearance" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Appearance
          </TabsTrigger>
        </TabsList>
        
        {/* Profile Tab */}
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Update your personal information and profile settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Avatar Section */}
              <div className="flex items-center gap-6">
                <div className="relative">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src={profileData.avatar} alt={profileData.name} />
                    <AvatarFallback className="text-lg">
                      {profileData.name
                        ?.split(" ")
                        .map((n) => n[0])
                        .join("")
                        .toUpperCase() || "U"}
                    </AvatarFallback>
                  </Avatar>
                  <Button
                    size="sm"
                    variant="outline"
                    className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
                    onClick={handleAvatarUpload}
                  >
                    <Camera className="h-4 w-4" />
                  </Button>
                </div>
                <div>
                  <h3 className="font-semibold">{profileData.name}</h3>
                  <p className="text-sm text-muted-foreground">{profileData.email}</p>
                  <Button variant="outline" size="sm" className="mt-2" onClick={handleAvatarUpload}>
                    Change Avatar
                  </Button>
                </div>
              </div>
              
              {/* Profile Form */}
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={profileData.name}
                    onChange={(e) => updateProfileData({ name: e.target.value })}
                    placeholder="Enter your full name"
                  />
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profileData.email}
                    onChange={(e) => updateProfileData({ email: e.target.value })}
                    placeholder="Enter your email address"
                  />
                </div>
              </div>
              
              {/* Save Button */}
              <div className="flex justify-end">
                <Button 
                  onClick={handleSaveProfile}
                  disabled={!hasProfileChanges}
                >
                  {hasProfileChanges ? 'Save Changes' : 'Saved'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Notifications Tab */}
        <TabsContent value="notifications">
          <NotificationPreferences 
            userId={user.id} 
            userEmail={user.email}
          />
        </TabsContent>
        
        {/* History Tab */}
        <TabsContent value="history">
          <NotificationHistoryComponent userId={user.id} />
        </TabsContent>
        
        {/* Appearance Tab */}
        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Appearance Settings
              </CardTitle>
              <CardDescription>
                Customize the look and feel of your application
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  <strong>Coming Soon</strong><br />
                  Theme customization, dark mode, and other appearance settings 
                  will be available in a future update.
                </p>
              </div>
              
              <div className="grid gap-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-base">Theme</Label>
                    <p className="text-sm text-muted-foreground">
                      Choose your preferred color theme
                    </p>
                  </div>
                  <Button variant="outline" disabled>
                    Light (Default)
                  </Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-base">Language</Label>
                    <p className="text-sm text-muted-foreground">
                      Select your preferred language
                    </p>
                  </div>
                  <Button variant="outline" disabled>
                    English
                  </Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-base">Timezone</Label>
                    <p className="text-sm text-muted-foreground">
                      Set your local timezone for accurate reminders
                    </p>
                  </div>
                  <Button variant="outline" disabled>
                    UTC
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
