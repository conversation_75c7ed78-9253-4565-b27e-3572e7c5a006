import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Возвращаем пустой массив, чтобы избежать бесконечных запросов
    console.log('API /api/chat/list called - returning empty array to prevent infinite requests');
    return NextResponse.json([]);
  } catch (error) {
    console.error('Error in chat list API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch chat list' },
      { status: 500 }
    );
  }
}
