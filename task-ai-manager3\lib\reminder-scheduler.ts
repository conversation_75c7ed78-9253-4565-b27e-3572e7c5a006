"use client";

import { checkAndSendAllReminders } from './deadline-reminder-service';
import {
  processReminderQueue,
  cleanupOldData,
  createRemindersForItem,
  getUserReminderPreferences
} from './enhanced-reminder-service';

// Enhanced interface for reminder scheduler
interface EnhancedReminderScheduler {
  queueProcessorId: NodeJS.Timeout | null;
  cleanupId: NodeJS.Timeout | null;
  overdueCheckId: NodeJS.Timeout | null;
  syncId: NodeJS.Timeout | null;
  isRunning: boolean;
  queueInterval: number; // milliseconds
  cleanupInterval: number; // milliseconds
  overdueInterval: number; // milliseconds
  syncInterval: number; // milliseconds
}

// Global enhanced scheduler
let scheduler: EnhancedReminderScheduler = {
  queueProcessorId: null,
  cleanupId: null,
  overdueCheckId: null,
  syncId: null,
  isRunning: false,
  queueInterval: 60 * 1000, // 1 minute
  cleanupInterval: 60 * 60 * 1000, // 1 hour
  overdueInterval: 30 * 60 * 1000, // 30 minutes
  syncInterval: 6 * 60 * 60 * 1000, // 6 hours
};

// Enhanced function to start reminder scheduler
export function startReminderScheduler(intervalMinutes: number = 1): void {
  // Stop previous scheduler if running
  stopReminderScheduler();

  console.log('Starting enhanced reminder scheduler...');

  // Start queue processor (every minute)
  scheduler.queueProcessorId = setInterval(async () => {
    try {
      await processReminderQueue();
    } catch (error) {
      console.error('Error processing reminder queue:', error);
    }
  }, scheduler.queueInterval);

  // Start cleanup process (every hour)
  scheduler.cleanupId = setInterval(async () => {
    try {
      cleanupOldData();
    } catch (error) {
      console.error('Error cleaning up old data:', error);
    }
  }, scheduler.cleanupInterval);

  // Start overdue checker (every 30 minutes)
  scheduler.overdueCheckId = setInterval(async () => {
    try {
      await checkOverdueItems();
    } catch (error) {
      console.error('Error checking overdue items:', error);
    }
  }, scheduler.overdueInterval);

  // Start reminder sync (every 6 hours)
  scheduler.syncId = setInterval(async () => {
    try {
      await syncAllReminders();
    } catch (error) {
      console.error('Error syncing reminders:', error);
    }
  }, scheduler.syncInterval);

  // Run initial checks
  processReminderQueue();
  checkAndSendAllReminders();

  scheduler.isRunning = true;
  console.log('Enhanced reminder scheduler started successfully');
}

// Enhanced function to stop reminder scheduler
export function stopReminderScheduler(): void {
  console.log('Stopping enhanced reminder scheduler...');

  if (scheduler.queueProcessorId) {
    clearInterval(scheduler.queueProcessorId);
    scheduler.queueProcessorId = null;
  }

  if (scheduler.cleanupId) {
    clearInterval(scheduler.cleanupId);
    scheduler.cleanupId = null;
  }

  if (scheduler.overdueCheckId) {
    clearInterval(scheduler.overdueCheckId);
    scheduler.overdueCheckId = null;
  }

  if (scheduler.syncId) {
    clearInterval(scheduler.syncId);
    scheduler.syncId = null;
  }

  scheduler.isRunning = false;
  console.log('Enhanced reminder scheduler stopped');
}

// Check for overdue items and send notifications
async function checkOverdueItems(): Promise<void> {
  console.log('Checking for overdue items...');

  try {
    const items = await getAllItemsWithDueDates();
    const now = new Date();

    for (const item of items) {
      const dueDate = new Date(item.dueDate);

      if (dueDate < now && !item.completed) {
        const userId = item.userId || item.assignee?.id;
        if (userId) {
          const preferences = getUserReminderPreferences(userId);

          if (preferences.overdueReminders) {
            await createRemindersForItem(
              userId,
              item.type,
              item.id,
              now,
              'high'
            );
          }
        }
      }
    }
  } catch (error) {
    console.error('Error checking overdue items:', error);
  }
}

// Sync reminders for all items
async function syncAllReminders(): Promise<void> {
  console.log('Syncing reminders for all items...');

  try {
    const items = await getAllItemsWithDueDates();

    for (const item of items) {
      if (!item.completed && item.dueDate) {
        const userId = item.userId || item.assignee?.id;
        if (userId) {
          await createRemindersForItem(
            userId,
            item.type,
            item.id,
            new Date(item.dueDate),
            item.priority || 'medium'
          );
        }
      }
    }

    console.log(`Synced reminders for ${items.length} items`);
  } catch (error) {
    console.error('Error syncing reminders:', error);
  }
}

// Get all items with due dates from storage
async function getAllItemsWithDueDates(): Promise<any[]> {
  const items: any[] = [];

  if (typeof window !== 'undefined') {
    try {
      // Get tasks
      const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
      tasks.forEach((task: any) => {
        if (task.dueDate) {
          items.push({
            ...task,
            type: 'task',
            userId: task.assignee?.id
          });
        }

        // Include subtasks
        if (task.subtasks) {
          task.subtasks.forEach((subtask: any) => {
            if (subtask.dueDate) {
              items.push({
                ...subtask,
                type: 'subtask',
                userId: subtask.assignee?.id || task.assignee?.id
              });
            }
          });
        }
      });

      // Get projects
      const projects = JSON.parse(localStorage.getItem('projects') || '[]');
      projects.forEach((project: any) => {
        if (project.dueDate) {
          items.push({
            ...project,
            type: 'project',
            userId: project.owner?.id
          });
        }
      });

      // Get calendar events
      const events = JSON.parse(localStorage.getItem('events') || '[]');
      events.forEach((event: any) => {
        if (event.start) {
          items.push({
            ...event,
            type: 'event',
            dueDate: event.start,
            userId: event.attendees?.[0]?.id
          });
        }
      });
    } catch (error) {
      console.error('Error loading items from storage:', error);
    }
  }

  return items;
}

// Function to check scheduler status
export function isReminderSchedulerRunning(): boolean {
  return scheduler.isRunning;
}

// Manual reminder check function
export async function manualReminderCheck(): Promise<void> {
  console.log('Running manual reminder check...');
  try {
    await processReminderQueue();
    await checkOverdueItems();
    await checkAndSendAllReminders();
    console.log('Manual reminder check completed successfully');
  } catch (error) {
    console.error('Error in manual reminder check:', error);
    throw error;
  }
}

// Функция для получения интервала проверки
export function getReminderCheckInterval(): number {
  return scheduler.checkInterval / (60 * 1000); // возвращаем в минутах
}

// Функция для изменения интервала проверки
export function setReminderCheckInterval(intervalMinutes: number): void {
  if (scheduler.isRunning) {
    startReminderScheduler(intervalMinutes);
  } else {
    scheduler.checkInterval = intervalMinutes * 60 * 1000;
  }
}



// Функция для инициализации планировщика при загрузке приложения
export function initializeReminderScheduler(): void {
  if (typeof window === 'undefined') {
    return; // Не запускаем на сервере
  }
  
  // Проверяем, включены ли напоминания
  const emailReminders = localStorage.getItem('emailReminders') === 'true';
  const telegramReminders = localStorage.getItem('telegramReminders') === 'true';
  
  if (emailReminders || telegramReminders) {
    // Получаем интервал проверки из настроек (по умолчанию 5 минут)
    const savedInterval = localStorage.getItem('reminderCheckInterval');
    const intervalMinutes = savedInterval ? parseInt(savedInterval) : 5;
    
    startReminderScheduler(intervalMinutes);
    
    console.log('Reminder scheduler initialized and started');
  } else {
    console.log('Reminders are disabled, scheduler not started');
  }
}

// Функция для сохранения интервала проверки в localStorage
export function saveReminderCheckInterval(intervalMinutes: number): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('reminderCheckInterval', String(intervalMinutes));
  }
}

// Функция для получения сохраненного интервала проверки
export function getSavedReminderCheckInterval(): number {
  if (typeof window === 'undefined') {
    return 5; // По умолчанию 5 минут
  }
  
  const saved = localStorage.getItem('reminderCheckInterval');
  return saved ? parseInt(saved) : 5;
}

// Функция для обновления планировщика при изменении настроек
export function updateReminderScheduler(): void {
  if (typeof window === 'undefined') {
    return;
  }
  
  const emailReminders = localStorage.getItem('emailReminders') === 'true';
  const telegramReminders = localStorage.getItem('telegramReminders') === 'true';
  
  if (emailReminders || telegramReminders) {
    if (!scheduler.isRunning) {
      const intervalMinutes = getSavedReminderCheckInterval();
      startReminderScheduler(intervalMinutes);
    }
  } else {
    if (scheduler.isRunning) {
      stopReminderScheduler();
    }
  }
}

// Функция для получения статистики планировщика
export function getReminderSchedulerStats(): {
  isRunning: boolean;
  intervalMinutes: number;
  nextCheckIn?: number; // секунды до следующей проверки
} {
  const stats = {
    isRunning: scheduler.isRunning,
    intervalMinutes: scheduler.checkInterval / (60 * 1000),
  };
  
  // Если планировщик запущен, вычисляем время до следующей проверки
  // Это приблизительная оценка, так как мы не отслеживаем точное время последней проверки
  if (scheduler.isRunning) {
    return {
      ...stats,
      nextCheckIn: Math.floor(scheduler.checkInterval / 1000),
    };
  }
  
  return stats;
}

// Автоматическая инициализация при загрузке модуля (только в браузере)
if (typeof window !== 'undefined') {
  // Инициализируем планировщик после небольшой задержки, чтобы дать время загрузиться приложению
  setTimeout(() => {
    initializeReminderScheduler();
  }, 2000);
  
  // Обновляем планировщик при изменении видимости страницы
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      // Страница стала видимой, проверяем напоминания
      manualReminderCheck();
    }
  });
  
  // Обновляем планировщик при фокусе на окне
  window.addEventListener('focus', () => {
    manualReminderCheck();
  });
}
