"use server";

import { prisma } from './prisma';
import { sendReminderEmail, ReminderData } from './email-service';

// Интерфейс для напоминания
export interface Reminder {
  id: string;
  userId: string;
  itemType: 'event' | 'task' | 'subtask' | 'project';
  itemId: string;
  reminderTime: Date;
  sent: boolean;
  sentAt?: Date;
}

// Функция для создания напоминания
export async function createReminder(
  userId: string,
  itemType: 'event' | 'task' | 'subtask' | 'project',
  itemId: string,
  reminderTime: Date
): Promise<Reminder | null> {
  try {
    // В реальном приложении здесь будет создание записи в базе данных
    // Для примера просто возвращаем объект напоминания
    const reminder: Reminder = {
      id: `reminder-${Date.now()}`,
      userId,
      itemType,
      itemId,
      reminderTime,
      sent: false,
    };
    
    console.log(`Reminder created for ${itemType} ${itemId} at ${reminderTime.toISOString()}`);
    
    return reminder;
  } catch (error) {
    console.error('Error creating reminder:', error);
    return null;
  }
}

// Функция для получения пользователя по ID
async function getUserById(userId: string) {
  try {
    // В реальном приложении здесь будет запрос к базе данных
    // Для примера возвращаем фиктивного пользователя
    return {
      id: userId,
      name: 'Пользователь',
      email: '<EMAIL>',
    };
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
}

// Функция для получения задачи по ID
async function getTaskById(taskId: string) {
  try {
    // В реальном приложении здесь будет запрос к базе данных
    // Для примера возвращаем фиктивную задачу
    return {
      id: taskId,
      title: 'Задача',
      description: 'Описание задачи',
      dueDate: new Date(),
      projectId: 'project-1',
    };
  } catch (error) {
    console.error('Error getting task:', error);
    return null;
  }
}

// Функция для получения подзадачи по ID
async function getSubtaskById(subtaskId: string) {
  try {
    // В реальном приложении здесь будет запрос к базе данных
    // Для примера возвращаем фиктивную подзадачу
    return {
      id: subtaskId,
      title: 'Подзадача',
      description: 'Описание подзадачи',
      dueDate: new Date(),
      taskId: 'task-1',
    };
  } catch (error) {
    console.error('Error getting subtask:', error);
    return null;
  }
}

// Функция для получения проекта по ID
async function getProjectById(projectId: string) {
  try {
    // В реальном приложении здесь будет запрос к базе данных
    // Для примера возвращаем фиктивный проект
    return {
      id: projectId,
      name: 'Проект',
      description: 'Описание проекта',
    };
  } catch (error) {
    console.error('Error getting project:', error);
    return null;
  }
}

// Функция для получения события по ID
async function getEventById(eventId: string) {
  try {
    // В реальном приложении здесь будет запрос к базе данных
    // Для примера возвращаем фиктивное событие
    return {
      id: eventId,
      title: 'Событие',
      description: 'Описание события',
      start: new Date(),
      end: new Date(),
      projectId: 'project-1',
    };
  } catch (error) {
    console.error('Error getting event:', error);
    return null;
  }
}

// Функция для отправки напоминания
export async function sendReminder(reminder: Reminder): Promise<boolean> {
  try {
    // Получаем пользователя
    const user = await getUserById(reminder.userId);
    if (!user || !user.email) {
      console.error(`User ${reminder.userId} not found or does not have an email`);
      return false;
    }
    
    // Формируем данные для напоминания в зависимости от типа элемента
    let reminderData: ReminderData | null = null;
    
    switch (reminder.itemType) {
      case 'task':
        const task = await getTaskById(reminder.itemId);
        if (!task) {
          console.error(`Task ${reminder.itemId} not found`);
          return false;
        }
        
        const taskProject = task.projectId ? await getProjectById(task.projectId) : null;
        
        reminderData = {
          userId: user.id,
          userEmail: user.email,
          userName: user.name || 'пользователь',
          subject: 'Напоминание о задаче',
          message: `У вас есть задача "${task.title}", которая должна быть выполнена к ${task.dueDate ? new Date(task.dueDate).toLocaleDateString('ru-RU') : 'указанному сроку'}.`,
          itemType: 'task',
          itemId: task.id,
          itemTitle: task.title,
          dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
          projectName: taskProject?.name,
        };
        break;
        
      case 'subtask':
        const subtask = await getSubtaskById(reminder.itemId);
        if (!subtask) {
          console.error(`Subtask ${reminder.itemId} not found`);
          return false;
        }
        
        reminderData = {
          userId: user.id,
          userEmail: user.email,
          userName: user.name || 'пользователь',
          subject: 'Напоминание о подзадаче',
          message: `У вас есть подзадача "${subtask.title}", которая должна быть выполнена к ${subtask.dueDate ? new Date(subtask.dueDate).toLocaleDateString('ru-RU') : 'указанному сроку'}.`,
          itemType: 'subtask',
          itemId: subtask.id,
          itemTitle: subtask.title,
          dueDate: subtask.dueDate ? new Date(subtask.dueDate) : undefined,
        };
        break;
        
      case 'project':
        const project = await getProjectById(reminder.itemId);
        if (!project) {
          console.error(`Project ${reminder.itemId} not found`);
          return false;
        }
        
        reminderData = {
          userId: user.id,
          userEmail: user.email,
          userName: user.name || 'пользователь',
          subject: 'Напоминание о проекте',
          message: `У вас есть проект "${project.name}", который требует вашего внимания.`,
          itemType: 'project',
          itemId: project.id,
          itemTitle: project.name,
          projectName: project.name,
        };
        break;
        
      case 'event':
        const event = await getEventById(reminder.itemId);
        if (!event) {
          console.error(`Event ${reminder.itemId} not found`);
          return false;
        }
        
        const eventProject = event.projectId ? await getProjectById(event.projectId) : null;
        
        reminderData = {
          userId: user.id,
          userEmail: user.email,
          userName: user.name || 'пользователь',
          subject: 'Напоминание о событии',
          message: `У вас запланировано событие "${event.title}" на ${new Date(event.start).toLocaleString('ru-RU')}.`,
          itemType: 'event',
          itemId: event.id,
          itemTitle: event.title,
          dueDate: new Date(event.start),
          projectName: eventProject?.name,
        };
        break;
        
      default:
        console.error(`Unknown item type: ${reminder.itemType}`);
        return false;
    }
    
    if (!reminderData) {
      console.error('Failed to create reminder data');
      return false;
    }
    
    // Отправляем напоминание по электронной почте
    const result = await sendReminderEmail(reminderData);
    
    if (result) {
      // Обновляем статус напоминания
      reminder.sent = true;
      reminder.sentAt = new Date();
      
      // В реальном приложении здесь будет обновление записи в базе данных
      console.log(`Reminder ${reminder.id} marked as sent at ${reminder.sentAt.toISOString()}`);
    }
    
    return result;
  } catch (error) {
    console.error('Error sending reminder:', error);
    return false;
  }
}

// Функция для проверки и отправки напоминаний
export async function checkAndSendReminders(): Promise<void> {
  try {
    // В реальном приложении здесь будет запрос к базе данных для получения напоминаний,
    // которые должны быть отправлены (reminderTime <= now && sent === false)
    const now = new Date();
    
    // Для примера создаем фиктивные напоминания
    const reminders: Reminder[] = [
      {
        id: 'reminder-1',
        userId: 'user-1',
        itemType: 'task',
        itemId: 'task-1',
        reminderTime: new Date(now.getTime() - 1000 * 60 * 5), // 5 минут назад
        sent: false,
      },
      {
        id: 'reminder-2',
        userId: 'user-2',
        itemType: 'event',
        itemId: 'event-1',
        reminderTime: new Date(now.getTime() - 1000 * 60 * 10), // 10 минут назад
        sent: false,
      },
    ];
    
    console.log(`Found ${reminders.length} reminders to send`);
    
    // Отправляем напоминания
    for (const reminder of reminders) {
      await sendReminder(reminder);
    }
  } catch (error) {
    console.error('Error checking and sending reminders:', error);
  }
}
