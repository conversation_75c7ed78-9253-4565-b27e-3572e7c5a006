// Функция для сохранения данных в localStorage
export function saveToLocalStorage(key: string, data: any): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.error(`Error saving to localStorage: ${error}`);
    }
  }
}

// Функция для получения данных из localStorage
export function getFromLocalStorage(key: string): any {
  if (typeof window !== 'undefined') {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error(`Error getting from localStorage: ${error}`);
      return null;
    }
  }
  return null;
}

// Функция для удаления данных из localStorage
export function removeFromLocalStorage(key: string): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing from localStorage: ${error}`);
    }
  }
}

// Функция для получения проектов из localStorage
export function getProjects() {
  return getFromLocalStorage('projects') || [];
}

// Функция для сохранения проектов в localStorage
export function saveProjects(projects: any[]) {
  saveToLocalStorage('projects', projects);
}
