// Simple test script to validate AI integration
// Run with: node test-ai-integration.js

const testAIEndpoints = async () => {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🧪 Testing AI Integration...\n');

  // Test 1: Settings API
  console.log('1. Testing Settings API...');
  try {
    const settingsResponse = await fetch(`${baseUrl}/api/settings/ai`);
    const settings = await settingsResponse.json();
    console.log('✅ Settings API working:', settings.enableAI ? 'AI Enabled' : 'AI Disabled');
  } catch (error) {
    console.log('❌ Settings API failed:', error.message);
  }

  // Test 2: General AI endpoint
  console.log('\n2. Testing General AI endpoint...');
  try {
    const aiResponse = await fetch(`${baseUrl}/api/ai`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: 'Analyze this project',
        context: { name: 'Test Project', description: 'A test project for AI integration' }
      })
    });
    const aiResult = await aiResponse.json();
    console.log('✅ General AI endpoint working');
    console.log('Response preview:', aiResult.response?.substring(0, 100) + '...');
  } catch (error) {
    console.log('❌ General AI endpoint failed:', error.message);
  }

  // Test 3: Task AI endpoint
  console.log('\n3. Testing Task AI endpoint...');
  try {
    const taskResponse = await fetch(`${baseUrl}/api/ai/tasks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: 'анализ задачи',
        context: {
          currentTask: { title: 'Implement AI features', description: 'Add AI functionality to the app' },
          tasks: []
        }
      })
    });
    const taskResult = await taskResponse.json();
    console.log('✅ Task AI endpoint working');
    console.log('Response preview:', taskResult.response?.substring(0, 100) + '...');
  } catch (error) {
    console.log('❌ Task AI endpoint failed:', error.message);
  }

  // Test 4: Subtasks AI endpoint
  console.log('\n4. Testing Subtasks AI endpoint...');
  try {
    const subtaskResponse = await fetch(`${baseUrl}/api/ai/subtasks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        task: { 
          id: 'test-task',
          title: 'Create user authentication system',
          description: 'Implement login, registration, and password reset functionality'
        },
        additionalInfo: 'Use JWT tokens and bcrypt for security',
        projectContext: { tasks: [] }
      })
    });
    const subtaskResult = await subtaskResponse.json();
    console.log('✅ Subtasks AI endpoint working');
    console.log('Generated subtasks:', subtaskResult.subtasks?.length || 0);
    if (subtaskResult.subtasks?.length > 0) {
      console.log('First subtask:', subtaskResult.subtasks[0].title);
    }
  } catch (error) {
    console.log('❌ Subtasks AI endpoint failed:', error.message);
  }

  // Test 5: Chat endpoint
  console.log('\n5. Testing Chat endpoint...');
  try {
    const chatResponse = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: 'Help me plan a software project' }
        ]
      })
    });
    
    if (chatResponse.ok) {
      console.log('✅ Chat endpoint working (streaming response)');
    } else {
      const error = await chatResponse.json();
      console.log('❌ Chat endpoint failed:', error.error);
    }
  } catch (error) {
    console.log('❌ Chat endpoint failed:', error.message);
  }

  console.log('\n🏁 AI Integration Test Complete!');
};

// Test error handling
const testErrorHandling = async () => {
  console.log('\n🔧 Testing Error Handling...\n');

  const baseUrl = 'http://localhost:3000';

  // Test with invalid API key scenario
  console.log('1. Testing with potentially invalid configuration...');
  try {
    const response = await fetch(`${baseUrl}/api/ai`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: 'test prompt',
        context: { name: 'test' }
      })
    });
    const result = await response.json();
    
    if (result.response?.includes('⚠️')) {
      console.log('✅ Fallback system working - detected fallback response');
    } else {
      console.log('✅ AI system working - got real AI response');
    }
  } catch (error) {
    console.log('❌ Error handling test failed:', error.message);
  }

  console.log('\n🛡️ Error Handling Test Complete!');
};

// Run tests
const runTests = async () => {
  try {
    await testAIEndpoints();
    await testErrorHandling();
  } catch (error) {
    console.error('Test runner failed:', error);
  }
};

// Check if running directly
if (require.main === module) {
  runTests();
}

module.exports = { testAIEndpoints, testErrorHandling };
