"use client";

// Интерфейс сообщения
export interface ChatMessage {
  id: string | number;
  senderId: string;
  senderName?: string;
  senderAvatar?: string;
  text: string;
  timestamp: number | string | Date;
  roomId?: string;
  read?: boolean;
}

// Ключи для хранения данных в localStorage
const CHAT_MESSAGES_KEY = 'chatMessages';
const CHAT_VISIBILITY_KEY = 'chatVisibility';

// Функция для получения всех сообщений из localStorage
export const getAllMessages = (): Record<string, ChatMessage[]> => {
  try {
    if (typeof window !== 'undefined') {
      const savedMessages = localStorage.getItem(CHAT_MESSAGES_KEY);
      return savedMessages ? JSON.parse(savedMessages) : {};
    }
    return {};
  } catch (error) {
    console.error('Error loading messages from localStorage:', error);
    return {};
  }
};

// Функция для удаления дубликатов сообщений
const removeDuplicateMessages = (messages: ChatMessage[]): ChatMessage[] => {
  // Создаем мап для хранения уникальных сообщений по ID
  const uniqueMessages = new Map<string | number, ChatMessage>();

  // Добавляем сообщения в мап, перезаписывая дубликаты
  messages.forEach(message => {
    uniqueMessages.set(message.id, message);
  });

  // Возвращаем массив уникальных сообщений, сохраняя порядок
  return Array.from(uniqueMessages.values()).sort((a, b) => {
    const timeA = new Date(a.timestamp).getTime();
    const timeB = new Date(b.timestamp).getTime();
    return timeA - timeB; // Сортируем по времени в порядке возрастания
  });
};

// Функция для получения сообщений конкретного чата
export const getChatMessages = (chatId: string): ChatMessage[] => {
  try {
    const allMessages = getAllMessages();
    const messages = allMessages[chatId] || [];

    // Удаляем дубликаты и сохраняем обратно, если они были найдены
    const uniqueMessages = removeDuplicateMessages(messages);

    // Если количество сообщений изменилось, сохраняем обновленный список
    if (uniqueMessages.length !== messages.length) {
      saveChatMessages(chatId, uniqueMessages);
    }

    return uniqueMessages;
  } catch (error) {
    console.error(`Error loading messages for chat ${chatId}:`, error);
    return [];
  }
};

// Функция для проверки наличия дубликата сообщения
const isDuplicateMessage = (messages: ChatMessage[], newMessage: ChatMessage): boolean => {
  // Проверяем по ID сообщения
  return messages.some(msg => msg.id === newMessage.id);
};

// Функция для сохранения сообщения
export const saveMessage = (chatId: string, message: ChatMessage): boolean => {
  try {
    if (typeof window !== 'undefined') {
      // Загружаем текущие сообщения
      const allMessages = getAllMessages();

      // Создаем массив для чата, если его еще нет
      if (!allMessages[chatId]) {
        allMessages[chatId] = [];
      }

      // Проверяем, есть ли уже такое сообщение
      if (!isDuplicateMessage(allMessages[chatId], message)) {
        // Добавляем сообщение, только если его еще нет
        allMessages[chatId].push(message);

        // Сохраняем обновленные сообщения
        localStorage.setItem(CHAT_MESSAGES_KEY, JSON.stringify(allMessages));
      }

      return true;
    }
    return false;
  } catch (error) {
    console.error(`Error saving message for chat ${chatId}:`, error);
    return false;
  }
};

// Функция для сохранения нескольких сообщений
export const saveChatMessages = (chatId: string, messages: ChatMessage[]): boolean => {
  try {
    if (typeof window !== 'undefined') {
      // Загружаем текущие сообщения
      const allMessages = getAllMessages();

      // Удаляем дубликаты из новых сообщений
      const uniqueMessages = messages.filter((message, index, self) =>
        index === self.findIndex(m => m.id === message.id)
      );

      // Обновляем сообщения для чата
      allMessages[chatId] = uniqueMessages;

      // Сохраняем обновленные сообщения
      localStorage.setItem(CHAT_MESSAGES_KEY, JSON.stringify(allMessages));

      return true;
    }
    return false;
  } catch (error) {
    console.error(`Error saving messages for chat ${chatId}:`, error);
    return false;
  }
};

// Функция для пометки сообщений как прочитанных
export const markMessagesAsRead = (chatId: string): boolean => {
  try {
    if (typeof window !== 'undefined') {
      // Загружаем текущие сообщения
      const allMessages = getAllMessages();

      // Если нет сообщений для этого чата, ничего не делаем
      if (!allMessages[chatId]) {
        return true;
      }

      // Помечаем все сообщения как прочитанные
      allMessages[chatId] = allMessages[chatId].map(message => ({
        ...message,
        read: true
      }));

      // Сохраняем обновленные сообщения
      localStorage.setItem(CHAT_MESSAGES_KEY, JSON.stringify(allMessages));

      return true;
    }
    return false;
  } catch (error) {
    console.error(`Error marking messages as read for chat ${chatId}:`, error);
    return false;
  }
};

// Функция для получения количества непрочитанных сообщений
export const getUnreadCount = (chatId: string): number => {
  try {
    const messages = getChatMessages(chatId);
    return messages.filter(message => !message.read).length;
  } catch (error) {
    console.error(`Error getting unread count for chat ${chatId}:`, error);
    return 0;
  }
};

// Функция для удаления всех сообщений чата
export const clearChatMessages = (chatId: string): boolean => {
  try {
    if (typeof window !== 'undefined') {
      // Загружаем текущие сообщения
      const allMessages = getAllMessages();

      // Удаляем сообщения для чата
      delete allMessages[chatId];

      // Сохраняем обновленные сообщения
      localStorage.setItem(CHAT_MESSAGES_KEY, JSON.stringify(allMessages));

      return true;
    }
    return false;
  } catch (error) {
    console.error(`Error clearing messages for chat ${chatId}:`, error);
    return false;
  }
};
