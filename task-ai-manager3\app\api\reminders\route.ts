"use server";

import { NextRequest, NextResponse } from 'next/server';
import { checkAndSendReminders } from '@/lib/reminder-service';
import { processCalendarReminders } from '@/lib/calendar-reminder-service';
import { processTaskReminders } from '@/lib/task-reminder-service';
import { checkAndSendAllReminders } from '@/lib/deadline-reminder-service';
import {
  processReminderQueue,
  createRemindersForItem,
  getUserReminderPreferences,
  saveUserReminderPreferences,
  getNotificationHistory
} from '@/lib/enhanced-reminder-service';
import {
  startReminderScheduler,
  stopReminderScheduler,
  isReminderSchedulerRunning,
  manualReminderCheck
} from '@/lib/reminder-scheduler';
// import { prisma } from '@/lib/prisma'; // Закомментировано до настройки БД

// Enhanced GET handler for reminder management
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const action = searchParams.get('action');

    switch (action) {
      case 'status':
        // Get scheduler status
        return NextResponse.json({
          success: true,
          data: {
            schedulerRunning: isReminderSchedulerRunning(),
            lastCheck: new Date().toISOString(),
          }
        });

      case 'preferences':
        if (!userId) {
          return NextResponse.json(
            { success: false, message: 'userId is required for preferences' },
            { status: 400 }
          );
        }
        const preferences = getUserReminderPreferences(userId);
        return NextResponse.json({
          success: true,
          data: { preferences }
        });

      case 'history':
        if (!userId) {
          return NextResponse.json(
            { success: false, message: 'userId is required for history' },
            { status: 400 }
          );
        }
        const limit = parseInt(searchParams.get('limit') || '50');
        const history = getNotificationHistory(userId, limit);
        return NextResponse.json({
          success: true,
          data: { history }
        });

      case 'check-queue':
        // Process reminder queue manually
        await processReminderQueue();
        return NextResponse.json({
          success: true,
          message: 'Reminder queue processed'
        });

      case 'check-all':
        // Check and send all reminders (legacy)
        await checkAndSendAllReminders();
        return NextResponse.json({
          success: true,
          message: 'All reminders checked and sent'
        });

      case 'manual-check':
        // Manual reminder check
        await manualReminderCheck();
        return NextResponse.json({
          success: true,
          message: 'Manual reminder check completed'
        });

      default:
        // Default: check and send reminders
        await checkAndSendReminders();
        return NextResponse.json({
          success: true,
          message: 'Reminders checked and sent'
        });
    }
  } catch (error) {
    console.error('Error in reminders API:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process reminder request', error: String(error) },
      { status: 500 }
    );
  }
}

// Enhanced POST handler for creating reminders and managing preferences
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'create-reminder':
        // Create reminder for specific item
        const { userId, itemType, itemId, dueDate, priority } = body;

        if (!userId || !itemType || !itemId || !dueDate) {
          return NextResponse.json(
            { success: false, message: 'Missing required fields: userId, itemType, itemId, dueDate' },
            { status: 400 }
          );
        }

        const reminders = await createRemindersForItem(
          userId,
          itemType,
          itemId,
          new Date(dueDate),
          priority || 'medium'
        );

        return NextResponse.json({
          success: true,
          message: 'Reminders created successfully',
          data: { reminders }
        });

      case 'save-preferences':
        // Save user reminder preferences
        const { preferences } = body;

        if (!preferences || !preferences.userId) {
          return NextResponse.json(
            { success: false, message: 'Missing preferences or userId' },
            { status: 400 }
          );
        }

        saveUserReminderPreferences(preferences);

        return NextResponse.json({
          success: true,
          message: 'Preferences saved successfully'
        });

      case 'start-scheduler':
        // Start reminder scheduler
        startReminderScheduler();
        return NextResponse.json({
          success: true,
          message: 'Reminder scheduler started'
        });

      case 'stop-scheduler':
        // Stop reminder scheduler
        stopReminderScheduler();
        return NextResponse.json({
          success: true,
          message: 'Reminder scheduler stopped'
        });

      case 'calendar-reminders':
        // Legacy: Process calendar reminders
        if (!body.userId || !body.events) {
          return NextResponse.json(
            { success: false, message: 'Missing required fields: userId or events' },
            { status: 400 }
          );
        }

        await processCalendarReminders(body.events, body.userId);
        return NextResponse.json({
          success: true,
          message: 'Calendar reminders processed'
        });

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action specified' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in reminders POST API:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process reminder request', error: String(error) },
      { status: 500 }
    );
  }
}

// Обработчик PUT-запроса для создания напоминаний для задач
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    // Проверяем наличие необходимых данных
    if (!body.tasks) {
      return NextResponse.json(
        { success: false, message: 'Missing required field: tasks' },
        { status: 400 }
      );
    }

    // Обрабатываем напоминания для задач
    await processTaskReminders(body.tasks);

    return NextResponse.json({ success: true, message: 'Task reminders processed' });
  } catch (error) {
    console.error('Error in task reminders API:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process task reminders', error: String(error) },
      { status: 500 }
    );
  }
}
