"use client"

import { createContext, useContext, useState, ReactNode } from "react"

// Define the available languages
const languages = {
  en: {
    // General
    dashboard: "Dashboard",
    projects: "Projects",
    analytics: "Analytics",
    settings: "Settings",
    tasks: "Tasks",
    documents: "Documents",
    save: "Save",
    cancel: "Cancel",
    calendar: "Calendar",

    // Tasks
    toDo: "To Do",
    inProgress: "In Progress",
    done: "Done",
    addTask: "Add Task",
    deleteTask: "Delete Task",
    editTask: "Edit Task",
    taskTitle: "Task Title",
    taskTitlePlaceholder: "Enter task title",
    taskDescription: "Task Description",
    taskDescriptionPlaceholder: "Enter task description",
    taskPriority: "Priority",
    high: "High",
    medium: "Medium",
    low: "Low",
    dueDate: "Due Date",
    selectPriority: "Select priority",
    taskAge: "Task Age",
    daysLeft: " days left",
    daysOverdue: " days overdue",
    taskUpdated: "Task Updated",
    taskUpdatedSuccess: "Task has been successfully updated",
    taskDeleted: "Task Deleted",
    taskDeletedSuccess: "Task has been successfully deleted",
    taskCreated: "Task Created",
    taskCreatedSuccess: "Task has been successfully created",
    deleteTaskConfirmation: "Are you sure you want to delete task {title}?",
    delete: "Delete",
    close: "Close",
    editTaskDescription: "Edit the task details",
    viewMode: "View mode",
    kanban: "Kanban",
    list: "List",
    searchTasks: "Search tasks",
    priority: "Priority",
    allPriorities: "All priorities",
    assignee: "Assignee",
    allAssignees: "All assignees",
    noTasksFound: "No tasks found",
    add: "Add",
    commaSeparatedTags: "Comma separated tags",

    // Subtasks
    manageSubtasks: "Manage Subtasks",
    manageSubtasksDescription: "Add or manage subtasks for this task",
    subtasks: "Subtasks",
    newSubtask: "New Subtask",
    newSubtaskPlaceholder: "Enter subtask name",
    extractSubtasks: "Extract as Subtasks",
    noSubtasksFound: "No Subtasks Found",
    noSubtasksFoundDescription: "Could not extract subtasks from AI response",
    subtasksAdded: "Subtasks Added",
    subtasksAddedFromAI: "Added {count} subtasks from AI suggestion",
    generateSubtasksWithAI: "Generate Subtasks with AI",

    // AI Assistant
    aiAssist: "AI Assistant",
    aiAssistDescription: "Get AI help with this task",
    aiPrompt: "Ask AI",
    aiPromptPlaceholder: "e.g. Help me break down this task",
    aiPromptExample1: "Break down this task",
    aiPromptExample2: "Suggest a priority",
    aiPromptExample3: "Recommend next steps",
    aiResponse: "AI Response",
    generateResponse: "Generate",
    thinking: "Thinking...",
    currentTask: "Current Task",

    // Projects
    projectProgress: "Project Progress",
    createProject: "Create Project",
    editProject: "Edit Project",
    deleteProject: "Delete Project",
    projectName: "Project Name",
    projectDescription: "Project Description",
    selectProject: "Select Project",
    newProject: "New Project",
    projectCreated: "Project Created",
    projectCreatedDescription: "Project has been created successfully",
    projectUpdated: "Project Updated",
    projectUpdatedDescription: "Project has been updated successfully",
    projectDeleted: "Project Deleted",
    projectDeletedDescription: "Project has been deleted successfully",
    deleteProjectConfirmation: "Are you sure you want to delete project {name}?",
    projectCompletion: "Project Completion",
    aiProjectAssistant: "AI Project Assistant",
    askAI: "Ask AI",
    applyAISuggestions: "Apply AI Suggestions",
    aiSuggestionsApplied: "AI Suggestions Applied",
    projectUpdatedWithAI: "Project has been updated with AI suggestions",
    privateProject: "Private Project",
    privateProjectDescription: "Private projects are only visible to you and people you share them with",
    private: "Private",
    shared: "Shared",
    shareProject: "Share Project",
    currentParticipants: "Current Participants",
    addParticipants: "Add Participants",
    searchTeamMembers: "Search team members",
    noResultsFound: "No results found",
    selectedParticipants: "Selected Participants",
    share: "Share",
    projectShared: "Project Shared",
    projectSharedDescription: "Project has been shared with {count} participants",
    participantRemoved: "Participant Removed",
    participantRemovedDescription: "Participant has been removed from the project",
    totalProjects: "Total Projects",
    completedProjects: "Completed Projects",
    inProgressProjects: "In Progress",
    averageCompletion: "Average Completion",
    noProjects: "No Projects",
    createFirstProject: "Create your first project to get started with task management",
    participants: "Participants",
    noParticipants: "No participants",
    actions: "Actions",
    completion: "Completion",
    createdAt: "Created At",

    // Documents
    projectDocuments: "Project Documents",
    uploadDocument: "Upload Document",
    addDocumentLink: "Add Document Link",
    viewDocuments: "View Documents",
    noDocuments: "No documents for this project",
    documentName: "Document Name",
    documentType: "Document Type",
    documentURL: "Document URL",
    addDocument: "Add Document",
    addDocumentDescription: "Add a document link to the current project",
    documentUploaded: "Document Uploaded",
    documentUploadedSuccess: "Document {name} has been uploaded successfully",
    documentAdded: "Document Added",
    documentAddedSuccess: "Document {name} has been added successfully",
    editDocument: "Edit Document",
    deleteDocument: "Delete Document",
    deleteDocumentConfirmation: "Are you sure you want to delete document {name}?",
    documentDeleted: "Document Deleted",
    documentDeletedSuccessfully: "Document has been deleted successfully",
    documentUpdated: "Document Updated",
    documentUpdatedSuccessfully: "Document {name} has been updated successfully",
    folders: "Folders",
    files: "Files",
    newFolder: "New Folder",
    uploadFiles: "Upload Files",
    uploadFilesDescription: "Upload files to the current project",
    dropFilesHere: "Drop files here",
    or: "or",
    browseFiles: "Browse Files",
    selectedFiles: "Selected Files",
    uploading: "Uploading...",
    upload: "Upload",
    uploadComplete: "Upload Complete",
    filesUploadedSuccessfully: "{count} files uploaded successfully",
    createdBy: "Created by",
    lastModified: "Last modified",
    size: "Size",
    modified: "Modified",
    folder: "Folder",
    noFolder: "No folder",
    tags: "Tags",
    content: "Content",
    aiDocumentAssistant: "AI Document Assistant",
    aiDocumentPromptPlaceholder: "e.g. Improve this document, Summarize, Suggest tags",
    aiDocumentPromptSuggestion1: "Improve this document",
    aiDocumentPromptSuggestion2: "Summarize content",
    aiDocumentPromptSuggestion3: "Suggest tags",
    currentDocument: "Current Document",
    documentUpdatedWithAI: "Document has been updated with AI suggestions",
    documentAIUpdated: "Document {name} has been updated with AI suggestions",
    openMenu: "Open Menu",
    view: "View",
    edit: "Edit",
    download: "Download",
    totalDocuments: "Total Documents",
    totalSize: "Total Size",
    latestUpdate: "Latest Update",
    documentTypes: "Document Types",
    groupByType: "Group by Type",
    version: "Version",
    status: "Status",
    description: "Description",

    // Admin
    adminDashboard: "Admin Dashboard",
    userManagement: "User Management",
    blockUser: "Block User",
    unblockUser: "Unblock User",
    deleteUser: "Delete User",
    confirmDelete: "Are you sure you want to delete this user?",
    confirmBlock: "Are you sure you want to block this user?",
    confirmUnblock: "Are you sure you want to unblock this user?",
    username: "Username",
    email: "Email",
    active: "Active",
    blocked: "Blocked",

    // Settings
    settingsSaved: "Settings Saved",
    generalSettingsSaved: "General settings have been saved successfully",
    aiSettingsSaved: "AI settings have been saved successfully",
    emailSettingsSaved: "Email settings have been saved successfully",
    aiModelApiKey: "AI Model API Key", // Added
    enterApiKeyPlaceholder: "Enter your API key", // Added
    // Admin Settings specific keys
    generalSettings: "General Settings",
    aiSettings: "AI Settings",
    emailSettings: "Email Settings",
    generalSettingsDescription: "Configure general site settings",
    aiSettingsDescription: "Configure AI assistant settings",
    emailSettingsDescription: "Configure email notification settings",
    siteName: "Site Name",
    siteDescription: "Site Description",
    allowRegistration: "Allow User Registration",
    requireEmailVerification: "Require Email Verification",
    maxProjectsPerUser: "Max Projects Per User",
    maxTasksPerProject: "Max Tasks Per Project",
    saveSettings: "Save Settings", // Might be context specific, using generic translation
    enableAI: "Enable AI Assistant",
    aiModel: "AI Model",
    aiTemperature: "AI Temperature",
    maxTokens: "Max Tokens",
    customPrompt: "Custom System Prompt",
    aiWarning: "AI Usage Warning",
    aiWarningDescription: "Inform users about potential AI inaccuracies or costs",
    enableEmailNotifications: "Enable Email Notifications",
    smtpServer: "SMTP Server",
    smtpPort: "SMTP Port",
    smtpUsername: "SMTP Username",
    smtpPassword: "SMTP Password",
    fromEmail: "From Email Address",
    systemSettings: "System Settings",
    // Admin Dashboard specific keys
    adminDashboardDescription: "Overview of system statistics and management",
    totalUsers: "Total Users",
    activeUsers: "Active Users",
    totalTasks: "Total Tasks",
    completedTasks: "Completed Tasks",
    completionRate: "Completion Rate",
    taskCompletionRate: "Task Completion Rate",
    // Dashboard specific keys
    logoutSuccessful: "Logout Successful",
    youHaveBeenLoggedOut: "You have been successfully logged out.",
    // Analytics specific keys
    taskDistribution: "Task Distribution",
    projectStatistics: "Project Statistics",
    tasksByStatus: "Tasks by Status",
    tasksByStatusDescription: "Distribution of tasks across different statuses",
    taskCompletion: "Task Completion Over Time",
    taskCompletionDescription: "Trend of task completion",
    completed: "Completed", // Context specific for analytics
    projectsByStatus: "Projects by Status",
    projectsByStatusDescription: "Distribution of projects across different statuses",
    onHold: "On Hold",
    monthlyProjects: "Monthly Project Creation",
    monthlyProjectsDescription: "Trend of new projects created each month",

    // User Analytics specific keys
    userParticipation: "User Participation",
    userActivity: "User Activity",
    projectCollaboration: "Project Collaboration",
    topUsersByProjects: "Top Users by Projects",
    topUsersByProjectsDescription: "Users with the most projects",
    projectOwnershipDistribution: "Project Ownership Distribution",
    projectOwnershipDistributionDescription: "Who owns the most projects",
    topUsersByTasks: "Top Users by Tasks",
    topUsersByTasksDescription: "Users with the most tasks",
    taskCompletionByUser: "Task Completion by User",
    taskCompletionByUserDescription: "Who completes the most tasks",
    projectsByTeamSize: "Projects by Team Size",
    projectsByTeamSizeDescription: "Distribution of projects by number of participants",
    collaborationDistribution: "Collaboration Distribution",
    collaborationDistributionDescription: "How projects are distributed by team size",
    totalProjects: "Total Projects",
    ownedProjects: "Owned Projects",
    participatedProjects: "Participated Projects",
    completedTasks: "Completed Tasks",
    inProgressTasks: "In Progress Tasks",
    todoTasks: "To Do Tasks",
    singleUserProjects: "Single User Projects",
    multiUserProjects: "{count} User Projects",
    // Sidebar specific keys
    aiAssistant: "AI Assistant", // Duplicated, ensure consistency
    aiTaskTracker: "AI Task Tracker",

    // Project timeline keys
    projectAge: "Project Age",
    daysLeft: "Days Left",
    days: "days",
    daysOverdue: "days overdue",
    noDueDate: "No due date",
    noChanges: "No Changes",
    allParticipantsAlreadyAdded: "All selected participants are already in the project",

    // AI Assistant
    error: "Error",
    aiRequestFailed: "Failed to process AI request. Please try again.",

    // Other
    all: "All",
    spreadsheets: "Spreadsheets",
    useMainMenu: "Use the sidebar menu for navigation",
    pdfs: "PDFs",
    other: "Other",
    noDocumentsFound: "No documents found",
    noDocumentsMatchingSearch: "No documents matching search '{query}'",
    noDocumentsInProject: "No documents in this project",
    noProjectSelected: "No project selected",
    selectProjectToViewDocuments: "Select a project to view its documents",
    selectProjectToManageDocuments: "Select a project to manage its documents",
    documentsForProject: "Documents for project: {name}",
    searchDocuments: "Search documents",

    // Language Toggle
    toggleLanguage: "Toggle language",

    // Delete Confirmation Dialog
    confirmDelete: "Confirm Delete",
    confirmDeleteDescription: "Are you sure you want to delete this item? This action cannot be undone.",
    confirmDeleteEvent: "Are you sure you want to delete this event? This action cannot be undone.",

    // Event Dialog
    eventTitle: "Event Title",
    eventTitlePlaceholder: "Enter event title",
    eventType: "Event Type",
    selectEventType: "Select type",
    eventTask: "Task",
    eventSubtask: "Subtask",
    eventMeeting: "Meeting",
    eventReminder: "Reminder",
    eventPersonal: "Personal",
    selectEventPriority: "Select priority",
    eventStatus: "Status",
    selectEventStatus: "Select status",
    eventActive: "Active",
    eventCompleted: "Completed",
    eventProject: "Project",
    selectEventProject: "Select project",
    noProject: "No project",
    eventDescription: "Event Description",
    eventDescriptionPlaceholder: "Enter event description",
    startDate: "Start Date and Time",
    endDate: "End Date and Time",

    // Admin Panel
    adminPanel: "Admin Panel",
    userManagement: "User Management",
    systemSettings: "System Settings",
    reminderStatus: "Reminder Status",

    // User Management
    searchUsers: "Search users",
    addUser: "Add User",
    addNewUser: "Add New User",
    addNewUserDescription: "Create a new user account",
    editUser: "Edit User",
    editUserDescription: "Edit user information",
    deleteUser: "Delete User",
    blockUser: "Block User",
    unblockUser: "Unblock User",
    confirmBlock: "Are you sure you want to block this user?",
    confirmUnblock: "Are you sure you want to unblock this user?",
    selectRole: "Select role",
    selectStatus: "Select status",
    active: "Active",
    blocked: "Blocked",
    admin: "Admin",
    user: "User",
    noUsersFound: "No users found",
    actions: "Actions",

    // Notification Preferences
    emailNotifications: "Email Notifications",
    emailNotificationsDescription: "Configure email reminder settings and preferences",
    enableEmailReminders: "Enable Email Reminders",
    emailRemindersDescription: "Receive task and project reminders via email",
    emailAddressForReminders: "Email Address for Reminders",
    emailPlaceholder: "<EMAIL>",
    telegramNotifications: "Telegram Notifications",
    telegramNotificationsDescription: "Set up Telegram bot integration for instant notifications",
    enableTelegramReminders: "Enable Telegram Reminders",
    telegramRemindersDescription: "Receive notifications through Telegram bot",
    telegramChatId: "Telegram Chat ID",
    telegramChatIdPlaceholder: "123456789 or @username",
    test: "Test",
    testSuccessful: "Test Successful",
    testSuccessfulDescription: "Test message sent to Telegram successfully!",
    testFailed: "Test Failed",
    testFailedDescription: "Failed to send test message. Please check your chat ID and try again.",
    missingChatId: "Missing Chat ID",
    missingChatIdDescription: "Please enter your Telegram chat ID first.",
    invalidTime: "Invalid Time",
    invalidTimeDescription: "Please enter a valid positive number.",
    duplicateTime: "Duplicate Time",
    duplicateTimeDescription: "This reminder time already exists.",
    settingsSaved: "Settings Saved",
    settingsSavedDescription: "Your notification preferences have been updated successfully.",
    errorSaving: "Error",
    errorSavingDescription: "Failed to save notification preferences. Please try again.",
    reminderTiming: "Reminder Timing",
    reminderTimingDescription: "Configure when you want to receive reminders before deadlines",
    currentReminderTimes: "Current Reminder Times",
    noReminderTimes: "No reminder times configured",
    beforeDeadline: "before",
    minutes: "Minutes",
    hours: "Hours",
    days: "Days",
    notificationTypes: "Notification Types",
    notificationTypesDescription: "Choose which types of items you want to receive reminders for",
    taskReminders: "Task Reminders",
    projectReminders: "Project Reminders",
    calendarReminders: "Calendar Reminders",
    overdueNotifications: "Overdue Notifications",
    quietHours: "Quiet Hours",
    quietHoursDescription: "Set times when you don't want to receive notifications",
    enableQuietHours: "Enable Quiet Hours",
    quietHoursInfo: "Notifications will be delayed until quiet hours end",
    startTime: "Start Time",
    endTime: "End Time",
    saveChanges: "Save Changes",
    saved: "Saved",

    // Telegram Setup Instructions
    telegramSetupTitle: "How to get your Chat ID:",
    telegramSetupStep1: "1. Start a chat with @userinfobot on Telegram",
    telegramSetupStep2: "2. Send any message to get your Chat ID",
    telegramSetupStep3: "3. Copy the number and paste it above",

    // Reminder Status
    reminderScheduler: "Reminder Scheduler",
    schedulerStatus: "Scheduler Status",
    running: "Running",
    stopped: "Stopped",
    checkInterval: "Check Interval",
    minutesShort: "min",
    lastCheck: "Last Check",
    never: "Never",
    startScheduler: "Start Scheduler",
    stopScheduler: "Stop Scheduler",
    manualCheck: "Manual Check",
    updateInterval: "Update Interval",
    enabled: "Enabled",
    disabled: "Disabled",
    users: "Users",

    // Test Suite
    reminderSystemTestSuite: "Reminder System Test Suite",
    testSystemFunctionality: "Test the reminder and notification system functionality",
    runAllTests: "Run All Tests",
    runningTests: "Running Tests...",
    reset: "Reset",
    testsReset: "Tests Reset",
    testsResetDescription: "Test results have been cleared",

    // Reminder Status Messages
    schedulerStarted: "Scheduler Started",
    schedulerStartedDescription: "Reminders will be checked every {minutes} minutes",
    schedulerStopped: "Scheduler Stopped",
    schedulerStoppedDescription: "Automatic reminder checking is disabled",
    checkCompleted: "Check Completed",
    checkCompletedDescription: "Reminders checked and sent",
    checkFailedDescription: "Failed to check reminders",
    intervalUpdated: "Interval Updated",
    intervalUpdatedDescription: "Check interval changed to {minutes} minutes",
    apiWorking: "API Working",
    apiWorkingDescription: "Test request to reminder API completed successfully",
    apiError: "API Error",
    connectionError: "Connection Error",
    connectionErrorDescription: "Failed to connect to reminder API",
    unknownError: "Unknown error",

    // Settings Page
    theme: "Theme",
    themeDescription: "Choose your preferred color theme",
    lightDefault: "Light (Default)",
    language: "Language",
    languageDescription: "Select your preferred language",
    english: "English",

    // Header
    myAccount: "My Account",
    profile: "Profile",
    headerSettings: "Settings", // Renamed key
    adminPanel: "Admin Panel",
    adminRole: "Administrator",
    userRole: "User",
    logout: "Logout",
    notifications: "Notifications",
    clearAll: "Clear All",
    noNotifications: "No notifications",
    role: "Role",
    name: "Name",
    editProfile: "Edit Profile",
    appearance: "Appearance",
    theme: "Theme",
    language: "Language",
    light: "Light",
    dark: "Dark",
    marketingEmails: "Marketing Emails",
    taskNotifications: "Task Notifications",
    systemNotifications: "System Notifications",
    projectNotifications: "Project Notifications",
    reminderSettings: "Reminder Settings",
    emailReminders: "Email Reminders",
    telegramReminders: "Telegram Reminders",
    reminders: "Reminders",
    emailRemindersDescription: "Receive reminders via email",
    telegramRemindersDescription: "Receive reminders via Telegram",
    emailForReminders: "Email for reminders",
    telegramUsername: "Telegram username",
    emailFromProfileUsed: "Email from your profile is used",
    reminderSettingsSaved: "Reminder settings have been updated",
    saveChanges: "Save Changes",
    settingsUpdatedSuccess: "Settings have been updated successfully",

    // Project Management Specific
    validationError: "Validation Error",
    projectNameRequired: "Project name is required.",
    selectProjectToUseQuickActions: "Select a project to use quick actions.",
    error: "Error",
    cannotNavigateWithoutProject: "Cannot navigate to tasks without a selected project.",
    createNewProject: "Create New Project",
    projectNamePlaceholder: "Enter project name",
    projectDescriptionPlaceholder: "Enter project description",
    selectAssignee: "Select assignee",
    unassigned: "Unassigned",
    selectDate: "Select date",
    create: "Create",
    viewTasks: "View Tasks",
    discussProject: "Discuss Project",
    discussTask: "Discuss Task",
    projectDetails: "Project Details",
    // status: "Status", // Removed duplicate - already exists under // Documents
    created: "Created", // Already exists, ensure consistency if needed
    notSet: "Not Set",
    privacy: "Privacy",
    public: "Public",
    todo: "To Do", // Already exists, ensure consistency if needed
    quickActions: "Quick Actions",
    viewCalendar: "View Calendar",
    team: "Team",
    teamMembers: "Team Members",
    addMember: "Add Member",
    improveWithAI: "Improve with AI",
    currentDescription: "Current Description",
    noDescriptionYet: "No description added yet.",
    generating: "Generating...",
    generateImprovedDescription: "Generate Improved Description",
    aiSuggestion: "AI Suggestion",
    aiSuggestionApplied: "AI Suggestion Applied",
    descriptionUpdatedWithAI: "Description updated with AI suggestion.",
    applyAISuggestion: "Apply AI Suggestion",
    aiProjectPromptPlaceholder: "e.g. Ask about project improvements, task suggestions, etc." // Added missing key
  },
  ru: {
    // General
    dashboard: "Панель управления",
    projects: "Проекты",
    analytics: "Аналитика",
    settings: "Настройки",
    tasks: "Задачи",
    documents: "Документы",
    save: "Сохранить",
    cancel: "Отмена",
    calendar: "Календарь",

    // Tasks
    toDo: "К выполнению",
    inProgress: "В процессе",
    done: "Выполнено",
    addTask: "Добавить задачу",
    deleteTask: "Удалить задачу",
    editTask: "Редактировать задачу",
    taskTitle: "Название задачи",
    taskTitlePlaceholder: "Введите название задачи",
    taskDescription: "Описание задачи",
    taskDescriptionPlaceholder: "Введите описание задачи",
    taskPriority: "Приоритет",
    high: "Высокий",
    medium: "Средний",
    low: "Низкий",
    dueDate: "Срок выполнения",
    selectPriority: "Выберите приоритет",
    taskAge: "Возраст задачи",
    daysLeft: " дней осталось",
    daysOverdue: " дней просрочено",
    taskUpdated: "Задача обновлена",
    taskUpdatedSuccess: "Задача успешно обновлена",
    taskDeleted: "Задача удалена",
    taskDeletedSuccess: "Задача успешно удалена",
    taskCreated: "Задача создана",
    taskCreatedSuccess: "Задача успешно создана",
    deleteTaskConfirmation: "Вы уверены, что хотите удалить задачу {title}?",
    delete: "Удалить",
    close: "Закрыть",
    editTaskDescription: "Редактирование деталей задачи",
    viewMode: "Режим просмотра",
    kanban: "Канбан",
    list: "Список",
    searchTasks: "Поиск задач",
    priority: "Приоритет",
    allPriorities: "Все приоритеты",
    assignee: "Исполнитель",
    allAssignees: "Все исполнители",
    noTasksFound: "Задачи не найдены",
    add: "Добавить",
    commaSeparatedTags: "Теги, разделенные запятыми",

    // Subtasks
    manageSubtasks: "Управление подзадачами",
    manageSubtasksDescription: "Добавление или управление подзадачами",
    subtasks: "Подзадачи",
    newSubtask: "Новая подзадача",
    newSubtaskPlaceholder: "Введите название подзадачи",
    extractSubtasks: "Извлечь как подзадачи",
    noSubtasksFound: "Подзадачи не найдены",
    noSubtasksFoundDescription: "Не удалось извлечь подзадачи из ответа ИИ",
    subtasksAdded: "Подзадачи добавлены",
    subtasksAddedFromAI: "Добавлено {count} подзадач из предложения ИИ",
    generateSubtasksWithAI: "Создать подзадачи с помощью ИИ",

    // AI Assistant
    aiAssist: "ИИ-помощник",
    aiAssistDescription: "Получить помощь ИИ для этой задачи",
    aiPrompt: "Спросить ИИ",
    aiPromptPlaceholder: "например: Помоги разбить эту задачу на подзадачи",
    aiPromptExample1: "Разбей эту задачу",
    aiPromptExample2: "Предложи приоритет",
    aiPromptExample3: "Рекомендуй следующие шаги",
    aiResponse: "Ответ ИИ",
    generateResponse: "Сгенерировать",
    thinking: "Думаю...",
    currentTask: "Текущая задача",

    // Projects
    projectProgress: "Прогресс проекта",
    createProject: "Создать проект",
    editProject: "Редактировать проект",
    deleteProject: "Удалить проект",
    projectName: "Название проекта",
    projectDescription: "Описание проекта",
    selectProject: "Выбрать проект",
    newProject: "Новый проект",
    projectCreated: "Проект создан",
    projectCreatedDescription: "Проект успешно создан",
    projectUpdated: "Проект обновлен",
    projectUpdatedDescription: "Проект успешно обновлен",
    projectDeleted: "Проект удален",
    projectDeletedDescription: "Проект успешно удален",
    deleteProjectConfirmation: "Вы уверены, что хотите удалить проект {name}?",
    projectCompletion: "Завершенность проекта",
    aiProjectAssistant: "ИИ-помощник проекта",
    askAI: "Спросить ИИ",
    applyAISuggestions: "Применить предложения ИИ",
    aiSuggestionsApplied: "Предложения ИИ применены",
    projectUpdatedWithAI: "Проект обновлен с учетом предложений ИИ",
    privateProject: "Приватный проект",
    privateProjectDescription: "Приватные проекты видны только вам и людям, с которыми вы ими делитесь",
    private: "Приватный",
    shared: "Общедоступный",
    shareProject: "Поделиться проектом",
    currentParticipants: "Текущие участники",
    addParticipants: "Добавить участников",
    searchTeamMembers: "Поиск участников команды",
    noResultsFound: "Результаты не найдены",
    selectedParticipants: "Выбранные участники",
    share: "Поделиться",
    projectShared: "Проект доступен для совместной работы",
    projectSharedDescription: "Проект доступен для {count} участников",
    participantRemoved: "Участник удален",
    participantRemovedDescription: "Участник удален из проекта",
    totalProjects: "Всего проектов",
    completedProjects: "Завершенных проектов",
    inProgressProjects: "В процессе",
    averageCompletion: "Средняя завершенность",
    noProjects: "Нет проектов",
    createFirstProject: "Создайте свой первый проект, чтобы начать управление задачами",
    participants: "Участники",
    noParticipants: "Нет участников",
    actions: "Действия",
    completion: "Завершенность",
    createdAt: "Создан",

    // Documents
    projectDocuments: "Документы проекта",
    uploadDocument: "Загрузить документ",
    addDocumentLink: "Добавить ссылку на документ",
    viewDocuments: "Просмотр документов",
    noDocuments: "Нет документов для этого проекта",
    documentName: "Название документа",
    documentType: "Тип документа",
    documentURL: "URL документа",
    addDocument: "Добавить документ",
    addDocumentDescription: "Добавить ссылку на документ к текущему проекту",
    documentUploaded: "Документ загружен",
    documentUploadedSuccess: "Документ {name} успешно загружен",
    documentAdded: "Документ добавлен",
    documentAddedSuccess: "Документ {name} успешно добавлен",
    editDocument: "Редактировать документ",
    deleteDocument: "Удалить документ",
    deleteDocumentConfirmation: "Вы уверены, что хотите удалить документ {name}?",
    documentDeleted: "Документ удален",
    documentDeletedSuccessfully: "Документ успешно удален",
    documentUpdated: "Документ обновлен",
    documentUpdatedSuccessfully: "Документ {name} успешно обновлен",
    folders: "Папки",
    files: "Файлы",
    newFolder: "Новая папка",
    uploadFiles: "Загрузить файлы",
    uploadFilesDescription: "Загрузка файлов в текущий проект",
    dropFilesHere: "Перетащите файлы сюда",
    or: "или",
    browseFiles: "Выбрать файлы",
    selectedFiles: "Выбранные файлы",
    uploading: "Загрузка...",
    upload: "Загрузить",
    uploadComplete: "Загрузка завершена",
    filesUploadedSuccessfully: "{count} файлов успешно загружено",
    createdBy: "Создал",
    lastModified: "Последнее изменение",
    size: "Размер",
    modified: "Изменен",
    folder: "Папка",
    noFolder: "Без папки",
    tags: "Теги",
    content: "Содержимое",
    aiDocumentAssistant: "ИИ-помощник для документов",
    aiDocumentPromptPlaceholder: "например: Улучши этот документ, Сделай резюме, Предложи теги",
    aiDocumentPromptSuggestion1: "Улучшить документ",
    aiDocumentPromptSuggestion2: "Сделать резюме",
    aiDocumentPromptSuggestion3: "Предложить теги",
    currentDocument: "Текущий документ",
    documentUpdatedWithAI: "Документ обновлен с учетом предложений ИИ",
    documentAIUpdated: "Документ {name} обновлен с учетом предложений ИИ",
    openMenu: "Открыть меню",
    view: "Просмотр",
    edit: "Редактировать",
    download: "Скачать",
    totalDocuments: "Всего документов",
    totalSize: "Общий размер",
    latestUpdate: "Последнее обновление",
    documentTypes: "Типы документов",
    groupByType: "Группировать по типу",
    version: "Версия",
    status: "Статус",
    description: "Описание",

    // Admin
    adminDashboard: "Панель администратора",
    userManagement: "Управление пользователями",
    blockUser: "Заблокировать пользователя",
    unblockUser: "Разблокировать пользователя",
    deleteUser: "Удалить пользователя",
    confirmDelete: "Вы уверены, что хотите удалить этого пользователя?",
    confirmBlock: "Вы уверены, что хотите заблокировать этого пользователя?",
    confirmUnblock: "Вы уверены, что хотите разблокировать этого пользователя?",
    username: "Имя пользователя",
    email: "Email",
    active: "Активен",
    blocked: "Заблокирован",

    // Settings
    settingsSaved: "Настройки сохранены",
    generalSettingsSaved: "Общие настройки успешно сохранены",
    aiSettingsSaved: "Настройки ИИ успешно сохранены",
    emailSettingsSaved: "Настройки электронной почты успешно сохранены",
    aiModelApiKey: "API-ключ модели ИИ", // Added
    enterApiKeyPlaceholder: "Введите ваш API-ключ", // Added
    // Admin Settings specific keys
    generalSettings: "Общие настройки",
    aiSettings: "Настройки ИИ",
    emailSettings: "Настройки Email",
    generalSettingsDescription: "Настройка общих параметров сайта",
    aiSettingsDescription: "Настройка параметров ИИ-помощника",
    emailSettingsDescription: "Настройка параметров уведомлений по email",
    siteName: "Название сайта",
    siteDescription: "Описание сайта",
    allowRegistration: "Разрешить регистрацию пользователей",
    requireEmailVerification: "Требовать подтверждение Email",
    maxProjectsPerUser: "Макс. проектов на пользователя",
    maxTasksPerProject: "Макс. задач на проект",
    saveSettings: "Сохранить настройки", // Может быть специфично для контекста
    enableAI: "Включить ИИ-помощника",
    aiModel: "Модель ИИ",
    aiTemperature: "Температура ИИ",
    maxTokens: "Макс. токенов",
    customPrompt: "Пользовательский системный промпт",
    aiWarning: "Предупреждение об использовании ИИ",
    aiWarningDescription: "Информировать пользователей о возможных неточностях ИИ или затратах",
    enableEmailNotifications: "Включить уведомления по Email",
    smtpServer: "SMTP Сервер",
    smtpPort: "SMTP Порт",
    smtpUsername: "SMTP Имя пользователя",
    smtpPassword: "SMTP Пароль",
    fromEmail: "Email отправителя",
    systemSettings: "Системные настройки",
    // Admin Dashboard specific keys
    adminDashboardDescription: "Обзор статистики и управления системой",
    totalUsers: "Всего пользователей",
    activeUsers: "Активных пользователей",
    totalTasks: "Всего задач",
    completedTasks: "Завершенных задач",
    completionRate: "Уровень завершения",
    taskCompletionRate: "Уровень завершения задач",
    // Dashboard specific keys
    logoutSuccessful: "Выход выполнен успешно",
    youHaveBeenLoggedOut: "Вы успешно вышли из системы.",
    // Analytics specific keys
    taskDistribution: "Распределение задач",
    projectStatistics: "Статистика проекта",
    tasksByStatus: "Задачи по статусу",
    tasksByStatusDescription: "Распределение задач по различным статусам",
    taskCompletion: "Завершение задач со временем",
    taskCompletionDescription: "Тренд завершения задач",
    completed: "Завершено", // Специфично для аналитики
    projectsByStatus: "Проекты по статусу",
    projectsByStatusDescription: "Распределение проектов по различным статусам",
    onHold: "На удержании",
    monthlyProjects: "Создание проектов по месяцам",
    monthlyProjectsDescription: "Тренд создания новых проектов каждый месяц",

    // User Analytics specific keys
    userParticipation: "Участие пользователей",
    userActivity: "Активность пользователей",
    projectCollaboration: "Совместная работа",
    topUsersByProjects: "Топ пользователей по проектам",
    topUsersByProjectsDescription: "Пользователи с наибольшим количеством проектов",
    projectOwnershipDistribution: "Распределение владения проектами",
    projectOwnershipDistributionDescription: "Кто владеет большинством проектов",
    topUsersByTasks: "Топ пользователей по задачам",
    topUsersByTasksDescription: "Пользователи с наибольшим количеством задач",
    taskCompletionByUser: "Выполнение задач по пользователям",
    taskCompletionByUserDescription: "Кто выполняет больше всего задач",
    projectsByTeamSize: "Проекты по размеру команды",
    projectsByTeamSizeDescription: "Распределение проектов по количеству участников",
    collaborationDistribution: "Распределение совместной работы",
    collaborationDistributionDescription: "Как распределены проекты по количеству участников",
    totalProjects: "Всего проектов",
    ownedProjects: "Владеет проектами",
    participatedProjects: "Участвует в проектах",
    completedTasks: "Завершенные задачи",
    inProgressTasks: "Задачи в процессе",
    todoTasks: "Задачи в очереди",
    singleUserProjects: "Проекты с 1 участником",
    multiUserProjects: "Проекты с {count} участниками",
    // Sidebar specific keys
    aiAssistant: "ИИ-помощник", // Дублируется, обеспечить согласованность
    aiTaskTracker: "ИИ Трекер Задач",

    // Project timeline keys
    projectAge: "Возраст проекта",
    daysLeft: "Осталось дней",
    days: "дней",
    daysOverdue: "дней просрочено",
    noDueDate: "Нет срока",
    noChanges: "Без изменений",
    allParticipantsAlreadyAdded: "Все выбранные участники уже добавлены в проект",

    // AI Assistant
    error: "Ошибка",
    aiRequestFailed: "Не удалось обработать запрос к ИИ. Пожалуйста, попробуйте еще раз.",

    // Other
    all: "Все",
    spreadsheets: "Таблицы",
    useMainMenu: "Используйте боковое меню для навигации",
    pdfs: "PDF-документы",
    other: "Другое",
    noDocumentsFound: "Документы не найдены",
    noDocumentsMatchingSearch: "Нет документов, соответствующих запросу '{query}'",
    noDocumentsInProject: "В этом проекте нет документов",
    noProjectSelected: "Проект не выбран",
    selectProjectToViewDocuments: "Выберите проект для просмотра документов",
    selectProjectToManageDocuments: "Выберите проект для управления документами",
    documentsForProject: "Документы проекта: {name}",
    searchDocuments: "Поиск документов",

    // Language Toggle
    toggleLanguage: "Переключить язык",

    // Delete Confirmation Dialog
    confirmDelete: "Подтверждение удаления",
    confirmDeleteDescription: "Вы уверены, что хотите удалить этот элемент? Это действие нельзя отменить.",
    confirmDeleteEvent: "Вы уверены, что хотите удалить это событие? Это действие нельзя отменить.",

    // Event Dialog
    eventTitle: "Название события",
    eventTitlePlaceholder: "Введите название события",
    eventType: "Тип события",
    selectEventType: "Выберите тип",
    eventTask: "Задача",
    eventSubtask: "Подзадача",
    eventMeeting: "Встреча",
    eventReminder: "Напоминание",
    eventPersonal: "Личное",
    selectEventPriority: "Выберите приоритет",
    eventStatus: "Статус",
    selectEventStatus: "Выберите статус",
    eventActive: "Активно",
    eventCompleted: "Завершено",
    eventProject: "Проект",
    selectEventProject: "Выберите проект",
    noProject: "Без проекта",
    eventDescription: "Описание события",
    eventDescriptionPlaceholder: "Введите описание события",
    startDate: "Дата и время начала",
    endDate: "Дата и время окончания",

    // Admin Panel
    adminPanel: "Панель администратора",
    userManagement: "Управление пользователями",
    systemSettings: "Системные настройки",
    reminderStatus: "Статус напоминаний",

    // User Management
    searchUsers: "Поиск пользователей",
    addUser: "Добавить пользователя",
    addNewUser: "Добавить нового пользователя",
    addNewUserDescription: "Создать новую учетную запись пользователя",
    editUser: "Редактировать пользователя",
    editUserDescription: "Редактировать информацию о пользователе",
    deleteUser: "Удалить пользователя",
    blockUser: "Заблокировать пользователя",
    unblockUser: "Разблокировать пользователя",
    confirmBlock: "Вы уверены, что хотите заблокировать этого пользователя?",
    confirmUnblock: "Вы уверены, что хотите разблокировать этого пользователя?",
    selectRole: "Выберите роль",
    selectStatus: "Выберите статус",
    active: "Активный",
    blocked: "Заблокирован",
    admin: "Администратор",
    user: "Пользователь",
    noUsersFound: "Пользователи не найдены",
    actions: "Действия",

    // Notification Preferences
    emailNotifications: "Email уведомления",
    emailNotificationsDescription: "Настройка параметров email напоминаний",
    enableEmailReminders: "Включить Email напоминания",
    emailRemindersDescription: "Получать напоминания о задачах и проектах по email",
    emailAddressForReminders: "Email адрес для напоминаний",
    emailPlaceholder: "<EMAIL>",
    telegramNotifications: "Telegram уведомления",
    telegramNotificationsDescription: "Настройка интеграции с Telegram ботом для мгновенных уведомлений",
    enableTelegramReminders: "Включить Telegram напоминания",
    telegramRemindersDescription: "Получать уведомления через Telegram бота",
    telegramChatId: "Telegram Chat ID",
    telegramChatIdPlaceholder: "123456789 или @username",
    test: "Тест",
    testSuccessful: "Тест успешен",
    testSuccessfulDescription: "Тестовое сообщение успешно отправлено в Telegram!",
    testFailed: "Тест не удался",
    testFailedDescription: "Не удалось отправить тестовое сообщение. Проверьте ваш chat ID и попробуйте снова.",
    missingChatId: "Отсутствует Chat ID",
    missingChatIdDescription: "Пожалуйста, сначала введите ваш Telegram chat ID.",
    invalidTime: "Неверное время",
    invalidTimeDescription: "Пожалуйста, введите корректное положительное число.",
    duplicateTime: "Дублирующееся время",
    duplicateTimeDescription: "Это время напоминания уже существует.",
    settingsSaved: "Настройки сохранены",
    settingsSavedDescription: "Ваши настройки уведомлений успешно обновлены.",
    errorSaving: "Ошибка",
    errorSavingDescription: "Не удалось сохранить настройки уведомлений. Попробуйте снова.",
    reminderTiming: "Время напоминаний",
    reminderTimingDescription: "Настройте, когда вы хотите получать напоминания до дедлайнов",
    currentReminderTimes: "Текущие времена напоминаний",
    noReminderTimes: "Времена напоминаний не настроены",
    beforeDeadline: "до дедлайна",
    minutes: "Минуты",
    hours: "Часы",
    days: "Дни",
    notificationTypes: "Типы уведомлений",
    notificationTypesDescription: "Выберите, для каких элементов вы хотите получать напоминания",
    taskReminders: "Напоминания о задачах",
    projectReminders: "Напоминания о проектах",
    calendarReminders: "Напоминания календаря",
    overdueNotifications: "Уведомления о просрочке",
    quietHours: "Тихие часы",
    quietHoursDescription: "Установите время, когда вы не хотите получать уведомления",
    enableQuietHours: "Включить тихие часы",
    quietHoursInfo: "Уведомления будут отложены до окончания тихих часов",
    startTime: "Время начала",
    endTime: "Время окончания",
    saveChanges: "Сохранить изменения",
    saved: "Сохранено",

    // Telegram Setup Instructions
    telegramSetupTitle: "Как получить ваш Chat ID:",
    telegramSetupStep1: "1. Начните чат с @userinfobot в Telegram",
    telegramSetupStep2: "2. Отправьте любое сообщение, чтобы получить ваш Chat ID",
    telegramSetupStep3: "3. Скопируйте номер и вставьте его выше",

    // Reminder Status
    reminderScheduler: "Планировщик напоминаний",
    schedulerStatus: "Статус планировщика",
    running: "Работает",
    stopped: "Остановлен",
    checkInterval: "Интервал проверки",
    minutesShort: "мин",
    lastCheck: "Последняя проверка",
    never: "Никогда",
    startScheduler: "Запустить планировщик",
    stopScheduler: "Остановить планировщик",
    manualCheck: "Ручная проверка",
    updateInterval: "Обновить интервал",
    enabled: "Включено",
    disabled: "Отключено",
    users: "Пользователи",

    // Test Suite
    reminderSystemTestSuite: "Набор тестов системы напоминаний",
    testSystemFunctionality: "Тестирование функциональности системы напоминаний и уведомлений",
    runAllTests: "Запустить все тесты",
    runningTests: "Выполнение тестов...",
    reset: "Сброс",
    testsReset: "Тесты сброшены",
    testsResetDescription: "Результаты тестов очищены",

    // Reminder Status Messages
    schedulerStarted: "Планировщик запущен",
    schedulerStartedDescription: "Напоминания будут проверяться каждые {minutes} минут",
    schedulerStopped: "Планировщик остановлен",
    schedulerStoppedDescription: "Автоматическая проверка напоминаний отключена",
    checkCompleted: "Проверка завершена",
    checkCompletedDescription: "Напоминания проверены и отправлены",
    checkFailedDescription: "Не удалось выполнить проверку напоминаний",
    intervalUpdated: "Интервал обновлен",
    intervalUpdatedDescription: "Интервал проверки изменен на {minutes} минут",
    apiWorking: "API работает",
    apiWorkingDescription: "Тестовый запрос к API напоминаний выполнен успешно",
    apiError: "Ошибка API",
    connectionError: "Ошибка соединения",
    connectionErrorDescription: "Не удалось подключиться к API напоминаний",
    unknownError: "Неизвестная ошибка",

    // Settings Page
    theme: "Тема",
    themeDescription: "Выберите предпочитаемую цветовую тему",
    lightDefault: "Светлая (по умолчанию)",
    language: "Язык",
    languageDescription: "Выберите предпочитаемый язык",
    english: "Английский",

    // Header
    myAccount: "Мой аккаунт",
    profile: "Профиль",
    headerSettings: "Настройки", // Renamed key
    adminPanel: "Панель администратора",
    adminRole: "Администратор",
    userRole: "Пользователь",
    logout: "Выйти",
    notifications: "Уведомления",
    clearAll: "Очистить все",
    noNotifications: "Нет уведомлений",
    role: "Роль",
    name: "Имя",
    editProfile: "Редактировать профиль",
    appearance: "Внешний вид",
    theme: "Тема",
    language: "Язык",
    light: "Светлая",
    dark: "Темная",
    marketingEmails: "Маркетинговые рассылки",
    taskNotifications: "Уведомления о задачах",
    systemNotifications: "Системные уведомления",
    projectNotifications: "Уведомления о проектах",
    reminderSettings: "Настройки напоминаний",
    emailReminders: "Напоминания на почту",
    telegramReminders: "Напоминания в Telegram",
    reminders: "Напоминания",
    emailRemindersDescription: "Получать напоминания на электронную почту",
    telegramRemindersDescription: "Получать напоминания в Telegram",
    emailForReminders: "Email для напоминаний",
    telegramUsername: "Имя пользователя в Telegram",
    emailFromProfileUsed: "Используется email из вашего профиля",
    reminderSettingsSaved: "Настройки напоминаний успешно обновлены",
    saveChanges: "Сохранить изменения",
    settingsUpdatedSuccess: "Настройки успешно обновлены",

    // Project Management Specific
    validationError: "Ошибка валидации",
    projectNameRequired: "Название проекта обязательно.",
    selectProjectToUseQuickActions: "Выберите проект для использования быстрых действий.",
    error: "Ошибка",
    cannotNavigateWithoutProject: "Невозможно перейти к задачам без выбранного проекта.",
    createNewProject: "Создать новый проект",
    projectNamePlaceholder: "Введите название проекта",
    projectDescriptionPlaceholder: "Введите описание проекта",
    selectAssignee: "Выберите исполнителя",
    unassigned: "Не назначен",
    selectDate: "Выберите дату",
    create: "Создать",
    viewTasks: "Просмотр задач",
    discussProject: "Обсудить проект",
    discussTask: "Обсудить задачу",
    projectDetails: "Детали проекта",
    // status: "Статус", // Removed duplicate - already exists under // Documents
    created: "Создан", // Уже существует, проверить согласованность при необходимости
    notSet: "Не указан",
    privacy: "Приватность",
    public: "Публичный",
    todo: "К выполнению", // Уже существует, проверить согласованность при необходимости
    quickActions: "Быстрые действия",
    viewCalendar: "Просмотр календаря",
    team: "Команда",
    teamMembers: "Участники команды",
    addMember: "Добавить участника",
    improveWithAI: "Улучшить с помощью ИИ",
    currentDescription: "Текущее описание",
    noDescriptionYet: "Описание еще не добавлено.",
    generating: "Генерация...",
    generateImprovedDescription: "Сгенерировать улучшенное описание",
    aiSuggestion: "Предложение ИИ",
    aiSuggestionApplied: "Предложение ИИ применено",
    descriptionUpdatedWithAI: "Описание обновлено с помощью предложения ИИ.",
    applyAISuggestion: "Применить предложение ИИ",
    aiProjectPromptPlaceholder: "например: Спроси об улучшениях проекта, предложениях задач и т.д." // Added missing key
  }
}

// Define a type for the translation keys based on the 'en' language object
type TranslationKey = keyof typeof languages.en;

// Create the context
// Adjust the context definition to match the provider's t function signature
const TranslationContext = createContext<{
  t: (key: TranslationKey, params?: Record<string, any>) => string;
  locale: string;
  setLocale: (locale: string) => void;
}>({
  t: (key: TranslationKey, params = {}) => key, // Default implementation
  locale: "en",
  setLocale: () => {},
});

// Create the provider component
interface TranslationProviderProps {
  children: ReactNode;
}

export function TranslationProvider({ children }: TranslationProviderProps) {
  const [locale, setLocale] = useState("en")

  // Translation function
  // Use TranslationKey for the key parameter
  const t = (key: TranslationKey, params: Record<string, any> = {}) => {
    const currentLocale = locale as keyof typeof languages; // Cast locale
    // Now TS knows 'key' is a valid key for languages[currentLocale]
    const translation = languages[currentLocale]?.[key] || key;

    // Replace parameters in the translation
    if (params && Object.keys(params).length > 0) {
      return Object.keys(params).reduce((acc, paramKey) => {
        return acc.replace(`{${paramKey}}`, params[paramKey])
      }, translation)
    }

    return translation
  }

  return <TranslationContext.Provider value={{ t, locale, setLocale }}>{children}</TranslationContext.Provider>
}

// Custom hook to use the translation context
export function useTranslation() {
  const context = useContext(TranslationContext)
  if (context === undefined) {
    throw new Error("useTranslation must be used within a TranslationProvider")
  }
  return context
}
