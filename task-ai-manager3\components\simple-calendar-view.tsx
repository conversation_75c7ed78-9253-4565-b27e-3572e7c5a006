"use client"

import React, { useState, useCallback, useMemo } from 'react'
import { Calendar, momentLocalizer } from 'react-big-calendar'
import moment from 'moment'
import 'react-big-calendar/lib/css/react-big-calendar.css'
import '@/styles/calendar.css'

import { Button } from "@/components/ui/button"
import { Plus, ChevronLeft, ChevronRight, Bell, CheckCircle2, AlertCircle, Users } from 'lucide-react'
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { ru } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"

// Типы для событий и задач
type EventType = 'task' | 'subtask' | 'meeting' | 'deadline' | 'reminder';
type TaskStatus = 'todo' | 'in-progress' | 'done';

// Интерфейс для события календаря
interface CalendarEvent {
  id: number | string;
  title: string;
  start: Date;
  end: Date;
  projectId?: string;
  projectName?: string;
  taskId?: string;
  subtaskId?: string;
  type: EventType;
  description?: string;
  bgColor: string;
  isCompleted?: boolean;
  status?: TaskStatus;
  priority?: string;
  assignee?: string;
  tags?: string[];
  reminder?: boolean;
  reminderTime?: Date;
  subtasks?: any[];
}

// Функция для получения цвета события в зависимости от типа и приоритета
const getEventColor = (type: EventType, priority?: string) => {
  switch (type) {
    case 'task':
      return priority === 'high' ? '#f43f5e' : priority === 'medium' ? '#f97316' : '#22c55e';
    case 'subtask':
      return priority === 'high' ? '#ef4444' : priority === 'medium' ? '#fb923c' : '#4ade80';
    case 'meeting':
      return '#3b82f6';
    case 'deadline':
      return '#dc2626';
    case 'reminder':
      return '#a855f7';
    default:
      return '#3b82f6';
  }
};

// Демо-события для календаря
const demoEvents: CalendarEvent[] = [
  {
    id: 1,
    title: 'Встреча с командой',
    start: new Date(),
    end: new Date(new Date().setHours(new Date().getHours() + 1)),
    type: 'meeting',
    description: 'Еженедельная встреча команды разработки',
    bgColor: getEventColor('meeting'),
  },
  {
    id: 2,
    title: 'Дизайн лендинга',
    start: new Date(),
    end: new Date(),
    projectId: 'p1',
    taskId: 't1',
    type: 'task',
    description: 'Создать дизайн лендинга для нового продукта',
    bgColor: getEventColor('task', 'medium'),
    priority: 'medium',
    isCompleted: false,
  }
];

// Интерфейс для пропсов компонента CalendarView
interface SimpleCalendarViewProps {
  projects?: any[];
  tasks?: any[];
  projectFilter?: string;
  typeFilter?: string;
  showCompleted?: boolean;
}

export default function SimpleCalendarView({
  projects = [],
  tasks = [],
  projectFilter = 'all',
  typeFilter = 'all',
  showCompleted = true,
}: SimpleCalendarViewProps) {
  const { toast } = useToast()
  const [view, setView] = useState<'month' | 'week' | 'day' | 'agenda'>('month')
  const [date, setDate] = useState(new Date())

  // Преобразуем задачи в события календаря
  const events = useMemo(() => {
    // Если нет задач, используем демо-события
    if (!tasks || tasks.length === 0) {
      return demoEvents;
    }

    // Создаем события из задач
    const taskEvents = tasks.map(task => {
      if (!task || !task.id) return null;

      // Определяем дату выполнения
      let dueDate = new Date();
      if (task.dueDate) {
        try {
          dueDate = new Date(task.dueDate);
        } catch (e) {
          console.error('Error parsing date:', e);
        }
      }

      // Создаем событие для задачи
      return {
        id: `task-${task.id}`,
        title: task.title || 'Задача без названия',
        start: dueDate,
        end: dueDate,
        projectId: task.projectId || '',
        projectName: task.projectName || '',
        taskId: task.id,
        type: 'task' as EventType,
        description: task.description || '',
        bgColor: getEventColor('task', task.priority),
        isCompleted: task.status === 'done',
        status: task.status || 'todo',
        priority: task.priority || 'medium',
        assignee: task.assignee,
        tags: task.tags || [],
      };
    }).filter(Boolean);

    return taskEvents;
  }, [tasks]);

  // Фильтруем события
  const filteredEvents = useMemo(() => {
    return events.filter(event => {
      // Фильтр по проекту
      if (projectFilter !== 'all' && event.projectId !== projectFilter) {
        return false;
      }

      // Фильтр по типу события
      if (typeFilter !== 'all' && event.type !== typeFilter) {
        return false;
      }

      // Фильтр по завершенным задачам
      if (!showCompleted && event.isCompleted) {
        return false;
      }

      return true;
    });
  }, [events, projectFilter, typeFilter, showCompleted]);

  // Обработчики навигации и изменения вида
  const handleNavigate = useCallback((newDate: Date) => {
    setDate(newDate)
  }, [])

  const handleView = useCallback((newView: 'month' | 'week' | 'day' | 'agenda') => {
    setView(newView)
  }, [])

  // Компонент для отображения события в календаре
  const EventComponent = useCallback(({ event }: { event: CalendarEvent }) => {
    // Функция для получения иконки события
    const getEventIcon = () => {
      switch (event.type) {
        case 'task': return <CheckCircle2 className="h-3 w-3 mr-1" />
        case 'subtask': return <CheckCircle2 className="h-3 w-3 mr-1" />
        case 'meeting': return <Users className="h-3 w-3 mr-1" />
        case 'deadline': return <AlertCircle className="h-3 w-3 mr-1" />
        case 'reminder': return <Bell className="h-3 w-3 mr-1" />
        default: return null
      }
    }

    return (
      <div
        className={cn(
          "px-2 py-1 rounded text-white text-xs font-medium truncate flex items-center",
          event.isCompleted && "opacity-60 line-through"
        )}
        style={{ backgroundColor: event.bgColor }}
      >
        {getEventIcon()}
        <span>{event.title}</span>
        {event.reminder && <Bell className="h-3 w-3 ml-auto" />}
      </div>
    )
  }, [])

  // Кастомный тулбар для календаря
  const CustomToolbar = useCallback(({ date, onNavigate, onView, view }: any) => {
    return (
      <div className="flex flex-col sm:flex-row justify-between items-center mb-4 gap-2">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => onNavigate('PREV')}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={() => onNavigate('TODAY')}>
            Сегодня
          </Button>
          <Button variant="outline" size="sm" onClick={() => onNavigate('NEXT')}>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <div className="ml-2 text-lg font-medium">
            {format(date, 'LLLL yyyy', { locale: ru })}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex space-x-1 rounded-md bg-muted p-1">
            <Button
              variant={view === 'month' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onView('month')}
            >
              Месяц
            </Button>
            <Button
              variant={view === 'week' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onView('week')}
            >
              Неделя
            </Button>
            <Button
              variant={view === 'day' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onView('day')}
            >
              День
            </Button>
            <Button
              variant={view === 'agenda' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onView('agenda')}
            >
              Список
            </Button>
          </div>
        </div>
      </div>
    )
  }, [])

  return (
    <div className="h-full flex flex-col bg-muted p-4 rounded-lg" style={{ minHeight: '700px' }}>
      <div className="flex justify-between items-center mb-4">
        <div className="text-xl font-semibold">Календарь</div>
      </div>
      <div className="flex-grow" style={{ height: 'calc(100vh - 250px)', minHeight: '600px' }}>
        <Calendar
          localizer={momentLocalizer(moment)}
          events={filteredEvents}
          startAccessor="start"
          endAccessor="end"
          style={{ height: '100%' }}
          view={view}
          date={date}
          onNavigate={handleNavigate}
          onView={handleView as any}
          selectable={true}
          popup
          showMultiDayTimes
          components={{
            toolbar: CustomToolbar,
            event: EventComponent,
          }}
          messages={{
            today: 'Сегодня',
            previous: 'Назад',
            next: 'Вперед',
            month: 'Месяц',
            week: 'Неделя',
            day: 'День',
            agenda: 'Список',
            date: 'Дата',
            time: 'Время',
            event: 'Событие',
            noEventsInRange: 'Нет событий в выбранном диапазоне',
          }}
        />
      </div>
    </div>
  )
}
