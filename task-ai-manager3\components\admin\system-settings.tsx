"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardDescription, <PERSON><PERSON>oot<PERSON>, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useTranslation } from "@/lib/translations"
import { AlertTriangle, Save } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"

// Определяем интерфейс для настроек
interface SystemSettingsProps {
  settings?: {
    siteName?: string;
    siteDescription?: string;
    allowRegistration?: boolean;
    requireEmailVerification?: boolean;
    maxProjectsPerUser?: number;
    maxTasksPerProject?: number;
    enableAI?: boolean;
    aiModel?: string;
    aiApiKey?: string; // Добавлено поле
    aiTemperature?: number;
    maxTokens?: number;
    customPrompt?: string;
    enableEmailNotifications?: boolean;
    smtpServer?: string;
    smtpPort?: string;
    smtpUsername?: string;
    smtpPassword?: string;
    fromEmail?: string;
  };
}

export function SystemSettings({ settings = {} }: SystemSettingsProps) {
  const { t } = useTranslation()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("general")
  const [isLoading, setIsLoading] = useState({
    general: false,
    ai: false,
    email: false
  })
  const [generalSettings, setGeneralSettings] = useState({
    siteName: settings?.siteName || "AI Task Tracker",
    siteDescription: settings?.siteDescription || "AI-powered task management system",
    allowRegistration: settings?.allowRegistration !== undefined ? settings.allowRegistration : true,
    requireEmailVerification: settings?.requireEmailVerification !== undefined ? settings.requireEmailVerification : true,
    maxProjectsPerUser: settings?.maxProjectsPerUser || 10,
    maxTasksPerProject: settings?.maxTasksPerProject || 50,
  })
  const [aiSettings, setAiSettings] = useState({
    enableAI: settings?.enableAI !== undefined ? settings.enableAI : true,
    aiModel: settings?.aiModel || "gpt-4",
    aiApiKey: settings?.aiApiKey || "", // Добавлено поле для API ключа
    aiTemperature: settings?.aiTemperature !== undefined ? settings.aiTemperature : 0.7,
    maxTokens: settings?.maxTokens || 1000,
    customPrompt:
      settings?.customPrompt ||
      "You are an AI assistant for a task management application. Help users organize their tasks and projects effectively.",
  })
  const [emailSettings, setEmailSettings] = useState({
    enableEmailNotifications: settings?.enableEmailNotifications !== undefined ? settings.enableEmailNotifications : false,
    smtpServer: settings?.smtpServer || "",
    smtpPort: settings?.smtpPort || "587",
    smtpUsername: settings?.smtpUsername || "",
    smtpPassword: settings?.smtpPassword || "",
    fromEmail: settings?.fromEmail || "<EMAIL>",
  })

  const handleSaveGeneral = async () => {
    setIsLoading(prev => ({ ...prev, general: true }));
    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: 'general',
          settings: generalSettings
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save settings');
      }

      toast({
        title: t("settingsSaved"),
        description: t("generalSettingsSaved"),
      });
    } catch (error) {
      console.error('Error saving general settings:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save settings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(prev => ({ ...prev, general: false }));
    }
  }

  const handleSaveAI = async () => {
    setIsLoading(prev => ({ ...prev, ai: true }));
    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: 'ai',
          settings: aiSettings
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save AI settings');
      }

      toast({
        title: t("settingsSaved"),
        description: t("aiSettingsSaved"),
      });
    } catch (error) {
      console.error('Error saving AI settings:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save AI settings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(prev => ({ ...prev, ai: false }));
    }
  }

  const handleSaveEmail = async () => {
    setIsLoading(prev => ({ ...prev, email: true }));
    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: 'email',
          settings: emailSettings
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save email settings');
      }

      toast({
        title: t("settingsSaved"),
        description: t("emailSettingsSaved"),
      });
    } catch (error) {
      console.error('Error saving email settings:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save email settings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(prev => ({ ...prev, email: false }));
    }
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">{t("generalSettings")}</TabsTrigger>
          <TabsTrigger value="ai">{t("aiSettings")}</TabsTrigger>
          <TabsTrigger value="email">{t("emailSettings")}</TabsTrigger>
        </TabsList>
        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("generalSettings")}</CardTitle>
              <CardDescription>{t("generalSettingsDescription")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="site-name">{t("siteName")}</Label>
                <Input
                  id="site-name"
                  value={generalSettings.siteName}
                  onChange={(e) => setGeneralSettings({ ...generalSettings, siteName: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="site-description">{t("siteDescription")}</Label>
                <Textarea
                  id="site-description"
                  value={generalSettings.siteDescription}
                  onChange={(e) => setGeneralSettings({ ...generalSettings, siteDescription: e.target.value })}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="allow-registration">{t("allowRegistration")}</Label>
                <Switch
                  id="allow-registration"
                  checked={generalSettings.allowRegistration}
                  onCheckedChange={(checked) => setGeneralSettings({ ...generalSettings, allowRegistration: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="require-email-verification">{t("requireEmailVerification")}</Label>
                <Switch
                  id="require-email-verification"
                  checked={generalSettings.requireEmailVerification}
                  onCheckedChange={(checked) =>
                    setGeneralSettings({ ...generalSettings, requireEmailVerification: checked })
                  }
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="max-projects">{t("maxProjectsPerUser")}</Label>
                <Input
                  id="max-projects"
                  type="number"
                  value={generalSettings.maxProjectsPerUser}
                  onChange={(e) =>
                    setGeneralSettings({ ...generalSettings, maxProjectsPerUser: Number.parseInt(e.target.value) })
                  }
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="max-tasks">{t("maxTasksPerProject")}</Label>
                <Input
                  id="max-tasks"
                  type="number"
                  value={generalSettings.maxTasksPerProject}
                  onChange={(e) =>
                    setGeneralSettings({ ...generalSettings, maxTasksPerProject: Number.parseInt(e.target.value) })
                  }
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveGeneral} className="ml-auto" disabled={isLoading.general}>
                <Save className="mr-2 h-4 w-4" />
                {isLoading.general ? t("saving") || "Saving..." : t("saveSettings")}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="ai" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("aiSettings")}</CardTitle>
              <CardDescription>{t("aiSettingsDescription")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="enable-ai">{t("enableAI")}</Label>
                <Switch
                  id="enable-ai"
                  checked={aiSettings.enableAI}
                  onCheckedChange={(checked) => setAiSettings({ ...aiSettings, enableAI: checked })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="ai-model">{t("aiModel")}</Label>
                <Input
                  id="ai-model"
                  value={aiSettings.aiModel}
                  onChange={(e) => setAiSettings({ ...aiSettings, aiModel: e.target.value })}
                />
              </div>
              {/* Добавлено поле для API ключа */}
              <div className="grid gap-2">
                <Label htmlFor="ai-api-key">{t("aiModelApiKey")}</Label>
                <Input
                  id="ai-api-key"
                  type="password" // Скрываем ключ
                  value={aiSettings.aiApiKey}
                  onChange={(e) => setAiSettings({ ...aiSettings, aiApiKey: e.target.value })}
                  placeholder={t("enterApiKeyPlaceholder")}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="ai-temperature">
                  {t("aiTemperature")} ({aiSettings.aiTemperature})
                </Label>
                <Input
                  id="ai-temperature"
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={aiSettings.aiTemperature}
                  onChange={(e) => setAiSettings({ ...aiSettings, aiTemperature: Number.parseFloat(e.target.value) })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="max-tokens">{t("maxTokens")}</Label>
                <Input
                  id="max-tokens"
                  type="number"
                  value={aiSettings.maxTokens}
                  onChange={(e) => setAiSettings({ ...aiSettings, maxTokens: Number.parseInt(e.target.value) })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="custom-prompt">{t("customPrompt")}</Label>
                <Textarea
                  id="custom-prompt"
                  value={aiSettings.customPrompt}
                  onChange={(e) => setAiSettings({ ...aiSettings, customPrompt: e.target.value })}
                  rows={4}
                />
              </div>
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>{t("aiWarning")}</AlertTitle>
                <AlertDescription>{t("aiWarningDescription")}</AlertDescription>
              </Alert>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveAI} className="ml-auto" disabled={isLoading.ai}>
                <Save className="mr-2 h-4 w-4" />
                {isLoading.ai ? t("saving") || "Saving..." : t("saveSettings")}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("emailSettings")}</CardTitle>
              <CardDescription>{t("emailSettingsDescription")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="enable-email-notifications">{t("enableEmailNotifications")}</Label>
                <Switch
                  id="enable-email-notifications"
                  checked={emailSettings.enableEmailNotifications}
                  onCheckedChange={(checked) =>
                    setEmailSettings({ ...emailSettings, enableEmailNotifications: checked })
                  }
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="smtp-server">{t("smtpServer")}</Label>
                <Input
                  id="smtp-server"
                  value={emailSettings.smtpServer}
                  onChange={(e) => setEmailSettings({ ...emailSettings, smtpServer: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="smtp-port">{t("smtpPort")}</Label>
                <Input
                  id="smtp-port"
                  value={emailSettings.smtpPort}
                  onChange={(e) => setEmailSettings({ ...emailSettings, smtpPort: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="smtp-username">{t("smtpUsername")}</Label>
                <Input
                  id="smtp-username"
                  value={emailSettings.smtpUsername}
                  onChange={(e) => setEmailSettings({ ...emailSettings, smtpUsername: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="smtp-password">{t("smtpPassword")}</Label>
                <Input
                  id="smtp-password"
                  type="password"
                  value={emailSettings.smtpPassword}
                  onChange={(e) => setEmailSettings({ ...emailSettings, smtpPassword: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="from-email">{t("fromEmail")}</Label>
                <Input
                  id="from-email"
                  type="email"
                  value={emailSettings.fromEmail}
                  onChange={(e) => setEmailSettings({ ...emailSettings, fromEmail: e.target.value })}
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveEmail} className="ml-auto" disabled={isLoading.email}>
                <Save className="mr-2 h-4 w-4" />
                {isLoading.email ? t("saving") || "Saving..." : t("saveSettings")}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
