"use client"

import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { useTranslation } from "@/lib/translations"

interface DeleteConfirmDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onDelete: () => void;
}

export default function DeleteConfirmDialog({
  isOpen,
  onOpenChange,
  onDelete
}: DeleteConfirmDialogProps) {
  const { t } = useTranslation()

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("confirmDelete")}</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          {t("confirmDeleteEvent")}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("cancel")}
          </Button>
          <Button variant="destructive" onClick={onDelete}>
            {t("delete")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
